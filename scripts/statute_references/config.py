import os
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory (2 levels up from this file)
PROJECT_ROOT = Path(__file__).parent.parent.parent

# Load environment variables from .env files
# Priority: .env.local (if exists) > .env
load_dotenv(PROJECT_ROOT / '.env.local')  # Local overrides
load_dotenv(PROJECT_ROOT / '.env')        # Default values

def get_mongo_uri() -> str:
    """
    Get MongoDB URI from environment variables with fallback to default.
    Priority:
    1. MONGODB_URI environment variable
    2. Constructed from individual components (host, port, db)
    3. Default local development URI
    """
    # Direct URI takes precedence
    if uri := os.getenv('MONGODB_URI'):
        return uri

    # Construct from components if available
    host = os.getenv('MONGODB_HOST', 'localhost')
    port = os.getenv('MONGODB_PORT', '27017')
    db = os.getenv('MONGODB_DB', 'statutes_db')
    user = os.getenv('MONGODB_USER')
    password = os.getenv('MONGODB_PASSWORD')

    if user and password:
        return f"mongodb://{user}:{password}@{host}:{port}/{db}"

    return f"mongodb://{host}:{port}/{db}"
