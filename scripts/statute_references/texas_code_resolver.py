from typing import Dict, List, Optional, Any
from .patterns import ReferencePatterns
from .resolver import BaseReferenceResolver
from .db import MongoDB
import re
import logging

class TexasCodeResolver(BaseReferenceResolver):
    def __init__(self, node: Dict[str, Any], db: MongoDB):
        super().__init__(node, db)
        self.initialize_patterns()

    # ================================
    # find_node methods
    # ================================

    async def find_section_node(self, section_number: str, chapter_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Find a section node by its number and optional chapter"""
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"

        if chapter_number:
            # If chapter is specified, look for section within that chapter
            end_path = f"/chapter/{chapter_number}/section/{section_number}"
            return await self.db.find_node_by_start_end_path(start_path, end_path)

        # If no chapter specified, first try direct section path
        end_path = f"/section/{section_number}"
        node = await self.db.find_node_by_start_end_path(start_path, end_path)
        if node:
            return node

        # If not found, try searching in current chapter context
        chapter_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'chapter'), None)
        if chapter_node:
            end_path = f"/chapter/{chapter_node['id']}/section/{section_number}"
            return await self.db.find_node_by_start_end_path(start_path, end_path)

        return None

    async def find_subsection_node(self, section_number: str, subsection: str, chapter_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Find a subsection node by its section number, subsection, and optional chapter"""
        section_node = await self.find_section_node(section_number, chapter_number)
        if not section_node:
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{section_number}{subsection}"
        if chapter_number:
            end_path = f"/chapter/{chapter_number}{end_path}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_chapter_node(self, chapter_number: str) -> Optional[Dict[str, Any]]:
        """Find a chapter node by its number"""
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/chapter/{chapter_number}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_subchapter_node(self, subchapter_letter: str, chapter_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Find a subchapter node by its letter and optional chapter"""
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        if chapter_number:
            end_path = f"/chapter/{chapter_number}/subchapter/{subchapter_letter}"
            return await self.db.find_node_by_start_end_path(start_path, end_path)

        # If no chapter specified, search in current chapter
        chapter_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'chapter'), None)
        if chapter_node:
            end_path = f"/chapter/{chapter_node['id']}/subchapter/{subchapter_letter}"
            return await self.db.find_node_by_start_end_path(start_path, end_path)

        return None

    async def find_title_node(self, title_number: str) -> Optional[Dict[str, Any]]:
        """Find a title node by its number"""
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/title/{title_number}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_subsection_in_current_section(self, subsection: str) -> Optional[Dict[str, Any]]:
        """Find a subsection in the current section"""
        logging.debug(f"Finding subsection '{subsection}' in current section")
        logging.debug(f"Node hierarchy: {[n.get('type') + ':' + n.get('id', '') for n in self.node.get('hierarchy', [])]}")

        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        if not context_node:
            logging.debug(f"No section found in hierarchy for subsection {subsection}")
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{context_node['id']}{subsection}"
        logging.debug(f"Looking for subsection with start_path: {start_path}, end_path: {end_path}")

        result = await self.db.find_node_by_start_end_path(start_path, end_path)
        logging.debug(f"Subsection search result: {'Found' if result else 'Not found'}")
        if result:
            logging.debug(f"Found node: {result.get('nodeId')}")
        return result

    async def find_subdivision_in_current_subsection(self, subdivision: str) -> Optional[Dict[str, Any]]:
        """Find a subdivision in the current subsection"""
        logging.debug(f"Finding subdivision '{subdivision}' in current subsection")
        logging.debug(f"Node hierarchy: {[n.get('type') + ':' + n.get('id', '') for n in self.node.get('hierarchy', [])]}")

        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'subsection'), None)
        logging.debug(f"Looking for subdivision {subdivision} in context: {context_node['id'] if context_node else 'None'}")

        if not context_node:
            # If we can't find a subsection in the hierarchy, check if the node itself is a subsection
            if hasattr(self.node, 'isSubsection') and self.node.isSubsection():
                logging.debug("Current node is a subsection")
                # Get the section ID from the node ID
                section_id = None
                for n in self.node.get('hierarchy', []):
                    if n.get('type') == 'section':
                        section_id = n.get('id')
                        break

                if section_id:  # Removed redundant context_node check since we know it's None
                    subsection_id = self.node.get('id')  # Use the current node's ID instead of context_node
                    start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
                    end_path = f"/section/{section_id}{subsection_id}{subdivision}"
                    logging.debug(f"Looking for subdivision with start_path: {start_path}, end_path: {end_path}")
                    result = await self.db.find_node_by_start_end_path(start_path, end_path)
                    logging.debug(f"Subdivision search result: {'Found' if result else 'Not found'}")
                    if result:
                        logging.debug(f"Found node: {result.get('nodeId')}")
                    return result
            logging.debug("No subsection context found")
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        section_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        if not section_node:
            logging.debug("No section found in hierarchy")
            return None

        end_path = f"/section/{section_node['id']}{context_node['id']}{subdivision}"
        logging.debug(f"Looking for subdivision with start_path: {start_path}, end_path: {end_path}")
        result = await self.db.find_node_by_start_end_path(start_path, end_path)
        logging.debug(f"Subdivision search result: {'Found' if result else 'Not found'}")
        if result:
            logging.debug(f"Found node: {result.get('nodeId')}")
        return result

    async def find_subdivision_in_subsection(self, subdivision: str, subsection: str) -> Optional[Dict[str, Any]]:
        """Find a subdivision in a specific subsection"""
        logging.debug("\nDEBUG find_subdivision_in_subsection:")
        logging.debug(f"subdivision: '{subdivision}'")
        logging.debug(f"subsection: '{subsection}'")

        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        logging.debug(f"context_node found: {'Yes' if context_node else 'No'}")
        if context_node:
            logging.debug(f"context_node id: {context_node.get('id')}")
            logging.debug(f"context_node type: {context_node.get('type')}")

        if not context_node:
            logging.debug("No section found in hierarchy")
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{context_node['id']}{subsection}{subdivision}"

        logging.debug(f"start_path: '{start_path}'")
        logging.debug(f"end_path: '{end_path}'")

        result = await self.db.find_node_by_start_end_path(start_path, end_path)
        logging.debug(f"result found: {'Yes' if result else 'No'}")
        if result:
            logging.debug(f"result nodeId: {result.get('nodeId')}")

        return result

    async def find_subdivision_node(self, section: str, subsection: str, subdivision: str) -> Optional[Dict[str, Any]]:
        """Find a subdivision node in a specific section and subsection"""
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{section}{subsection}{subdivision}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_constitution_node(self, section_number: str, article_number: str) -> Optional[Dict[str, Any]]:
        """Find a node in the Texas Constitution by article and section number"""
        logging.debug("\nDEBUG find_constitution_node:")
        logging.debug(f"section_number: '{section_number}'")
        logging.debug(f"article_number: '{article_number}'")
        start_path = f"/collection/tx/code/cn/article/{article_number}"
        end_path = f"/section/{section_number}"
        logging.debug(f"start_path: '{start_path}'")
        logging.debug(f"end_path: '{end_path}'")
        result = await self.db.find_node_by_start_end_path(start_path, end_path)
        logging.debug(f"result: {result}")
        return result

    # ================================
    # build_link methods
    # ================================

    async def build_section_link(self, matched_text: str, section: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section reference"""
        node = await self.find_section_node(section)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': node['nodeId'] if node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if node else 'unresolved'
            }]
        }

    async def build_section_list_link(self, matched_text: str, first_ref: str, first_section: str, rest_of_refs: str, last_section: str, offset: int) -> Dict[str, Any]:
        """Build a link for a list of section references"""
        logging.debug("\nDEBUG build_section_list_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"first_ref: '{first_ref}'")
        logging.debug(f"first_section: '{first_section}'")
        logging.debug(f"rest_of_refs: '{rest_of_refs}'")
        logging.debug(f"last_section: '{last_section}'")
        logging.debug(f"offset: {offset}")

        matches = []
        current_offset = offset

        # Add first section
        node1 = await self.find_section_node(first_section)
        matches.append({
            'text': first_ref,
            'target': node1['nodeId'] if node1 else None,
            'startOffset': current_offset,
            'endOffset': current_offset + len(first_ref),
            'status': 'resolved' if node1 else 'unresolved'
        })

        # Process additional sections
        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.SECTION_NUMBER.finditer(rest_of_refs):
                section = match.group(0)
                section_pos = offset + rest_of_refs_pos + match.start()
                node = await self.find_section_node(section)
                matches.append({
                    'text': section,
                    'target': node['nodeId'] if node else None,
                    'startOffset': section_pos,
                    'endOffset': section_pos + len(section),
                    'status': 'resolved' if node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_section_chapter_link(self, matched_text: str, section: str, chapter: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section and chapter reference"""
        node = await self.find_section_node(section, chapter)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': node['nodeId'] if node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if node else 'unresolved'
            }]
        }

    async def build_section_subsection_link(self, matched_text: str, section: str, subsection: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section with subsection reference"""
        node = await self.find_subsection_node(section, subsection)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': node['nodeId'] if node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if node else 'unresolved'
            }]
        }

    async def build_subsection_list_link(self, matched_text: str, first_ref: str, first_subsection: str, rest_of_refs: str, last_subsection: str, offset: int) -> Dict[str, Any]:
        """Build a link for a list of subsection references"""
        matches = []
        current_offset = offset

        # Add first subsection
        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        if context_node:
            node1 = await self.find_subsection_node(context_node['id'], first_subsection)
            matches.append({
                'text': first_ref,
                'target': node1['nodeId'] if node1 else None,
                'startOffset': current_offset,
                'endOffset': current_offset + len(first_ref),
                'status': 'resolved' if node1 else 'unresolved'
            })

        # Process additional subsections
        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.SUBSECTION.finditer(rest_of_refs):
                subsection = match.group(0)
                subsection_pos = offset + rest_of_refs_pos + match.start()
                if context_node:
                    node = await self.find_subsection_node(context_node['id'], subsection)
                    matches.append({
                        'text': subsection,
                        'target': node['nodeId'] if node else None,
                        'startOffset': subsection_pos,
                        'endOffset': subsection_pos + len(subsection),
                        'status': 'resolved' if node else 'unresolved'
                    })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_title_section_link(self, matched_text: str, title: str, section: str, offset: int) -> Dict[str, Any]:
        """Build a link for a title and section reference"""
        node = await self.find_section_node(section)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': node['nodeId'] if node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if node else 'unresolved'
            }]
        }

    async def build_chapter_section_link(self, matched_text: str, chapter: str, section: str, offset: int) -> Dict[str, Any]:
        """Build a link for a chapter and section reference"""
        node = await self.find_section_node(section, chapter)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': node['nodeId'] if node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if node else 'unresolved'
            }]
        }

    async def build_external_chapter_link(self, matched_text: str, chapter: str, code_name: str, offset: int) -> Dict[str, Any]:
        """Build a link for an external chapter reference (e.g., 'Chapter 311, Government Code')"""
        code = self.reference_patterns.get_code_from_name(code_name)
        if not code:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        # Construct the path to the external chapter
        start_path = f"/collection/{self.node['collection']}/code/{code}"
        end_path = f"/chapter/{chapter}"
        target_node = await self.db.find_node_by_start_end_path(start_path, end_path)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_node else 'unresolved'
            }]
        }

    async def build_external_section_link(self, matched_text: str, section: str, code_name: str, offset: int) -> Dict[str, Any]:
        """Build a link for an external section reference (e.g., 'Section 442.001, Government Code')"""
        code = self.reference_patterns.get_code_from_name(code_name)
        if not code:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        # Try finding the section directly
        start_path = f"/collection/{self.node['collection']}/code/{code}"
        end_path = f"/section/{section}"
        target_node = await self.db.find_node_by_start_end_path(start_path, end_path)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_node else 'unresolved'
            }]
        }

    async def build_external_subchapter_chapter_link(self, matched_text: str, subchapter: str, chapter: str, code_name: str, offset: int) -> Dict[str, Any]:
        """Build a link for an external subchapter and chapter reference (e.g., 'Subchapter C, Chapter 23, Tax Code')"""
        code = self.reference_patterns.get_code_from_name(code_name)
        if not code:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        # Construct the path to the external subchapter and chapter
        start_path = f"/collection/{self.node['collection']}/code/{code}"
        end_path = f"/chapter/{chapter}/subchapter/{subchapter}"
        target_node = await self.db.find_node_by_start_end_path(start_path, end_path)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_node else 'unresolved'
            }]
        }

    async def build_subchapter_chapter_link(self, matched_text: str, subchapter: str, chapter: str, offset: int) -> Dict[str, Any]:
        """Build a link for a subchapter and chapter reference (e.g., 'Subchapter A, Chapter 5')"""
        # Find the subchapter node using the chapter context
        target_node = await self.find_subchapter_node(subchapter, chapter)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_node else 'unresolved'
            }]
        }

    async def build_subsection_link(self, matched_text: str, subsection: str, offset: int) -> Dict[str, Any]:
        """Build a link for a subsection reference (e.g., 'Subsection (a)')"""
        # Get the current section context from hierarchy
        section_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        
        target_nodeId = None
        if section_node:
            # Construct target nodeId by appending subsection to section's nodeId
            section_nodeId = section_node.get('nodeId', '')
            if section_nodeId:
                target_nodeId = f"{section_nodeId}{subsection}"

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_nodeId,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_nodeId else 'unresolved'
            }]
        }

    async def build_subdivision_link(self, matched_text: str, subdivision: str, offset: int) -> Dict[str, Any]:
        """Build a link for a subdivision reference (e.g., 'Subdivision (1)')"""
        # Get the current subsection context from hierarchy
        subsection_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'subsection'), None)
        
        target_nodeId = None
        if subsection_node:
            # Construct target nodeId by appending subdivision to subsection's nodeId
            subsection_nodeId = subsection_node.get('nodeId', '')
            if subsection_nodeId:
                target_nodeId = f"{subsection_nodeId}{subdivision}"
        else:
            # If no subsection in hierarchy, check if current node is a subsection
            if self.node.get('type') == 'subsection':
                current_nodeId = self.node.get('nodeId', '')
                if current_nodeId:
                    target_nodeId = f"{current_nodeId}{subdivision}"

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_nodeId,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_nodeId else 'unresolved'
            }]
        }

    async def build_subsection_subdivision_link(self, matched_text: str, subsection: str, subdivision: str, offset: int) -> Dict[str, Any]:
        """Build a link for a subsection with subdivision reference (e.g., 'Subsection (c)(1)')"""
        # Get the current section context from hierarchy
        section_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        
        target_nodeId = None
        if section_node:
            # Construct target nodeId by appending subsection and subdivision to section's nodeId
            section_nodeId = section_node.get('nodeId', '')
            if section_nodeId:
                target_nodeId = f"{section_nodeId}{subsection}{subdivision}"

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_nodeId,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_nodeId else 'unresolved'
            }]
        }

    async def build_title_link(self, matched_text: str, title: str, offset: int) -> Dict[str, Any]:
        """Build a link for a title reference (e.g., 'Title 3')"""
        # Find the title node
        target_node = await self.find_title_node(title)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_node else 'unresolved'
            }]
        }

    async def build_chapter_list_link(self, matched_text: str, first_ref: str, first_chapter: str, rest_of_refs: str, last_chapter: str, offset: int) -> Dict[str, Any]:
        """Build a link for a list of chapter references (e.g., 'Chapters 28, 30, and 32')"""
        logging.debug("\nDEBUG build_chapter_list_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"first_ref: '{first_ref}'")
        logging.debug(f"first_chapter: '{first_chapter}'")
        logging.debug(f"rest_of_refs: '{rest_of_refs}'")
        logging.debug(f"last_chapter: '{last_chapter}'")
        logging.debug(f"offset: {offset}")

        matches = []
        current_offset = offset

        # Add first chapter
        node1 = await self.find_chapter_node(first_chapter)
        matches.append({
            'text': first_ref,
            'target': node1['nodeId'] if node1 else None,
            'startOffset': current_offset,
            'endOffset': current_offset + len(first_ref),
            'status': 'resolved' if node1 else 'unresolved'
        })

        # Process additional chapters
        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.CHAPTER_NUMBER.finditer(rest_of_refs):
                chapter = match.group(0)
                chapter_pos = offset + rest_of_refs_pos + match.start()
                node = await self.find_chapter_node(chapter)
                matches.append({
                    'text': chapter,
                    'target': node['nodeId'] if node else None,
                    'startOffset': chapter_pos,
                    'endOffset': chapter_pos + len(chapter),
                    'status': 'resolved' if node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_section_subsection_subdivision_link(self, matched_text: str, section: str, subsection: str, subdivision: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section with subsection and subdivision reference (e.g., 'Section 61.71(a)(14)')"""
        # First find the section node
        section_node = await self.find_section_node(section)
        if not section_node:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        # Construct the path to the subsection with subdivision
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{section}{subsection}{subdivision}"
        target_node = await self.db.find_node_by_start_end_path(start_path, end_path)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': 'resolved' if target_node else 'unresolved'
            }]
        }

    async def build_section_subsection_list_link(self, matched_text: str, full_first_part: str, section_number: str, first_subsection: str, rest_of_text: str, last_subsection: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section with a list of subsections (e.g., 'Sections 11.39(a) and (b)')"""
        logging.debug("\nDEBUG build_section_subsection_list_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"full_first_part: '{full_first_part}'")
        logging.debug(f"section_number: '{section_number}'")
        logging.debug(f"first_subsection: '{first_subsection}'")
        logging.debug(f"rest_of_text: '{rest_of_text}'")
        logging.debug(f"last_subsection: '{last_subsection}'")
        logging.debug(f"offset: {offset}")

        matches = []

        # Add first subsection
        node1 = await self.find_subsection_node(section_number, first_subsection)
        matches.append({
            'text': full_first_part,
            'target': node1['nodeId'] if node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(full_first_part),
            'status': 'resolved' if node1 else 'unresolved'
        })

        # Process additional subsections
        # Find all subsection patterns in the rest_of_text
        for match in self.reference_patterns.SUBSECTION.finditer(rest_of_text):
            subsection = match.group(0)
            subsection_pos = offset + len(full_first_part) + match.start()
            node = await self.find_subsection_node(section_number, subsection)
            matches.append({
                'text': subsection,
                'target': node['nodeId'] if node else None,
                'startOffset': subsection_pos,
                'endOffset': subsection_pos + len(subsection),
                'status': 'resolved' if node else 'unresolved'
            })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_section_subsection_subdivision_list_link(self, matched_text: str, full_first_part: str, section_number: str, subsection: str, first_subdivision: str, rest_of_text: str, last_subdivision: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section with a subsection and a list of subdivisions
        (e.g., 'Section 501.035(b)(7), (8), or (9)')"""
        logging.debug("\nDEBUG build_section_subsection_subdivision_list_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"full_first_part: '{full_first_part}'")
        logging.debug(f"section_number: '{section_number}'")
        logging.debug(f"subsection: '{subsection}'")
        logging.debug(f"first_subdivision: '{first_subdivision}'")
        logging.debug(f"rest_of_text: '{rest_of_text}'")
        logging.debug(f"last_subdivision: '{last_subdivision}'")
        logging.debug(f"offset: {offset}")

        matches = []

        # Add first subdivision
        node1 = await self.find_subdivision_node(section_number, subsection, first_subdivision)
        matches.append({
            'text': full_first_part,
            'target': node1['nodeId'] if node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(full_first_part),
            'status': 'resolved' if node1 else 'unresolved'
        })

        # Process additional subdivisions
        # Find all subdivision patterns in the rest_of_text
        for match in self.reference_patterns.SUBDIVISION.finditer(rest_of_text):
            subdivision = match.group(0)
            subdivision_pos = offset + len(full_first_part) + match.start()
            node = await self.find_subdivision_node(section_number, subsection, subdivision)
            matches.append({
                'text': subdivision,
                'target': node['nodeId'] if node else None,
                'startOffset': subdivision_pos,
                'endOffset': subdivision_pos + len(subdivision),
                'status': 'resolved' if node else 'unresolved'
            })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_external_section_subsection_subdivision_list_link(self, matched_text: str, full_first_part: str, section_number: str, subsection: str, first_subdivision: str, rest_of_text: str, last_subdivision: str, code_name: str, offset: int) -> Dict[str, Any]:
        """Build a link for a section with a subsection, a list of subdivisions, and an external code reference
        (e.g., 'Section 501.035(b)(7), (8), or (9), Election Code')"""
        logging.debug("\nDEBUG build_external_section_subsection_subdivision_list_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"full_first_part: '{full_first_part}'")
        logging.debug(f"section_number: '{section_number}'")
        logging.debug(f"subsection: '{subsection}'")
        logging.debug(f"first_subdivision: '{first_subdivision}'")
        logging.debug(f"rest_of_text: '{rest_of_text}'")
        logging.debug(f"last_subdivision: '{last_subdivision}'")
        logging.debug(f"code_name: '{code_name}'")
        logging.debug(f"offset: {offset}")

        matches = []

        # Get the code abbreviation from the code name
        code = self.reference_patterns.get_code_from_name(code_name)
        if not code:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        # Extract the part of rest_of_text before the code name
        # This contains the additional subdivisions
        subdivisions_text = rest_of_text
        if code_name in rest_of_text:
            subdivisions_text = rest_of_text[:rest_of_text.index(code_name)].strip()

        # Add first subdivision
        # Construct the path to the external section
        start_path = f"/collection/{self.node['collection']}/code/{code}"
        end_path = f"/section/{section_number}{subsection}{first_subdivision}"
        node1 = await self.db.find_node_by_start_end_path(start_path, end_path)

        matches.append({
            'text': full_first_part,
            'target': node1['nodeId'] if node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(full_first_part),
            'status': 'resolved' if node1 else 'unresolved'
        })

        # Process additional subdivisions
        # Find all subdivision patterns in the subdivisions_text
        for match in self.reference_patterns.SUBDIVISION.finditer(subdivisions_text):
            subdivision = match.group(0)
            subdivision_pos = offset + len(full_first_part) + match.start()

            # Construct the path to the external section with this subdivision
            end_path = f"/section/{section_number}{subsection}{subdivision}"
            node = await self.db.find_node_by_start_end_path(start_path, end_path)

            matches.append({
                'text': subdivision,
                'target': node['nodeId'] if node else None,
                'startOffset': subdivision_pos,
                'endOffset': subdivision_pos + len(subdivision),
                'status': 'resolved' if node else 'unresolved'
            })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_mixed_section_list_link(self, matched_text: str, first_ref: str, first_section: str, rest_of_refs: str, last_section: str, offset: int) -> Dict[str, Any]:
        """Build a link for a mixed list of section references with various formats

        Example: "Section 11.61(b)(14), 22.12, 28.11, 32.17(a)(2), 32.17(a)(3), 61.71(a)(5), 61.71(a)(6), 61.74(a)(14), 69.13, 71.09, 101.04, 101.63, 104.01(a)(4), 106.03, 106.06, or 106.15"
        """
        logging.debug("\nDEBUG build_mixed_section_list_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"first_ref: '{first_ref}'")
        logging.debug(f"first_section: '{first_section}'")
        logging.debug(f"rest_of_refs: '{rest_of_refs}'")
        logging.debug(f"last_section: '{last_section}'")
        logging.debug(f"offset: {offset}")

        matches = []
        current_offset = offset

        # Process the first section reference
        # It could be a simple section number or a section with subsections/subdivisions
        section_parts = re.match(r'(\d+(?:\.\d+)*)(?:(\([a-z](?:-\d+)?\))(?:(\(\d+\)))?)?', first_section)
        if section_parts:
            section_number = section_parts.group(1)
            subsection = section_parts.group(2) or ""
            subdivision = section_parts.group(3) or ""

            if subsection and subdivision:
                # Section with subsection and subdivision
                node = await self.find_subdivision_node(section_number, subsection, subdivision)
            elif subsection:
                # Section with subsection
                node = await self.find_subsection_node(section_number, subsection)
            else:
                # Simple section
                node = await self.find_section_node(section_number)

            matches.append({
                'text': first_ref,
                'target': node['nodeId'] if node else None,
                'startOffset': current_offset,
                'endOffset': current_offset + len(first_ref),
                'status': 'resolved' if node else 'unresolved'
            })

        # Process additional section references
        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)

            # Use a more comprehensive pattern to match various section formats
            section_pattern = re.compile(r'(\d+(?:\.\d+)*)(?:(\([a-z](?:-\d+)?\))(?:(\(\d+\)))?)?')

            for match in section_pattern.finditer(rest_of_refs):
                section_number = match.group(1)
                subsection = match.group(2) or ""
                subdivision = match.group(3) or ""

                section_text = match.group(0)
                section_pos = offset + rest_of_refs_pos + match.start()

                if subsection and subdivision:
                    # Section with subsection and subdivision
                    node = await self.find_subdivision_node(section_number, subsection, subdivision)
                elif subsection:
                    # Section with subsection
                    node = await self.find_subsection_node(section_number, subsection)
                else:
                    # Simple section
                    node = await self.find_section_node(section_number)

                matches.append({
                    'text': section_text,
                    'target': node['nodeId'] if node else None,
                    'startOffset': section_pos,
                    'endOffset': section_pos + len(section_text),
                    'status': 'resolved' if node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_subdivision_list_link(self, matched_text: str, first_ref: str, first_subdivision: str, rest_of_refs: str, last_subdivision: str, offset: int) -> Dict[str, Any]:
        """Build a link for a list of subdivision references (e.g., 'Subdivisions (1) and (2)')"""
        matches = []
        current_offset = offset

        # Add first subdivision
        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'subsection'), None)
        if context_node:
            section_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
            if section_node:
                node1 = await self.find_subdivision_node(section_node['id'], context_node['id'], first_subdivision)
                matches.append({
                    'text': first_ref,
                    'target': node1['nodeId'] if node1 else None,
                    'startOffset': current_offset,
                    'endOffset': current_offset + len(first_ref),
                    'status': 'resolved' if node1 else 'unresolved'
                })

        # Process additional subdivisions
        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.SUBDIVISION.finditer(rest_of_refs):
                subdivision = match.group(0)
                subdivision_pos = offset + rest_of_refs_pos + match.start()
                if context_node and section_node:
                    node = await self.find_subdivision_node(section_node['id'], context_node['id'], subdivision)
                    matches.append({
                        'text': subdivision,
                        'target': node['nodeId'] if node else None,
                        'startOffset': subdivision_pos,
                        'endOffset': subdivision_pos + len(subdivision),
                        'status': 'resolved' if node else 'unresolved'
                    })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_section_article_constitution_link(self, matched_text: str, section: str, article: str, offset: int) -> Dict[str, Any]:
        """Build a link for a constitution reference like 'Section 52-a, Article III, Texas Constitution'"""
        logging.debug("\nDEBUG build_section_article_constitution_link match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"section: '{section}'")
        logging.debug(f"article: '{article}'")
        logging.debug(f"offset: {offset}")

        # Convert roman numeral article number to integer
        article_num = self.reference_patterns.get_article_number(article)
        logging.debug(f"converted article_num: {article_num}")

        # Find the referenced node in the constitution
        target_node = await self.find_constitution_node(section, article_num)
        logging.debug(f"target_node: {target_node}")
        logging.debug(f"target_node['nodeId']: {target_node['nodeId']}")
        logging.debug(f"target_node['type']: {target_node['type']}")

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_federal_usc_section_link(self, matched_text: str, title: str, section: str, offset: int) -> dict:
        """Build a link for a federal USC section reference (e.g., '1 U.S.C. 101')"""
        logging.debug("\nDEBUG build_federal_usc_section_link:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"title: '{title}'")
        logging.debug(f"section: '{section}'")
        logging.debug(f"offset: {offset}")

        # Construct the URL to the federal statute
        # For anything that is not a section, the URL is:
        # https://uscode.house.gov/browse/prelim@title{title}/chapter{chapter}/subchapter{subchapter}&edition=prelim
        # For a section, the URL is:
        # https://uscode.house.gov/view.xhtml?req=granuleid:USC-prelim-title{title}-section{section}&num=0&edition=prelim
        # For a section with a subpart, the URL is:
        # https://uscode.house.gov/view.xhtml?req=granuleid:USC-prelim-title{title}-section{section}-subpart{subpart}&num=0&edition=prelim
        url = f"https://uscode.house.gov/view.xhtml?req=granuleid:USC-prelim-title{title}-section{section}&num=0&edition=prelim"
        logging.debug(f"url: '{url}'")

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': url,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if url else 'unresolved'
            }]
        }

    # ================================
    # initialize_patterns
    # ================================

    def initialize_patterns(self):
        """Initialize all the reference patterns"""
        patterns = [
            # External section reference (most specific)
            # Example: "Section 442.001, Government Code"
            # Example: "Section 442.001 of the Government Code"
            {
                'name': 'external_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER,
                    self.reference_patterns.COMMA_OF_THE_THIS,
                    self.reference_patterns.CODE_NAME
                ),
                'build_link': self.build_external_section_link
            },
            # External chapter reference
            # Example: "Chapter 311, Government Code"
            # Example: "Chapter 311 of the Government Code"
            {
                'name': 'external_chapter',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.CHAPTER_PREFIX,
                    self.reference_patterns.CHAPTER_NUMBER,
                    self.reference_patterns.COMMA_OF_THE_THIS,
                    self.reference_patterns.CODE_NAME
                ),
                'build_link': self.build_external_chapter_link
            },
            # External Subchapter with chapter reference
            # Example: "Subchapter C, Chapter 23, Tax Code"
            # Example: "Subchapter C, Chapter 23 of the Tax Code"
            {
                'name': 'external_subchapter_chapter',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBCHAPTER_PREFIX,
                    self.reference_patterns.SUBCHAPTER_LETTER,
                    self.reference_patterns.COMMA_OF_THE_THIS,
                    self.reference_patterns.CHAPTER_PREFIX,
                    self.reference_patterns.CHAPTER_NUMBER,
                    self.reference_patterns.COMMA_OF_THE_THIS,
                    self.reference_patterns.CODE_NAME
                ),
                'build_link': self.build_external_subchapter_chapter_link
            },
            # Subchapter with chapter reference
            # Example: "Subchapter A, Chapter 5"
            # Example: "Subchapter A of Chapter 5"
            {
                'name': 'subchapter_chapter',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBCHAPTER_PREFIX,
                    self.reference_patterns.SUBCHAPTER_LETTER,
                    self.reference_patterns.COMMA_OF_THE_THIS,
                    self.reference_patterns.CHAPTER_PREFIX,
                    self.reference_patterns.CHAPTER_NUMBER
                ),
                'build_link': self.build_subchapter_chapter_link
            },
            # List of chapters
            # Example: "Chapters 28, 30, and 32"
            # Example: "Chapter 28, 30, or 32"
            {
                'name': 'chapter_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.CHAPTER_PREFIX,
                    self.reference_patterns.CHAPTER_NUMBER
                ),
                'build_link': self.build_chapter_list_link
            },
            # Subsection with subdivision reference
            # Example: "Subsection (c)(1)"
            {
                'name': 'subsection_subdivision',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION,
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_subsection_subdivision_link
            },
            # Single subsection reference
            # Example: "Subsection (a)"
            {
                'name': 'subsection',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_subsection_link,
                'debug': True
            },
            # Single subdivision reference
            # Example: "Subdivision (1)"
            {
                'name': 'subdivision',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBDIVISION_PREFIX,
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_subdivision_link
            },
            # Title reference
            # Example: "Title 3"
            {
                'name': 'title',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.TITLE_PREFIX,
                    self.reference_patterns.TITLE_NUMBER
                ),
                'build_link': self.build_title_link
            },
            # Section with chapter reference
            # Example: "Section 1-1, Chapter 1"
            {
                'name': 'section_chapter',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER,
                    self.reference_patterns.SECTION_CONNECTOR,
                    self.reference_patterns.CHAPTER_PREFIX,
                    self.reference_patterns.CHAPTER_NUMBER
                ),
                'build_link': self.build_section_chapter_link
            },
            # Section with subsection
            # Example: "Section 1-1(a)"
            {
                'name': 'section_subsection',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER,
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_section_subsection_link
            },
            # Section with subsection and subdivision
            # Example: "Section 61.71(a)(14)"
            {
                'name': 'section_subsection_subdivision',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER,
                    self.reference_patterns.SUBSECTION,
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_section_subsection_subdivision_link
            },
            # Section with list of subsections
            # Example: "Sections 11.39(a) and (b)"
            # Example: "Sections 11.39(a), (b), and (c)"
            # Example: "Sections 11.39(a) through (c)"
            {
                'name': 'section_subsection_list',
                'pattern': self.reference_patterns.SHARED_BASE_LIST(
                    self.reference_patterns.COMBINE(
                        self.reference_patterns.SECTION_PREFIX,
                        self.reference_patterns.SECTION_NUMBER,
                        self.reference_patterns.SUBSECTION
                    ),
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_section_subsection_list_link,
            },
            # Section with subsection and list of subdivisions
            # Example: "Section 501.035(b)(7), (8), or (9)"
            {
                'name': 'section_subsection_subdivision_list',
                'pattern': self.reference_patterns.SHARED_BASE_LIST(
                    self.reference_patterns.COMBINE(
                        self.reference_patterns.SECTION_PREFIX,
                        self.reference_patterns.SECTION_NUMBER,
                        self.reference_patterns.SUBSECTION,
                        self.reference_patterns.SUBDIVISION
                    ),
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_section_subsection_subdivision_list_link,
                # 'debug': True  # Add debug flag to print matches
            },
            # External section with subsection and list of subdivisions
            # Example: "Section 501.035(b)(7), (8), or (9), Election Code"
            {
                'name': 'external_section_subsection_subdivision_list',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SHARED_BASE_LIST(
                        self.reference_patterns.COMBINE(
                            self.reference_patterns.SECTION_PREFIX,
                            self.reference_patterns.SECTION_NUMBER,
                            self.reference_patterns.SUBSECTION,
                            self.reference_patterns.SUBDIVISION
                        ),
                        self.reference_patterns.SUBDIVISION
                    ),
                    self.reference_patterns.COMMA_OF_THE_THIS,
                    self.reference_patterns.CODE_NAME
                ),
                'build_link': self.build_external_section_subsection_subdivision_list_link,
                # 'debug': True  # Add debug flag to print matches
            },
            # Title and section
            # Example: "Title 1, Section 1-1"
            {
                'name': 'title_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.TITLE_PREFIX,
                    self.reference_patterns.TITLE_NUMBER,
                    self.reference_patterns.COMMA_AND_SPACE,
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER
                ),
                'build_link': self.build_title_section_link
            },
            # Chapter and section
            # Example: "Chapter 1, Section 1-1"
            {
                'name': 'chapter_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.CHAPTER_PREFIX,
                    self.reference_patterns.CHAPTER_NUMBER,
                    self.reference_patterns.COMMA_AND_SPACE,
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER
                ),
                'build_link': self.build_chapter_section_link
            },
            # List of sections (less specific)
            # Example: "Sections 1-1, 1-2, and 1-3"
            {
                'name': 'section_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER
                ),
                'build_link': self.build_section_list_link
            },
            # Mixed list of sections with various formats
            # Example: "Section 11.61(b)(14), 22.12, 28.11, 32.17(a)(2), 32.17(a)(3), 61.71(a)(5), 61.71(a)(6), 61.74(a)(14), 69.13, 71.09, 101.04, 101.63, 104.01(a)(4), 106.03, 106.06, or 106.15"
            {
                'name': 'mixed_section_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER_WITH_SUBSECTIONS
                ),
                'build_link': self.build_mixed_section_list_link
            },
            # List of subdivisions
            # Example: "Subdivisions (1), (2), and (3)"
            {
                'name': 'subdivision_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.SUBDIVISION_PREFIX,
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_subdivision_list_link
            },
            # List of subsections
            # Example: "Subsections (a), (b), and (c)"
            {
                'name': 'subsection_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_subsection_list_link
            },
            # Single section reference (most generic)
            # Example: "Section 1-1"
            {
                'name': 'section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.SECTION_NUMBER
                ),
                'build_link': self.build_section_link,
            },
            # Section article constitution pattern
            # Example: Section 52-a, Article III, Texas Constitution
            {
                'name': 'section_article_constitution',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER,
                    self.reference_patterns.COMMA_AND_SPACE,
                    self.reference_patterns.ARTICLE_PREFIX,
                    self.reference_patterns.ARTICLE_NUMBER,
                    self.reference_patterns.COMMA_AND_SPACE,
                    self.reference_patterns.CONSTITUTION_PREFIX
                ),
                'build_link': self.build_section_article_constitution_link
            },
            # Federal USC section pattern
            # Example: "1 U.S.C. 101"
            # Example: "1 U.S.C. 101a"
            {
                'name': 'federal_usc_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.FEDERAL_TITLE,
                    self.reference_patterns.USC,
                    self.reference_patterns.SECTION_WORD_OR_SYMBOL,
                    self.reference_patterns.FEDERAL_USC_SECTION
                ),
                'build_link': self.build_federal_usc_section_link,
                'debug': True
            }
        ]

        # Register each pattern with the resolver
        self.add_patterns(patterns)
