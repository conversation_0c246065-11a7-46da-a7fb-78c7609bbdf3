import re
from typing import Dict, Pattern, List, Union

class ReferencePatterns:
    """Class containing regex patterns for statute references"""

    def __init__(self):
        # Code mappings
        self.CODE_MAP = {
            'Alcoholic Beverage Code': 'al',
            'Agriculture Code': 'ag',
            'Auxillary Water Laws': 'wl',
            'Business and Commerce Code': 'bc',
            'Business Organizations Code': 'bo',
            'Civil Practice and Remedies Code': 'cp',
            'Code of Criminal Procedure': 'cr',
            'Education Code': 'ed',
            'Election Code': 'el',
            'Estates Code': 'es',
            'Texas Rules of Evidence': 'ev',
            'Family Code': 'fa',
            'Finance Code': 'fi',
            'Government Code': 'gv',
            'Health and Safety Code': 'hs',
            'Human Resources Code': 'hr',
            'Insurance Code': 'in',
            'Labor Code': 'la',
            'Local Government Code': 'lg',
            'Natural Resources Code': 'nr',
            'Occupations Code': 'oc',
            'Parks and Wildlife Code': 'pw',
            'Penal Code': 'pe',
            'Property Code': 'pr',
            'Special District Local Laws Code': 'sd',
            'Tax Code': 'tx',
            'Transportation Code': 'tn',
            'Utilities Code': 'ut',
            'Water Code': 'wa',
            'Texas Constitution': 'cn'
        }

        # Roman numeral mappings
        self.ROMAN_NUMERALS_MAP = {
            'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6, 'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10,
            'XI': 11, 'XII': 12, 'XIII': 13, 'XIV': 14, 'XV': 15, 'XVI': 16, 'XVII': 17, 'XVIII': 18, 'XIX': 19, 'XX': 20,
            'XXI': 21, 'XXII': 22, 'XXIII': 23, 'XXIV': 24, 'XXV': 25, 'XXVI': 26, 'XXVII': 27, 'XXVIII': 28, 'XXIX': 29, 'XXX': 30,
            'XXXI': 31, 'XXXII': 32, 'XXXIII': 33, 'XXXIV': 34, 'XXXV': 35, 'XXXVI': 36, 'XXXVII': 37, 'XXXVIII': 38, 'XXXIX': 39, 'XL': 40,
            'XLI': 41, 'XLII': 42, 'XLIII': 43, 'XLIV': 44, 'XLV': 45, 'XLVI': 46, 'XLVII': 47, 'XLVIII': 48, 'XLIX': 49, 'L': 50,
            'LI': 51, 'LII': 52, 'LIII': 53, 'LIV': 54, 'LV': 55, 'LVI': 56, 'LVII': 57, 'LVIII': 58, 'LIX': 59, 'LX': 60,
            'LXI': 61, 'LXII': 62, 'LXIII': 63, 'LXIV': 64, 'LXV': 65, 'LXVI': 66, 'LXVII': 67, 'LXVIII': 68, 'LXIX': 69, 'LXX': 70,
            'LXXI': 71, 'LXXII': 72, 'LXXIII': 73, 'LXXIV': 74, 'LXXV': 75, 'LXXVI': 76, 'LXXVII': 77, 'LXXVIII': 78, 'LXXIX': 79, 'LXXX': 80,
            'LXXXI': 81, 'LXXXII': 82, 'LXXXIII': 83, 'LXXXIV': 84, 'LXXXV': 85, 'LXXXVI': 86, 'LXXXVII': 87, 'LXXXVIII': 88, 'LXXXIX': 89, 'XC': 90,
            'XCI': 91, 'XCII': 92, 'XCIII': 93, 'XCIV': 94, 'XCV': 95, 'XCVI': 96, 'XCVII': 97, 'XCVIII': 98, 'XCIX': 99, 'C': 100
        }

        # Create pattern that matches code names with flexible whitespace
        code_names = '|'.join(re.escape(name).replace(r'\ ', r'\s+') for name in self.CODE_MAP.keys())
        self.CODE_NAME = re.compile(fr'\s*({code_names})', re.IGNORECASE)

        # Article number patterns
        self.POSITIVE_INTEGER = re.compile(r'(?<![.\da-zA-Z])(?<!\d-)[1-9]\d*(?![.\da-zA-Z,])')
        roman_numerals = '|'.join(self.ROMAN_NUMERALS_MAP.keys())
        self.ROMAN_NUMERALS = re.compile(fr'\b({roman_numerals})\b', re.IGNORECASE)
        self.ARTICLE_NUMBER = re.compile(fr'([1-9]\d*|\b(?:{roman_numerals})\b)')

        # Section number patterns
        section_number_core = r'\d+(?:[a-zA-Z])?(?:\.\d+|-[a-zA-Z](?:-\d+)?)*'
        self.SECTION_NUMBER = re.compile(r'(?<!\.)\b(' + section_number_core + r')\b(?!-|\.[a-zA-Z])')
        self.SECTION_NUMBER_WITH_SUBSECTIONS = re.compile(r'(?<!\.)\b(' + section_number_core + r')(?:\s*\([a-z0-9](?:-\d+)?\)(?:\([a-z0-9]+\))*)?(?!\s*\.)')
        self.CN_SECTION_NUMBER = re.compile(r'(?<!\.|-)\b(\d+[A-Za-z]?(?:-[a-zA-Z0-9]+)*)\b(?!-|\.|\.|\.\d)')

        # Subsection patterns
        self.SUBSECTION = re.compile(r'(\([a-z](?:-\d+)?\))')
        self.SUBSECTION_WITH_SUBDIVISIONS = re.compile(r'(\([a-z0-9](?:-\d+)?\)(?:\([a-z0-9A-Z]+\))*)')
        self.SUBDIVISION = re.compile(r'(\((?!0+\d+)\d+\))')
        self.SUBPARAGRAPH = re.compile(r'(\([A-Z]\))')

        # Chapter and Title patterns
        self.CHAPTER_NUMBER = re.compile(r'\b(\d+(?:[A-Z]|-\d+\/\d+)?)\b')
        self.SUBCHAPTER_LETTER = re.compile(r'\b([A-Z](?:-\d+)?)\b')
        self.TITLE_NUMBER = re.compile(r'\b(\d+(?:[A-Z]|-[A-Z])?)\b')
        self.SUBTITLE_LETTER = re.compile(r'\b([A-Z](?:\d+|-\d+)?)\b')

        # Connectors and separators
        self.AND_OR_THROUGH = re.compile(r'(?:\s*,\s*|\s+)(?:and|or|through)\s+')
        self.OPTIONAL_COMMA = re.compile(r'\s*,?\s*')
        self.COMMA_AND_SPACE = re.compile(r'\s*,\s*')
        self.OF_THE_THIS = re.compile(r'(?:\s+of\s+(?:(?:the|this)\s+)?)')
        # Matches either:
        # 1. Comma with optional whitespace (\s*,\s*)
        # 2. OR whitespace + 'of' + optional 'the'/'this' (\s+of(?:\s+(?:the|this))?\s+)
        self.COMMA_OF_THE_THIS = re.compile(r'(?:\s*,\s*|\s+of(?:\s+(?:the|this))?\s+)')
        self.SECTION_CONNECTOR = re.compile(r'(?:\s*,?\s*|\s+of\s+(?:(?:the|this)\s+)?)')

        # Common prefixes with word boundaries
        self.CONSTITUTION_PREFIX = re.compile(r'\b(?:[Cc]onstitution(?:\s+[Oo]f\s+[Tt]exas|\s+[Tt]exas)?|[Tt]exas\s+[Cc]onstitution)\b')
        self.ARTICLE_PREFIX = re.compile(r'\b(?:[Aa]rticles?|[Aa]rt\.)\s+')
        self.TITLE_PREFIX = re.compile(r'\b[Tt]itles?\s+')
        self.SUBTITLE_PREFIX = re.compile(r'\b[Ss]ubtitles?\s+')
        self.CHAPTER_PREFIX = re.compile(r'\b[Cc]hapters?\s+')
        self.SUBCHAPTER_PREFIX = re.compile(r'\b[Ss]ubchapters?\s+')
        self.SECTION_PREFIX = re.compile(r'\b(?:[Ss]ections?|[Ss]ec\.)\s+')
        self.SUBSECTION_PREFIX = re.compile(r'\b[Ss]ubsections?\s+')
        self.SUBDIVISION_PREFIX = re.compile(r'\b[Ss]ubdivisions?\s+')
        self.SUBPARAGRAPH_PREFIX = re.compile(r'\b[Ss]ubparagraphs?\s+')

        # List patterns
        self.LIST_SEPARATOR = re.compile(r'(?:\s*,\s*(?:(?:and|or|through)\s+)?|\s+(?:and|or|through)\s+)')  # Matches ", " or ", and " or " and " or " through "
        self.COMMA_SEPARATED_LIST = re.compile(r'(?:\s*,\s*[^,]+)*')

        # Negative lookahead/behind
        self.NOT_FOLLOWED_BY_CODE = re.compile(r'(?!\s*,?\s*(?:[A-Za-z\s]+\s+Code))')

        # Federal statute pattern
        self.FEDERAL_TITLE = r'\b\d{1,3}\b'
        self.USC = r'\bU\.?S\.?C\.?\b'
        self.CFR = r'\bCFR\b'
        self.SECTION_WORD_OR_SYMBOL = r'\b(?:Section|§)\s+'
        self.FEDERAL_USC_SECTION = r'\b\d+[a-z]*(?:-[\da-z]+)?'
        self.FEDERAL_CFR_SECTION = r'\b\d+[a-z]*(?:-[\da-z]+)?'


    def normalize_code_name(self, text: str) -> str:
        """Normalize a code name by converting to single spaces between words and proper case"""
        # First normalize spaces
        normalized = ' '.join(text.strip().split())

        # Then handle case - try to find a matching key in CODE_MAP
        for key in self.CODE_MAP:
            if key.lower() == normalized.lower():
                return key

        return normalized

    def get_code_from_name(self, code_name: str) -> str:
        """Get code abbreviation from full name, handling extra spaces and case"""
        normalized_name = self.normalize_code_name(code_name)
        return self.CODE_MAP.get(normalized_name)

    def is_valid_code(self, code_name: str) -> bool:
        """Check if a code name is valid, handling extra spaces and case"""
        normalized_name = self.normalize_code_name(code_name)
        return normalized_name in self.CODE_MAP

    def get_article_number(self, text: str) -> int:
        """Convert article number from roman or arabic numerals to integer"""
        if isinstance(text, int):
            return text

        if isinstance(text, str):
            text = text.upper()
            if text in self.ROMAN_NUMERALS_MAP:
                return self.ROMAN_NUMERALS_MAP[text]
            try:
                return int(text)
            except ValueError:
                return text

        raise ValueError(f"Invalid article number: {text}")

    def LIST_OF(self, prefix: re.Pattern, pattern: re.Pattern) -> re.Pattern:
        """Create a pattern for matching lists of items with a prefix.

        The pattern will create the following capture groups:
        1. first_ref - The full "Prefix X" part
        2. first_section - Just the section number from pattern's capture group in first_ref
        3. rest_of_refs - The rest of the list as one group
        4. last_section - The last matched number from pattern's capture group in rest_of_refs
        """
        return re.compile(
            fr'({prefix.pattern}\s*{pattern.pattern})'  # First item with prefix and pattern
            fr'((?:{self.LIST_SEPARATOR.pattern}{pattern.pattern})*)',  # Rest of list as one group
            re.IGNORECASE
        )

    def LIST_OF_WITH_SUFFIX(self, prefix: re.Pattern, pattern: re.Pattern, connector: re.Pattern, suffix: re.Pattern) -> re.Pattern:
        """Create a pattern for matching lists that end with a specific suffix"""
        return re.compile(
            fr'({prefix.pattern}\s*{pattern.pattern})'  # First item with prefix
            fr'((?:{self.LIST_SEPARATOR.pattern}{pattern.pattern})*)'  # Additional items
            fr'{connector.pattern}'  # Connector between list and suffix
            fr'{suffix.pattern}',  # Suffix
            re.IGNORECASE
        )

    def SHARED_BASE_LIST(self, base_pattern: re.Pattern, repeating_pattern: re.Pattern) -> re.Pattern:
        """Create a pattern for matching lists where only the first item has the full pattern

        This pattern will create the following capture groups:
        1. full_first_part - The entire first part with the base pattern (e.g., "Section 1.001(a)")
        2. rest_of_text - The rest of the text with additional items (e.g., " and (b)")

        Plus any capture groups that exist in the base_pattern and repeating_pattern.
        """
        return re.compile(
            fr'({base_pattern.pattern})'  # Full first item as a capture group
            fr'((?:{self.LIST_SEPARATOR.pattern}{repeating_pattern.pattern})*)',  # Additional items as a capture group
            re.IGNORECASE
        )

    def COMBINE(self, *patterns: Union[re.Pattern, List[re.Pattern]]) -> re.Pattern:
        """Combine multiple patterns"""
        combined_patterns = []
        for pattern in patterns:
            if isinstance(pattern, list):
                # For arrays, combine the patterns and wrap in a capture group
                combined_array = ''.join(self._get_pattern_source(p) for p in pattern)
                combined_patterns.append(f'({combined_array})')
            else:
                # For non-arrays, just get the pattern source
                combined_patterns.append(self._get_pattern_source(pattern))

        # Join patterns
        source = ''.join(combined_patterns)
        return re.compile(source, re.IGNORECASE)

    def _escape_regexp(self, string: str) -> str:
        """Helper function to escape special regex characters"""
        return re.escape(string)

    def _get_pattern_source(self, pattern: Union[re.Pattern, str]) -> str:
        """Helper to get pattern source, preserving capture groups"""
        if isinstance(pattern, re.Pattern):
            return pattern.pattern
        # For strings, escape special characters and wrap in non-capturing group
        return f'(?:{self._escape_regexp(pattern)})'
