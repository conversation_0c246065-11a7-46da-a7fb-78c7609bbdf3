from motor.motor_asyncio import AsyncIOMotorClient
from typing import List, Dict, Any, Optional
import logging
import re
import os
import time

class MongoDB:
    # Content types that should be processed
    CONTENT_TYPES = ['subsection', 'subdivision', 'subparagraph', 'subpart', 'subitem']

    def __init__(self, uri: str = "mongodb://localhost:27017/statutes_db"):
        self.client = AsyncIOMotorClient(uri)
        self.db = self.client.get_database()
        # Get collection name from environment or default to 'nodes'
        self.collection_name = os.environ.get('MONGODB_COLLECTION', 'statutes')
        logging.info(f"Using MongoDB collection: {self.collection_name}")

        # Enable profiling for slow operations (>100ms)
        # self.db.command({'profile': 1, 'slowms': 100})
        # logging.info("Enabled MongoDB profiling for slow operations (>100ms)")

    async def get_nodes_cursor(self, code: str = None, batch_size: int = 100, start_from: str = None):
        """Get a cursor for content type nodes, optionally filtered by code and starting from a specific node"""
        collection = getattr(self.db, self.collection_name)
        filter_query = {"type": {"$in": self.CONTENT_TYPES}}

        if code:
            filter_query["code"] = code

        if start_from:
            # Find the order value of the start_from node
            start_node = await collection.find_one({"nodeId": start_from}, {"order": 1})
            if start_node and "order" in start_node:
                filter_query["order"] = {"$gte": start_node["order"]}
                logging.info(f"Starting from node {start_from} with order >= {start_node['order']}")
            else:
                logging.warning(f"Start node {start_from} not found or has no order field, proceeding without start filter")

        logging.info(f"Getting nodes cursor with filter: {filter_query}, batch_size: {batch_size}")
        return collection.find(filter_query).sort("order", 1).batch_size(batch_size)

    async def count_nodes(self, code: str = None, start_from: str = None) -> int:
        """Count total content type nodes, optionally filtered by code and starting from a specific node"""
        start_time = time.time()
        collection = getattr(self.db, self.collection_name)
        filter_query = {"type": {"$in": self.CONTENT_TYPES}}

        if code:
            filter_query["code"] = code

        if start_from:
            # Find the order value of the start_from node
            start_node = await collection.find_one({"nodeId": start_from}, {"order": 1})
            if start_node and "order" in start_node:
                filter_query["order"] = {"$gte": start_node["order"]}

        count = await collection.count_documents(filter_query)
        duration = time.time() - start_time
        logging.info(f"Found {count} nodes matching filter {filter_query} in {duration:.2f}s")
        return count

    async def clear_references(self, node_id: str):
        """Clear existing references for a node"""
        if not node_id:
            return

        start_time = time.time()
        collection = self.db.references
        result = await collection.delete_many({"nodeId": node_id})
        duration = time.time() - start_time
        logging.debug(f"clear_references for {node_id} took {duration:.2f}s (deleted {result.deleted_count} refs)")

    async def save_references(self, references: List[Dict[str, Any]]):
        """Save new references to the database"""
        if not references:
            return

        start_time = time.time()
        collection = self.db.references
        try:
            result = await collection.insert_many(references)
            duration = time.time() - start_time
            logging.debug(f"save_references took {duration:.2f}s (inserted {len(result.inserted_ids)} refs)")
        except Exception as e:
            logging.error(f"Error saving references: {e}")
            raise

    def _hierarchy_lookup_stage(self):
        """Create the hierarchy lookup stage for the aggregation pipeline"""
        return {
            "$graphLookup": {
                "from": self.collection_name,
                "startWith": "$parentId",
                "connectFromField": "parentId",
                "connectToField": "nodeId",
                "as": "hierarchy",
                "maxDepth": 10,
                "depthField": "depth",
                "restrictSearchWithMatch": {
                    "nodeId": {"$exists": True}  # Ensure we only get valid nodes
                }
            }
        }

    def _sorted_hierarchy_stage(self):
        """Create the sorted hierarchy stage for the aggregation pipeline"""
        return {
            "sortedHierarchy": {
                "$sortArray": {
                    "input": "$hierarchy",
                    "sortBy": { "order": 1 }
                }
            }
        }

    async def find_node_by_path(self, path: str) -> Optional[Dict[str, Any]]:
        """Find a node by its exact path with hierarchy information"""
        if not path:
            return None

        # Ensure path starts with /
        if not path.startswith('/'):
            path = '/' + path

        # Create the aggregation pipeline
        pipeline = [
            # Stage 1: Match the node
            {
                "$match": {
                    "nodeId": path
                }
            },

            # Stage 2: Get hierarchy
            self._hierarchy_lookup_stage(),

            # Stage 3: Add sorted hierarchy field
            {
                "$addFields": self._sorted_hierarchy_stage()
            },

            # Stage 4: Project the final shape
            {
                "$project": {
                    "_id": 0,
                    "nodeId": 1,
                    "type": 1,
                    "code": 1,
                    "collection": 1,
                    "text": 1,
                    "id": 1,
                    "parentId": 1,
                    'hierarchy': {
                        '$map': {
                            'input': '$sortedHierarchy',
                            'as': 'item',
                            'in': {
                                'nodeId': '$$item.nodeId',
                                'type': '$$item.type',
                                'code': '$$item.code',
                                'collection': '$$item.collection',
                                'text': '$$item.text',
                                'id': '$$item.id',
                                'parentId': '$$item.parentId',
                                'order': '$$item.order'
                            }
                        }
                    }
                }
            }
        ]

        # Execute the pipeline
        collection = getattr(self.db, self.collection_name)
        results = await collection.aggregate(pipeline, collation={"locale": "en", "numericOrdering": True}).to_list(1)
        return results[0] if results else None

    async def find_node_by_start_end_path(self, start_path: str, end_path: str) -> Optional[Dict[str, Any]]:
        """Find a node by matching the start and end of its path with hierarchy information"""
        logging.debug("\nDEBUG find_node_by_start_end_path:")
        logging.debug(f"start_path: '{start_path}'")
        logging.debug(f"end_path: '{end_path}'")

        start_time = time.time()
        if not start_path or not end_path:
            return None

        # Ensure paths start with /
        if not start_path.startswith('/'):
            start_path = '/' + start_path
        if not end_path.startswith('/'):
            end_path = '/' + end_path

        # Create regex pattern
        start_path_escaped = re.escape(start_path)
        end_path_escaped = re.escape(end_path)
        pattern = f"^{start_path_escaped}.*{end_path_escaped}$"

        # Use a simpler query first to check if the node exists
        collection = getattr(self.db, self.collection_name)
        node = await collection.find_one(
            {"nodeId": {"$regex": pattern}},
            {"nodeId": 1, "type": 1, "code": 1, "collection": 1, "id": 1, "parentId": 1}
        )

        if not node:
            duration = time.time() - start_time
            logging.debug(f"find_node_by_start_end_path miss took {duration:.2f}s")
            return None

        # Only if we found a node, then run the full hierarchy pipeline
        pipeline = [
            {"$match": {"_id": node["_id"]}},
            self._hierarchy_lookup_stage(),
            {"$addFields": self._sorted_hierarchy_stage()},
            {
                "$project": {
                    "_id": 0,
                    "nodeId": 1,
                    "type": 1,
                    "code": 1,
                    "collection": 1,
                    "text": 1,
                    "id": 1,
                    "parentId": 1,
                    'hierarchy': {
                        '$map': {
                            'input': '$sortedHierarchy',
                            'as': 'item',
                            'in': {
                                'nodeId': '$$item.nodeId',
                                'type': '$$item.type',
                                'code': '$$item.code',
                                'collection': '$$item.collection',
                                'text': '$$item.text',
                                'id': '$$item.id',
                                'parentId': '$$item.parentId',
                                'order': '$$item.order'
                            }
                        }
                    }
                }
            }
        ]

        # Execute the pipeline
        results = await collection.aggregate(pipeline, collation={"locale": "en", "numericOrdering": True}).to_list(1)
        logging.debug(f"results: {results}")
        duration = time.time() - start_time
        logging.debug(f"find_node_by_start_end_path hit took {duration:.2f}s")
        return results[0] if results else node  # Fall back to the simple node if pipeline fails

    async def get_node(self, node_id: str) -> Optional[Dict[str, Any]]:
        """Get a single node by its ID with hierarchy information"""
        if not node_id:
            return None

        start_time = time.time()
        collection = getattr(self.db, self.collection_name)

        # Create the aggregation pipeline
        pipeline = [
            # Stage 1: Match the node
            {
                "$match": {
                    "nodeId": node_id
                }
            },

            # Stage 2: Get hierarchy
            self._hierarchy_lookup_stage(),

            # Stage 3: Add sorted hierarchy field
            {
                "$addFields": self._sorted_hierarchy_stage()
            },

            # Stage 4: Project the final shape
            {
                "$project": {
                    "_id": 0,
                    "nodeId": 1,
                    "type": 1,
                    "code": 1,
                    "collection": 1,
                    "text": 1,
                    "id": 1,
                    "parentId": 1,
                    'hierarchy': {
                        '$map': {
                            'input': '$sortedHierarchy',
                            'as': 'item',
                            'in': {
                                'nodeId': '$$item.nodeId',
                                'type': '$$item.type',
                                'code': '$$item.code',
                                'collection': '$$item.collection',
                                'text': '$$item.text',
                                'id': '$$item.id',
                                'parentId': '$$item.parentId',
                                'order': '$$item.order'
                            }
                        }
                    }
                }
            }
        ]

        # Execute the pipeline
        results = await collection.aggregate(pipeline, collation={"locale": "en", "numericOrdering": True}).to_list(1)
        duration = time.time() - start_time
        logging.debug(f"get_node took {duration:.2f}s")
        return results[0] if results else None

    async def get_node_and_descendants(self, node_id: str) -> List[Dict[str, Any]]:
        """Get a node and all its descendants by node ID"""
        if not node_id:
            return []

        start_time = time.time()
        collection = getattr(self.db, self.collection_name)

        # Ensure node_id starts with /
        if not node_id.startswith('/'):
            node_id = '/' + node_id

        # Create regex pattern to match node and all descendants
        pattern = re.escape(node_id)
        logging.info(f"Searching for descendants with pattern: {pattern}")

        # Create aggregation pipeline to get sorted nodes with hierarchy
        pipeline = [
            # Stage 1: Match nodes
            {
                "$match": {
                    "nodeId": {"$regex": pattern}
                }
            },
            # Stage 2: Get hierarchy for each node
            self._hierarchy_lookup_stage(),
            # Stage 3: Add sorted hierarchy
            {
                "$addFields": self._sorted_hierarchy_stage()
            },
            # Stage 4: Sort by nodeId
            {
                "$sort": {
                    "order": 1
                }
            },
            # Stage 5: Project final shape
            {
                "$project": {
                    "_id": 0,
                    "nodeId": 1,
                    "type": 1,
                    "code": 1,
                    "collection": 1,
                    "text": 1,
                    "id": 1,
                    "parentId": 1,
                    'hierarchy': {
                        '$map': {
                            'input': '$sortedHierarchy',
                            'as': 'item',
                            'in': {
                                'nodeId': '$$item.nodeId',
                                'type': '$$item.type',
                                'code': '$$item.code',
                                'collection': '$$item.collection',
                                'text': '$$item.text',
                                'id': '$$item.id',
                                'parentId': '$$item.parentId',
                                'order': '$$item.order'
                            }
                        }
                    }
                }
            }
        ]
        logging.info(f"pipeline: {pipeline}")

        # Execute pipeline with numeric collation
        results = await collection.aggregate(
            pipeline,
            collation={"locale": "en", "numericOrdering": True}
        ).to_list(None)

        duration = time.time() - start_time
        logging.info(f"get_node_and_descendants found {len(results)} nodes in {duration:.2f}s")

        if results:
            # Log the order of nodes for debugging
            logging.info("Node order:")
            for node in results:
                logging.info(f"  {node.get('nodeId')} ({node.get('type')})")

        return results

    async def close(self):
        """Close the MongoDB connection"""
        self.client.close()
