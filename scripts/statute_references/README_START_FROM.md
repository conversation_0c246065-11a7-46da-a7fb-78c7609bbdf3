# Start From Feature

The `--start-from` parameter allows you to resume processing from a specific node within a code, continuing through to the end of that code. This is useful when processing fails partway through and you want to resume without reprocessing already completed nodes.

## Usage

```bash
# Resume Texas Tax Code processing from a specific section
python main.py --code tx --start-from /collection/tx/code/tx/title/1/chapter/5/section/5.10

# Resume with custom batch size and concurrency
python main.py --code gv --start-from /collection/tx/code/gv/title/2/chapter/51 --batch-size 50 --max-concurrent 5
```

## Validation Rules

1. `--start-from` requires `--code` to be specified
2. `--start-from` and `--node-id` are mutually exclusive
3. The start node must exist in the database
4. The start node must belong to the specified code

## How It Works

The system uses the `order` field in the database to determine processing sequence:

1. Finds the `order` value of the specified start node
2. Filters all subsequent nodes with `order >= start_node.order`
3. Processes nodes in order from that point to the end of the code

This ensures that processing resumes exactly where it should, without gaps or duplicates.

## Error Handling

- If the start node is not found, processing will stop with an error
- If the start node belongs to a different code than specified, processing will stop with an error
- If the start node has no `order` field, a warning is logged and processing continues without the start filter (falling back to full code processing)