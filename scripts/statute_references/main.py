import asyncio
import logging
import argparse
import os
import sys

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from statute_references.processor import ReferenceProcessor
from statute_references.config import get_mongo_uri

def parse_args():
    parser = argparse.ArgumentParser(description='Process statute references')
    parser.add_argument('--code', help='Code to process (e.g., tx)')
    parser.add_argument('--node-id', help='Process a specific node and its descendants')
    parser.add_argument('--start-from', help='Start processing from this node and continue through the rest of the code')
    parser.add_argument('--mongo-uri', help='MongoDB URI')
    parser.add_argument('--log-level', default='INFO',
                      choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                      help='Set the logging level')
    parser.add_argument('--batch-size', type=int, default=100,
                      help='Number of nodes to process in each batch')
    parser.add_argument('--max-concurrent', type=int, default=10,
                      help='Maximum number of concurrent operations')
    return parser.parse_args()

async def main():
    args = parse_args()
    parser = argparse.ArgumentParser()

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    try:
        # Validate argument combinations
        if args.start_from and not args.code:
            parser.error("--start-from requires --code to be specified")
        
        if args.start_from and args.node_id:
            parser.error("--start-from and --node-id are mutually exclusive")

        # Use provided URI or get from config
        mongo_uri = args.mongo_uri or get_mongo_uri()

        if args.node_id:
            logging.info(f"Starting reference processing for node: {args.node_id} and its descendants")
        elif args.start_from:
            logging.info(f"Starting reference processing from node: {args.start_from} through end of code: {args.code}")
        else:
            logging.info(f"Starting reference processing for {'all codes' if args.code is None else f'code: {args.code}'}")

        await ReferenceProcessor.run(
            code=args.code,
            mongo_uri=mongo_uri,
            batch_size=args.batch_size,
            max_concurrent=args.max_concurrent,
            node_id=args.node_id,
            start_from=args.start_from
        )
        logging.info("Reference processing completed successfully")
    except Exception as e:
        logging.error(f"Error in main: {e}")
        raise

if __name__ == '__main__':
    asyncio.run(main())
