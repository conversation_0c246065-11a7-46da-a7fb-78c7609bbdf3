import asyncio
import logging
from typing import Optional, List
from .db import MongoDB
from .resolver_factory import ResolverFactory
from .config import get_mongo_uri
import time

class ReferenceProcessor:
    def __init__(self, mongo_uri: Optional[str] = None,
                 batch_size: int = 100,
                 max_concurrent: int = 10):  # Only configuration parameters
        self.mongo_uri = mongo_uri or get_mongo_uri()
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.db = None
        self.stats = {
            'total_time': 0,
            'process_time': 0,
            'db_time': 0,
            'nodes_processed': 0,
            'references_found': 0
        }
        self.semaphore = None  # Will be initialized in initialize()
        self.processed_count = 0
        self.total_nodes = 0
        self.process_start_time = None
        self.total_references = 0
        self.processed_nodes = set()  # Add this line to track processed nodes

    async def initialize(self):
        """Initialize database connection and create indexes"""
        start_time = time.time()
        self.db = MongoDB(self.mongo_uri)
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        duration = time.time() - start_time
        logging.info(f"Initialization took {duration:.2f}s")

    async def _save_reference_batch(self):
        """Save the current batch of references and clear the batch"""
        if not self.reference_batch:
            return

        logging.debug(f"Saving batch of {len(self.reference_batch)} references")
        try:
            await self.db.save_references(self.reference_batch)
            self.reference_batch = []
            logging.debug("Reference batch saved successfully")
        except Exception as e:
            logging.error(f"Error saving reference batch: {e}")
            # Don't clear the batch on error so we can retry
            raise

    async def _log_progress(self):
        """Log processing progress"""
        if self.processed_count % 100 == 0:
            elapsed_time = time.time() - self.process_start_time
            nodes_per_second = self.processed_count / elapsed_time
            percent = (self.processed_count / self.total_nodes) * 100
            eta_seconds = (self.total_nodes - self.processed_count) / nodes_per_second if nodes_per_second > 0 else 0

            logging.info(
                f"Processed {self.processed_count} of {self.total_nodes} nodes ({percent:.1f}%) - "
                f"Found {self.total_references} references - "
                f"In {elapsed_time/60:.1f} minutes - "
                f"Speed: {nodes_per_second:.1f} nodes/s - "
                f"ETA: {eta_seconds/60:.1f} minutes"
            )

    async def process_node(self, node: dict) -> int:
        """Process a single node and return number of references found"""
        if not node.get('text'):
            return 0

        node_id = node.get('nodeId', 'unknown')

        # Add this check to skip already processed nodes
        if node_id in self.processed_nodes:
            logging.info(f"Skipping already processed node {node_id}")
            return 0

        logging.info(f"Processing node {node_id}")

        # Add the node to processed set
        self.processed_nodes.add(node_id)

        # Clear existing references for this node
        await self.db.clear_references(node_id)

        try:
            resolver = ResolverFactory.create_resolver(node, self.db)
        except ValueError as e:
            logging.warning(f"Skipping node {node_id}: {str(e)}")
            return 0

        # Find references in the node's text
        references = await resolver.process_text(node['text'])
        if references:
            logging.info(f"Found {len(references)} references in node {node_id}")
            try:
                await self.db.save_references(references)
                logging.debug(f"Saved {len(references)} references for node {node_id}")
                return len(references)
            except Exception as e:
                logging.error(f"Error saving references for node {node_id}: {e}")
                return 0

        return 0

    async def process_node_batch(self, nodes: List[dict]) -> int:
        """Process a batch of nodes concurrently"""
        if not nodes:
            return 0

        async def process_with_semaphore(node):
            async with self.semaphore:
                return await self.process_node(node)

        # Process nodes concurrently with semaphore control
        results = await asyncio.gather(
            *[process_with_semaphore(node) for node in nodes],
            return_exceptions=True
        )

        # Handle any exceptions
        total_refs = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logging.error(f"Error processing node {nodes[i].get('nodeId')}: {result}")
            else:
                total_refs += result

        # Update progress counters
        self.processed_count += len(nodes)
        self.total_references += total_refs
        await self._log_progress()

        return total_refs

    async def process_nodes(self, code: Optional[str] = None, node_id: Optional[str] = None, start_from: Optional[str] = None):
        """Process all nodes for a given code, a node and its descendants, or starting from a specific node"""
        try:
            self.process_start_time = time.time()
            # Reset counters for new processing run
            self.processed_count = 0
            self.total_nodes = 0
            self.total_references = 0

            # Validate start_from node if provided
            if start_from:
                if not code:
                    raise ValueError("start_from parameter requires code to be specified")
                
                # Verify the start node exists and belongs to the specified code
                start_node = await self.db.get_node(start_from)
                if not start_node:
                    raise ValueError(f"Start node {start_from} not found")
                
                if start_node.get('code') != code:
                    raise ValueError(f"Start node {start_from} belongs to code '{start_node.get('code')}', not '{code}'")
                
                logging.info(f"Validated start node {start_from} belongs to code {code}")

            if node_id:
                # Process node and its descendants
                nodes = await self.db.get_node_and_descendants(node_id)
                if not nodes:
                    logging.error(f"Node {node_id} and descendants not found")
                    return

                self.total_nodes = len(nodes)
                # Filter nodes to only include content types
                content_nodes = [node for node in nodes if node.get('type') in self.db.CONTENT_TYPES]
                num_descendants = len(content_nodes) - 1 if content_nodes else 0
                logging.info(f"Processing node {node_id} and {num_descendants} descendants")

                # Process nodes in batches
                for i in range(0, len(content_nodes), self.batch_size):
                    batch = content_nodes[i:i + self.batch_size]
                    await self.process_node_batch(batch)

                # Log final stats
                total_time = time.time() - self.process_start_time
                nodes_per_second = self.processed_count / total_time if total_time > 0 else 0
                logging.info(f"Completed processing {node_id} and descendants in {total_time:.1f}s")
                logging.info(f"Processed {self.processed_count} nodes")
                logging.info(f"Average speed: {nodes_per_second:.1f} nodes/s")
                logging.info(f"Total references found: {self.total_references}")
                return

            self.total_nodes = await self.db.count_nodes(code, start_from)

            if self.total_nodes == 0:
                logging.info("No nodes found to process")
                return

            if start_from:
                logging.info(f"Starting to process {self.total_nodes} nodes from {start_from}")
            else:
                logging.info(f"Starting to process {self.total_nodes} nodes")
            cursor = await self.db.get_nodes_cursor(code, self.batch_size, start_from)
            current_batch = []

            async for node in cursor:
                current_batch.append(node)

                if len(current_batch) >= self.batch_size:
                    await self.process_node_batch(current_batch)
                    current_batch = []

            # Process any remaining nodes
            if current_batch:
                logging.debug(f"Processing final batch of {len(current_batch)} nodes")
                await self.process_node_batch(current_batch)

            # Log final stats
            total_time = time.time() - self.process_start_time
            nodes_per_second = self.processed_count / total_time if total_time > 0 else 0

            logging.info(f"Completed processing {self.processed_count} nodes in {total_time:.1f}s")
            logging.info(f"Average speed: {nodes_per_second:.1f} nodes/s")
            logging.info(f"Total references found: {self.total_references}")

        except Exception as e:
            logging.error(f"Error in process_nodes: {e}")
            raise
        finally:
            if self.db:
                await self.db.close()

    @classmethod
    async def run(cls, code: Optional[str] = None,
                 mongo_uri: Optional[str] = None,
                 batch_size: int = 100,
                 max_concurrent: int = 10,
                 node_id: Optional[str] = None,
                 start_from: Optional[str] = None):
        """Class method to create and run a processor

        Args:
            code: Optional code to filter nodes by
            mongo_uri: Optional MongoDB URI
            batch_size: Number of nodes to process in each batch
            max_concurrent: Maximum number of concurrent operations
            node_id: Optional node ID to process a single node
            start_from: Optional node ID to start processing from
        """
        processor = cls(mongo_uri=mongo_uri,
                      batch_size=batch_size,
                      max_concurrent=max_concurrent)
        await processor.initialize()
        await processor.process_nodes(code=code, node_id=node_id, start_from=start_from)
