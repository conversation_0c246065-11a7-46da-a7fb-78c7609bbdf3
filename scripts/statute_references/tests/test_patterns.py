import pytest
from statute_references.patterns import ReferencePatterns
import re

@pytest.fixture
def patterns():
    return ReferencePatterns()

class TestReferencePatterns:
    def test_code_name_patterns(self, patterns):
        """Test code name pattern matching"""
        test_cases = [
            ("Government Code", "Government Code"),
            ("Texas Constitution", "Texas Constitution"),
            ("Business and Commerce Code", "Business and Commerce Code"),
            ("Code of Criminal Procedure", "Code of Criminal Procedure"),
            # Test with extra spaces
            ("Government   Code", "Government Code"),
            # Test with different cases
            ("GOVERNMENT CODE", "Government Code"),
            ("government code", "Government Code"),
        ]

        for input_text, expected_match in test_cases:
            match = patterns.CODE_NAME.search(input_text)
            assert match is not None
            normalized_match = patterns.normalize_code_name(match.group(1))
            assert normalized_match in patterns.CODE_MAP

    def test_roman_numerals(self, patterns):
        """Test roman numeral pattern matching and conversion"""
        test_cases = [
            ("I", 1),
            ("V", 5),
            ("X", 10),
            ("L", 50),
            ("C", 100),
            ("XIV", 14),
            ("XLII", 42),
            ("XCIX", 99),
        ]

        for numeral, expected_value in test_cases:
            # Test pattern matching
            match = patterns.ROMAN_NUMERALS.match(numeral)
            assert match is not None
            assert match.group(0) == numeral

            # Test conversion
            assert patterns.get_article_number(numeral) == expected_value

    def test_section_number_patterns(self, patterns):
        """Test section number pattern matching"""
        test_cases = [
            # Regular section numbers
            ("1.001", True),
            ("2.002", True),
            ("123.456", True),
            # Section numbers with letters
            ("1a", True),
            ("2b.001", True),
            # Section numbers with hyphens
            ("1-a", True),
            ("2-b-1", True),
            # Invalid section numbers
            (".001", False),
            ("1.", False),
            ("1..001", False),
            ("a1.001", False),
        ]

        for section_number, should_match in test_cases:
            match = patterns.SECTION_NUMBER.match(section_number)
            assert (match is not None) == should_match

    def test_subsection_patterns(self, patterns):
        """Test subsection pattern matching"""
        test_cases = [
            # Basic subsections
            ("(a)", True),
            ("(b)", True),
            ("(z)", True),
            # Subsections with numbers
            ("(a-1)", True),
            ("(b-2)", True),
            # Invalid subsections
            ("(1)", False),  # This is a subdivision
            ("(A)", False),  # This is a subparagraph
            ("a", False),
            ("()", False),
            ("(a-)", False),
        ]

        for subsection, should_match in test_cases:
            match = patterns.SUBSECTION.match(subsection)
            assert (match is not None) == should_match

    def test_subdivision_patterns(self, patterns):
        """Test subdivision pattern matching"""
        test_cases = [
            # Basic subdivisions
            ("(1)", True),
            ("(2)", True),
            ("(10)", True),
            # Invalid subdivisions
            ("(01)", False),  # Leading zero
            ("(a)", False),   # Letter
            ("(A)", False),   # Capital letter
            ("1", False),     # No parentheses
            ("()", False),    # Empty
        ]

        for subdivision, should_match in test_cases:
            match = patterns.SUBDIVISION.match(subdivision)
            assert (match is not None) == should_match

    def test_list_patterns(self, patterns):
        """Test list pattern matching"""
        section_list_pattern = patterns.LIST_OF(
            patterns.SECTION_PREFIX,
            patterns.SECTION_NUMBER
        )

        test_cases = [
            # Simple list
            ("Sections 1.001, 1.002", True),
            # List with 'and'
            ("Sections 1.001 and 1.002", True),
            # List with 'or'
            ("Sections 1.001 or 1.002", True),
            # List with multiple items
            ("Sections 1.001, 1.002, and 1.003", True),
            # List with 'through'
            ("Sections 1.001 through 1.010", True),
            # Invalid lists
            ("Section 1.001", True),  # Single item
            ("Sections", False),       # No sections
            ("1.001, 1.002", False),   # No prefix
        ]

        for text, should_match in test_cases:
            match = section_list_pattern.match(text)
            assert (match is not None) == should_match, f"Failed on '{text}'"

    def test_list_separator_pattern(self, patterns):
        """Test LIST_SEPARATOR pattern matching"""
        test_cases = [
            # Basic separators
            (", ", True),
            (" and ", True),
            (" or ", True),
            (" through ", True),
            # Combined formats
            (", and ", True),
            (", or ", True),
            (", through ", True),
            # Invalid separators
            ("and,", False),
            ("or,", False),
            ("through,", False),
            ("", False),
        ]

        for text, should_match in test_cases:
            match = patterns.LIST_SEPARATOR.match(text)
            assert (match is not None) == should_match, f"Failed on '{text}'"
            if match:
                # The match should cover the entire input
                assert match.group(0) == text, f"Match '{match.group(0)}' doesn't equal input '{text}'"

    def test_combined_patterns(self, patterns):
        """Test combined pattern matching"""
        # Test section with chapter
        section_chapter_pattern = patterns.COMBINE(
            patterns.SECTION_PREFIX,
            patterns.SECTION_NUMBER,
            patterns.SECTION_CONNECTOR,  # This pattern handles the whitespace between section and chapter
            patterns.CHAPTER_PREFIX,
            patterns.CHAPTER_NUMBER
        )

        # Test section with subsection
        section_subsection_pattern = patterns.COMBINE(
            patterns.SECTION_PREFIX,
            patterns.SECTION_NUMBER,
            patterns.SUBSECTION  # No whitespace needed between number and subsection
        )

        # Test section with subsection and subdivision
        section_subsection_subdivision_pattern = patterns.COMBINE(
            patterns.SECTION_PREFIX,
            patterns.SECTION_NUMBER,
            patterns.SUBSECTION,  # No whitespace needed between number and subsection
            patterns.SUBDIVISION  # No whitespace needed between subsection and subdivision
        )

        test_cases = [
            # Basic combination
            ("Section 1.001, Chapter 1", True, section_chapter_pattern),
            # With 'of'
            ("Section 1.001 of Chapter 1", True, section_chapter_pattern),
            # With 'of the'
            ("Section 1.001 of the Chapter 1", True, section_chapter_pattern),
            # Invalid combinations
            ("Section 1.001", False, section_chapter_pattern),
            ("Chapter 1", False, section_chapter_pattern),
            ("Section 1.001 Chapter 1", True, section_chapter_pattern),  # Space-only connector is valid

            # Section with subsection
            ("Section 27-d(a)", True, section_subsection_pattern),
            ("Section 1.001(b)", True, section_subsection_pattern),
            ("Section 1.001 (b)", False, section_subsection_pattern),  # Extra space not allowed

            # Section with subsection and subdivision
            ("Section 27-d(a)(1)", True, section_subsection_subdivision_pattern),
            ("Section 1.001(b)(2)", True, section_subsection_subdivision_pattern),
            ("Section 1.001(b) (2)", False, section_subsection_subdivision_pattern),  # Extra space not allowed
        ]

        for text, should_match, pattern in test_cases:
            match = pattern.match(text)
            assert (match is not None) == should_match, f"Failed on '{text}'"

    def test_constitution_section_patterns(self, patterns):
        """Test constitution section pattern matching"""
        test_cases = [
            # Basic section numbers
            ("49-c", True),
            ("49-d", True),
            ("49-d-1", True),
            # Invalid section numbers
            ("-49-d", False),
            ("49-", False),
            ("49--d", False),
        ]

        for section_number, should_match in test_cases:
            match = patterns.CN_SECTION_NUMBER.match(section_number)
            assert (match is not None) == should_match

    def test_prefix_patterns(self, patterns):
        """Test prefix pattern matching"""
        test_cases = [
            # Article prefix
            ("Article ", patterns.ARTICLE_PREFIX),
            ("Articles ", patterns.ARTICLE_PREFIX),
            ("Art. ", patterns.ARTICLE_PREFIX),
            # Section prefix
            ("Section ", patterns.SECTION_PREFIX),
            ("Sections ", patterns.SECTION_PREFIX),
            ("Sec. ", patterns.SECTION_PREFIX),
            # Chapter prefix
            ("Chapter ", patterns.CHAPTER_PREFIX),
            ("Chapters ", patterns.CHAPTER_PREFIX),
            # Subchapter prefix
            ("Subchapter ", patterns.SUBCHAPTER_PREFIX),
            ("Subchapters ", patterns.SUBCHAPTER_PREFIX),
            # Title prefix
            ("Title ", patterns.TITLE_PREFIX),
            ("Titles ", patterns.TITLE_PREFIX),
        ]

        for text, pattern in test_cases:
            match = pattern.match(text)
            assert match is not None

    def test_connector_patterns(self, patterns):
        """Test connector pattern matching"""
        test_cases = [
            # AND_OR_THROUGH
            (", and ", patterns.AND_OR_THROUGH),
            (", or ", patterns.AND_OR_THROUGH),
            (", through ", patterns.AND_OR_THROUGH),
            # COMMA_AND_SPACE
            (", ", patterns.COMMA_AND_SPACE),
            # OF_THE_THIS
            (" of ", patterns.OF_THE_THIS),
            (" of the ", patterns.OF_THE_THIS),
            (" of this ", patterns.OF_THE_THIS),
            # COMMA_OF_THE_THIS - test both formats
            (", ", patterns.COMMA_OF_THE_THIS),
            (" of ", patterns.COMMA_OF_THE_THIS),
            (" of the ", patterns.COMMA_OF_THE_THIS),
            (" of this ", patterns.COMMA_OF_THE_THIS),
            # Test with different spacing
            ("   ,   ", patterns.COMMA_OF_THE_THIS),
            ("  of  ", patterns.COMMA_OF_THE_THIS),
            ("  of  the  ", patterns.COMMA_OF_THE_THIS),
            ("  of  this  ", patterns.COMMA_OF_THE_THIS),
            # Test comma variations
            (",", patterns.COMMA_OF_THE_THIS),
            (", ", patterns.COMMA_OF_THE_THIS),
            (" , ", patterns.COMMA_OF_THE_THIS),
            ("  ,  ", patterns.COMMA_OF_THE_THIS),
        ]

        for text, pattern in test_cases:
            match = pattern.match(text)
            assert match is not None, f"Pattern should match '{text}'"
            assert match.group(0) == text, f"Full match should equal input text '{text}'"

        # Test cases that should not match
        negative_test_cases = [
            ("of,", patterns.COMMA_OF_THE_THIS),
            ("the", patterns.COMMA_OF_THE_THIS),
            ("this", patterns.COMMA_OF_THE_THIS),
            ("of the,", patterns.COMMA_OF_THE_THIS),
            ("of this,", patterns.COMMA_OF_THE_THIS),
            (" ", patterns.COMMA_OF_THE_THIS),  # Single space should not match
            ("  ", patterns.COMMA_OF_THE_THIS),  # Multiple spaces should not match
        ]

        for text, pattern in negative_test_cases:
            match = pattern.match(text)
            assert match is None, f"Pattern should not match '{text}'"

    def test_shared_base_list_pattern(self, patterns):
        """Test SHARED_BASE_LIST pattern matching and capture groups"""
        # Create a pattern for section with list of subsections
        section_subsection_list_pattern = patterns.SHARED_BASE_LIST(
            patterns.COMBINE(
                patterns.SECTION_PREFIX,
                patterns.SECTION_NUMBER,
                patterns.SUBSECTION
            ),
            patterns.SUBSECTION
        )

        test_cases = [
            # Basic cases
            ("Section 1.001(a) and (b)", True),
            ("Section 1.001(a), (b)", True),
            ("Section 1.001(a), (b), and (c)", True),
            ("Section 1.001(a) through (c)", True),
            ("Sections 11.39(a) and (b)", True),
            ("Sections 11.39(a), (b), and (c)", True),
            # Invalid cases
            ("Section 1.001", False),  # No subsections
            ("Subsection (a) and (b)", False),  # Wrong prefix
            ("Section 1.001 (a)", False),  # Space between section number and subsection
        ]

        for text, should_match in test_cases:
            match = section_subsection_list_pattern.search(text)
            assert (match is not None) == should_match, f"Failed on '{text}'"

            if match:
                # Verify the capture groups
                print(f"Match groups for '{text}':")
                for i, group in enumerate(match.groups()):
                    print(f"  Group {i+1}: '{group}'")

                # Based on the output, the pattern captures these groups:
                # 1. full_first_part - The entire first part (e.g., "Section 1.001(a)")
                # 2. section_number - The section number (e.g., "1.001")
                # 3. first_subsection - The first subsection (e.g., "(a)")
                # 4. rest_of_text - The rest of the text with additional items (e.g., " and (b)")
                # 5. last_subsection - The last subsection (e.g., "(b)")
                assert len(match.groups()) >= 5

                # Check the first group (full_first_part)
                assert "Section" in match.group(1) or "Sections" in match.group(1)
                assert "(" in match.group(1) and ")" in match.group(1)

                # Check the second group (section_number)
                assert re.match(r'\d+\.\d+', match.group(2))

                # Check the third group (first_subsection)
                assert re.match(r'\([a-z](?:-\d+)?\)', match.group(3))

                # Check the fourth group (rest_of_text)
                assert " and " in match.group(4) or ", " in match.group(4) or " through " in match.group(4)

                # Check the fifth group (last_subsection)
                assert re.match(r'\([a-z](?:-\d+)?\)', match.group(5))

                # Test specific cases with known expected groups
                if text == "Section 1.001(a) and (b)":
                    assert match.group(1) == "Section 1.001(a)"
                    assert match.group(2) == "1.001"
                    assert match.group(3) == "(a)"
                    assert match.group(4) == " and (b)"
                    assert match.group(5) == "(b)"

                if text == "Sections 11.39(a), (b), and (c)":
                    assert match.group(1) == "Sections 11.39(a)"
                    assert match.group(2) == "11.39"
                    assert match.group(3) == "(a)"
                    assert ", (b), and (c)" in match.group(4)

    def test_section_number_with_subsections_patterns(self, patterns):
        """Test section number with subsections pattern matching"""
        test_cases = [
            # Regular section numbers
            ("1.001", True),
            ("2.002", True),
            ("123.456", True),
            # Section numbers with letters
            ("1a", True),
            ("2b.001", True),
            # Section numbers with hyphens
            ("1-a", True),
            ("2-b-1", True),
            # Section numbers with subsections
            ("1.001(a)", True),
            ("2.002(b)", True),
            ("123.456(c)", True),
            ("1.001 (a)", True),  # Space between section number and subsection
            # Section numbers with subsections and subdivisions
            ("1.001(a)(1)", True),
            ("2.002(b)(2)", True),
            ("123.456(c)(3)", True),
            # Complex examples
            ("11.61(b)(14)", True),
            ("32.17(a)(2)", True),
            ("61.71(a)(5)", True),
            ("61.71 (a)(5)", True),
            # Invalid section numbers
            (".001", False),
            ("1.", False),
            ("1..001", False),
            ("a1.001", False),
        ]

        for section_number, should_match in test_cases:
            match = patterns.SECTION_NUMBER_WITH_SUBSECTIONS.match(section_number)
            assert (match is not None) == should_match, f"Failed on '{section_number}'"
            if match and should_match:
                assert match.group(0) == section_number, f"Match '{match.group(0)}' doesn't equal input '{section_number}'"
