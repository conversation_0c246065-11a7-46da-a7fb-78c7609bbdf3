import pytest
from unittest.mock import AsyncMock, Mock
import re
from statute_references.resolver import BaseReferenceResolver
from statute_references.patterns import ReferencePatterns
from statute_references.db import MongoDB

@pytest.fixture
def mock_db():
    return AsyncMock(spec=MongoDB)

@pytest.fixture
def mock_node():
    node = {
        'nodeId': '/test/node',
        'type': 'section',
        'text': 'Test text'
    }
    return node

@pytest.fixture
def base_resolver(mock_node, mock_db):
    resolver = BaseReferenceResolver(mock_node, mock_db)
    return resolver

class TestBaseReferenceResolver:
    @pytest.mark.asyncio
    async def test_find_references_empty_text(self, base_resolver):
        """Test that find_references returns empty list for empty text"""
        references = await base_resolver.find_references("")
        assert references == []

        references = await base_resolver.find_references(None)
        assert references == []

    @pytest.mark.asyncio
    async def test_find_references_basic_pattern(self, base_resolver):
        """Test finding references with a basic pattern"""
        # Add a simple test pattern
        async def build_link(matched_text, number, offset):
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'target': f'/test/{number}',
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text)
                }]
            }

        pattern = {
            'name': 'test_pattern',
            'pattern': re.compile(r'Number (\d+)'),
            'build_link': build_link
        }
        base_resolver.add_pattern(pattern)

        # Test text with pattern
        text = "This is Number 123 and Number 456"
        references = await base_resolver.find_references(text)

        assert len(references) == 2
        assert references[0]['text'] == "Number 123"
        assert references[0]['pattern'] == 'test_pattern'
        assert references[0]['matches'][0]['target'] == '/test/123'
        assert references[1]['text'] == "Number 456"
        assert references[1]['pattern'] == 'test_pattern'
        assert references[1]['matches'][0]['target'] == '/test/456'

    @pytest.mark.asyncio
    async def test_find_references_overlapping_patterns(self, base_resolver):
        """Test that overlapping references are properly filtered"""
        # Add two patterns that could overlap
        async def build_link_1(matched_text, number, offset):
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'target': f'/test/{number}',
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text)
                }]
            }

        async def build_link_2(matched_text, number1, number2, offset):
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [
                    {
                        'text': f"Number {number1}",
                        'target': f'/test/{number1}',
                        'startOffset': offset,
                        'endOffset': offset + len(f"Number {number1}")
                    },
                    {
                        'text': f"Number {number2}",
                        'target': f'/test/{number2}',
                        'startOffset': offset + len(f"Number {number1} and "),
                        'endOffset': offset + len(matched_text)
                    }
                ]
            }

        base_resolver.add_patterns([
            {
                'name': 'single_number',
                'pattern': re.compile(r'Number (\d+)'),
                'build_link': build_link_1
            },
            {
                'name': 'number_pair',
                'pattern': re.compile(r'Number (\d+) and Number (\d+)'),
                'build_link': build_link_2
            }
        ])

        # Test text with overlapping patterns
        text = "Number 123 and Number 456"
        references = await base_resolver.find_references(text)

        # Should prefer the longer match (number_pair) over individual matches
        assert len(references) == 1
        assert references[0]['pattern'] == 'number_pair'
        assert len(references[0]['matches']) == 2

    @pytest.mark.asyncio
    async def test_find_references_error_handling(self, base_resolver):
        """Test error handling in reference finding"""
        async def build_link_error(matched_text, number, offset):
            raise Exception("Test error")

        base_resolver.add_pattern({
            'name': 'error_pattern',
            'pattern': re.compile(r'Number (\d+)'),
            'build_link': build_link_error
        })

        # Should not raise exception and should return empty list
        text = "Number 123"
        references = await base_resolver.find_references(text)
        assert references == []

    def test_sort_and_filter_references(self, base_resolver):
        """Test reference sorting and filtering"""
        references = [
            # Reference with 2 matches
            {
                'text': 'Numbers 1 and 2',
                'startOffset': 20,
                'endOffset': 35,
                'matches': [
                    {'text': 'Number 1', 'target': '/test/1'},
                    {'text': 'Number 2', 'target': '/test/2'}
                ]
            },
            # Reference with 1 match but earlier position
            {
                'text': 'Number 3',
                'startOffset': 0,
                'endOffset': 8,
                'matches': [
                    {'text': 'Number 3', 'target': '/test/3'}
                ]
            },
            # Reference that overlaps with first one
            {
                'text': 'Number 1',
                'startOffset': 20,
                'endOffset': 28,
                'matches': [
                    {'text': 'Number 1', 'target': '/test/1'}
                ]
            }
        ]

        filtered = base_resolver._sort_and_filter_references(references)

        # Should keep the reference with most matches and filter overlapping ones
        assert len(filtered) == 2
        assert len(filtered[0]['matches']) == 1  # Number 3
        assert len(filtered[1]['matches']) == 2  # Numbers 1 and 2
        assert filtered[0]['startOffset'] < filtered[1]['startOffset']

    def test_add_patterns(self, base_resolver):
        """Test adding multiple patterns"""
        patterns = [
            {'name': 'pattern1', 'pattern': re.compile(r'test1')},
            {'name': 'pattern2', 'pattern': re.compile(r'test2')}
        ]

        base_resolver.add_patterns(patterns)
        assert len(base_resolver.patterns) == 2

    def test_add_pattern(self, base_resolver):
        """Test adding a single pattern"""
        pattern = {'name': 'pattern1', 'pattern': re.compile(r'test1')}
        base_resolver.add_pattern(pattern)
        assert len(base_resolver.patterns) == 1

    @pytest.mark.asyncio
    async def test_process_text(self, base_resolver):
        """Test the process_text method"""
        # Add a test pattern
        async def build_link(matched_text, number, offset):
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'target': f'/test/{number}',
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text)
                }]
            }

        base_resolver.add_pattern({
            'name': 'test_pattern',
            'pattern': re.compile(r'Number (\d+)'),
            'build_link': build_link
        })

        # Test normal case
        text = "This is Number 123"
        references = await base_resolver.process_text(text)
        assert len(references) == 1
        assert references[0]['text'] == "Number 123"

        # Test empty text
        assert await base_resolver.process_text("") == []
        assert await base_resolver.process_text(None) == []

        # Test error case
        async def build_link_error(matched_text, number, offset):
            raise Exception("Test error")

        base_resolver.patterns = [{
            'name': 'error_pattern',
            'pattern': re.compile(r'Number (\d+)'),
            'build_link': build_link_error
        }]

        # Should handle error gracefully
        references = await base_resolver.process_text(text)
        assert references == []
