import pytest
from unittest.mock import AsyncMock
from statute_references.texas_constitution_resolver import TexasConstitutionResolver
from statute_references.db import MongoDB

@pytest.fixture
def mock_db():
    db = AsyncMock(spec=MongoDB)
    return db

@pytest.fixture
def mock_constitution_node():
    return {
        'nodeId': '/collection/tx/code/cn/article/1/section/1',
        'collection': 'tx',
        'code': 'cn',
        'type': 'section',
        'id': '1',
        'text': 'Sample text',
        'hierarchy': [
            {'type': 'collection', 'id': 'tx'},
            {'type': 'code', 'id': 'cn'},
            {'type': 'article', 'id': '1'},
            {'type': 'section', 'id': '1'}
        ]
    }

class TestTexasConstitutionResolver:
    @pytest.mark.asyncio
    async def test_find_article_node(self, mock_db, mock_constitution_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_constitution_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for finding articles
        test_cases = [
            {
                'article': '1',
                'expected_path': '/article/1',
                'description': 'numeric article'
            },
            {
                'article': 'I',
                'expected_path': '/article/1',
                'description': 'roman numeral article'
            }
        ]

        for case in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test finding article
            article = await resolver.find_article_node(case['article'])
            assert article == mock_constitution_node

            # Verify database calls
            expected_call = (('/collection/tx/code/cn', case['expected_path']),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_find_section_node(self, mock_db, mock_constitution_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_constitution_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for finding sections
        test_cases = [
            {
                'section': '1',
                'article': '1',
                'expected_path': '/article/1/section/1',
                'description': 'with article'
            },
            {
                'section': '1',
                'article': None,
                'expected_path': '/section/1',
                'description': 'without article (using context)'
            }
        ]

        for case in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test finding section
            section = await resolver.find_section_node(case['section'], case['article'])
            assert section == mock_constitution_node

            # Verify database calls
            expected_call = (('/collection/tx/code/cn', case['expected_path']),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_process_text_section_reference(self, mock_db, mock_constitution_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_constitution_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for section references
        test_cases = [
            "Section 1",
            "Sec. 1"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            internal_section_refs = [ref for ref in references if ref['pattern'] == 'internal_section']
            assert len(internal_section_refs) > 0, "Pattern 'internal_section' must be one of the matches"

            # Verify the internal_section reference has correct properties
            internal_section_ref = internal_section_refs[0]
            assert len(internal_section_ref['matches']) == 1
            match = internal_section_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == mock_constitution_node['nodeId']
            assert match['status'] is None

            # Other patterns may match too, that's fine
            # But we've verified that internal_section is definitely one of them

            # Verify database was called with expected path
            expected_call = (('/collection/tx/code/cn', '/article/1/section/1'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_process_text_article_section(self, mock_db, mock_constitution_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_constitution_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for article and section references
        test_cases = [
            "Article I, Section 1",
            "Article 1, Section 1",
            "Art. I, Section 1"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            article_section_refs = [ref for ref in references if ref['pattern'] == 'article_section']
            assert len(article_section_refs) > 0, "Pattern 'article_section' must be one of the matches"

            # Verify the article_section reference has correct properties
            article_section_ref = article_section_refs[0]
            assert article_section_ref['text'] == text

            # Verify the match has the correct properties
            assert len(article_section_ref['matches']) == 1
            match = article_section_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == mock_constitution_node['nodeId']
            assert match['status'] is None

            # Verify database was called with expected path
            expected_call = (('/collection/tx/code/cn', '/article/1/section/1'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that article_section is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_section_list(self, mock_db, mock_constitution_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_constitution_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for section lists
        test_cases = [
            "Sections 1, 2, and 3",
            "Sections 1 and 2",
            "Sections 1, 2"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            section_list_refs = [ref for ref in references if ref['pattern'] == 'section_list']
            assert len(section_list_refs) > 0, "Pattern 'section_list' must be one of the matches"

            # Verify the section_list reference has correct properties
            section_list_ref = section_list_refs[0]
            assert section_list_ref['text'] == text

            # Count expected matches based on section numbers in text
            expected_matches = len([x for x in text.split() if x.strip(',.').isdigit()])
            assert len(section_list_ref['matches']) == expected_matches

            # Verify each match has correct properties
            for match in section_list_ref['matches']:
                assert match['target'] == mock_constitution_node['nodeId']
                assert match['status'] is None
                assert isinstance(match['startOffset'], int)
                assert isinstance(match['endOffset'], int)
                assert match['startOffset'] < match['endOffset']
                assert text[match['startOffset']:match['endOffset']] in text

            # Other patterns may match too, that's fine
            # But we've verified that section_list is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subsection_reference(self, mock_db, mock_constitution_node):
        subsection_node = {**mock_constitution_node, 'nodeId': f"{mock_constitution_node['nodeId']}(a)"}
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subsection_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for subsection references
        test_cases = [
            "Section 1(a)",
            "Sec. 1(a)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            section_subsection_refs = [ref for ref in references if ref['pattern'] == 'section_subsection']
            assert len(section_subsection_refs) > 0, "Pattern 'section_subsection' must be one of the matches"

            # Verify the section_subsection reference has correct properties
            section_subsection_ref = section_subsection_refs[0]
            assert section_subsection_ref['text'] == text

            # Verify the match has the correct properties
            assert len(section_subsection_ref['matches']) == 1
            match = section_subsection_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == subsection_node['nodeId']
            assert match['status'] is None

            # Verify database was called with expected path
            expected_call = (('/collection/tx/code/cn', '/article/1/section/1(a)'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that section_subsection is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subsection_list(self, mock_db, mock_constitution_node):
        subsection_node = {**mock_constitution_node, 'nodeId': f"{mock_constitution_node['nodeId']}(a)"}
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subsection_node)
        resolver = TexasConstitutionResolver(mock_constitution_node, mock_db)

        # Test cases for subsection lists
        test_cases = [
            "Subsections (a), (b), and (c) of Section 1",
            "Subsections (a) and (b) of Section 1",
            "Subsections (a), (b) of Section 1"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            subsection_list_refs = [ref for ref in references if ref['pattern'] == 'subsection_list']
            assert len(subsection_list_refs) > 0, "Pattern 'subsection_list' must be one of the matches"

            # Verify the subsection_list reference has correct properties
            subsection_list_ref = subsection_list_refs[0]
            assert subsection_list_ref['text'] == text

            # Count expected matches based on subsection patterns
            expected_matches = len([x for x in text.split() if x.strip(',.').startswith('(')])
            assert len(subsection_list_ref['matches']) == expected_matches

            # Verify each match has correct properties
            for match in subsection_list_ref['matches']:
                assert match['target'] == subsection_node['nodeId']
                assert match['status'] is None
                assert isinstance(match['startOffset'], int)
                assert isinstance(match['endOffset'], int)
                assert match['startOffset'] < match['endOffset']
                assert text[match['startOffset']:match['endOffset']] in text

            # Other patterns may match too, that's fine
            # But we've verified that subsection_list is definitely one of them
