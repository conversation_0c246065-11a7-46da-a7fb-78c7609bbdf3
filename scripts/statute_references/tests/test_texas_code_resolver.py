import pytest
from unittest.mock import AsyncMock
from statute_references.texas_code_resolver import TexasCodeResolver
from statute_references.db import MongoDB
import re

@pytest.fixture
def mock_db():
    db = AsyncMock(spec=MongoDB)
    return db

@pytest.fixture
def mock_node():
    return {
        'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001',
        'collection': 'tx',
        'code': 'gv',
        'type': 'section',
        'id': '1.001',
        'text': 'Sample text',
        'hierarchy': [
            {'type': 'collection', 'id': 'tx'},
            {'type': 'code', 'id': 'gv'},
            {'type': 'chapter', 'id': '1'},
            {'type': 'section', 'id': '1.001'}
        ]
    }

class TestTexasCodeResolver:
    @pytest.mark.asyncio
    async def test_find_section_node(self, mock_db, mock_node):
        # Setup mock response
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for finding sections
        test_cases = [
            {
                'section': '1.001',
                'chapter': '1',
                'expected_path': '/chapter/1/section/1.001'
            },
            {
                'section': '1.001',
                'chapter': None,  # Test without chapter (using context)
                'expected_path': '/section/1.001'
            }
        ]

        for case in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test finding section
            section = await resolver.find_section_node(case['section'], case['chapter'])
            assert section == mock_node

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', case['expected_path']),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_find_subsection_node(self, mock_db, mock_node):
        subsection_node = {**mock_node, 'nodeId': f"{mock_node['nodeId']}(a)"}
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subsection_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for finding subsections
        test_cases = [
            {
                'section': '1.001',
                'subsection': '(a)',
                'chapter': '1',
                'expected_path': '/chapter/1/section/1.001(a)'
            },
            {
                'section': '1.001',
                'subsection': '(a)',
                'chapter': None,  # Test without chapter (using context)
                'expected_path': '/section/1.001(a)'
            }
        ]

        for case in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test finding subsection
            subsection = await resolver.find_subsection_node(case['section'], case['subsection'], case['chapter'])
            assert subsection == subsection_node

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', case['expected_path']),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_process_text_section_list(self, mock_db, mock_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for section lists
        test_cases = [
            "Sections 1.001, 1.002, and 1.003",
            "Sections 1.001 and 1.002",
            "Sections 1.001, 1.002",
            "Section 11.67 or 32.18",
            "Sections 1.001 through 1.003"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            section_list_refs = [ref for ref in references if ref['pattern'] == 'section_list']
            assert len(section_list_refs) > 0, f"Pattern 'section_list' must be one of the matches for text: {text}"

            # Verify the section_list reference has correct properties
            section_list_ref = section_list_refs[0]
            assert section_list_ref['text'] == text

            # Verify matches
            assert len(section_list_ref['matches']) > 0

            # For the new test case, verify that both section numbers are matched
            if text == "Section 11.67 or 32.18":
                # Should have at least 2 matches (one for each section number)
                assert len(section_list_ref['matches']) >= 2

                # Verify first section match
                first_match = section_list_ref['matches'][0]
                assert "11.67" in first_match['text']
                assert first_match['target'] == mock_node['nodeId']

                # Find the match for the second section
                second_section_matches = [m for m in section_list_ref['matches'] if "32.18" in m['text']]
                assert len(second_section_matches) > 0, "No match found for section 32.18"
                second_match = second_section_matches[0]
                assert second_match['target'] == mock_node['nodeId']

            # Verify database calls - at least one call should be made
            assert mock_db.find_node_by_start_end_path.called

            # Other patterns may match too, that's fine
            # But we've verified that section_list is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_section_chapter(self, mock_db, mock_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for section with chapter references
        test_cases = [
            "Section 1.001, Chapter 1",
            "Section 1.001 of Chapter 1",
            "Section 1.001 of the Chapter 1"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            section_chapter_refs = [ref for ref in references if ref['pattern'] == 'section_chapter']
            assert len(section_chapter_refs) > 0, "Pattern 'section_chapter' must be one of the matches"

            # Verify the section_chapter reference has correct properties
            section_chapter_ref = section_chapter_refs[0]
            assert section_chapter_ref['text'] == text

            # Verify matches
            assert len(section_chapter_ref['matches']) == 1
            match = section_chapter_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == mock_node['nodeId']
            assert match['status'] is None

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', '/chapter/1/section/1.001'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that section_chapter is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_external_chapter(self, mock_db, mock_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for external chapter references
        test_cases = [
            "Chapter 311, Government Code",
            "Chapter 311 of the Government Code",
            "Chapter 311 of Government Code",
            "Chapter 2110, Government Code,"  # Added test case with trailing comma
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            external_chapter_refs = [ref for ref in references if ref['pattern'] == 'external_chapter']
            assert len(external_chapter_refs) > 0, f"Pattern 'external_chapter' must be one of the matches for text: {text}"

            # Verify the external_chapter reference has correct properties
            external_chapter_ref = external_chapter_refs[0]

            # For the case with trailing comma, the match should not include the trailing comma
            if text == "Chapter 2110, Government Code,":
                expected_text = "Chapter 2110, Government Code"
                assert external_chapter_ref['text'] == expected_text, f"Expected '{expected_text}' but got '{external_chapter_ref['text']}'"
            else:
                assert external_chapter_ref['text'] == text

            # Verify matches
            assert len(external_chapter_ref['matches']) == 1
            match = external_chapter_ref['matches'][0]

            # For the case with trailing comma, the match should not include the trailing comma
            if text == "Chapter 2110, Government Code,":
                expected_text = "Chapter 2110, Government Code"
                assert match['text'] == expected_text, f"Expected '{expected_text}' but got '{match['text']}'"
            else:
                assert match['text'] == text

            assert match['target'] == mock_node['nodeId']
            assert match['status'] is None

            # Verify database calls
            chapter_number = "2110" if "2110" in text else "311"
            expected_call = (('/collection/tx/code/gv', f'/chapter/{chapter_number}'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that external_chapter is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_external_section(self, mock_db, mock_node):
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=mock_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for external section references
        test_cases = [
            "Section 442.001, Government Code",
            "Section 442.001 of the Government Code",
            "Section 442.001 of Government Code"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            external_section_refs = [ref for ref in references if ref['pattern'] == 'external_section']
            assert len(external_section_refs) > 0, "Pattern 'external_section' must be one of the matches"

            # Verify the external_section reference has correct properties
            external_section_ref = external_section_refs[0]
            assert external_section_ref['text'] == text

            # Verify matches
            assert len(external_section_ref['matches']) == 1
            match = external_section_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == mock_node['nodeId']
            assert match['status'] is None

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', '/section/442.001'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that external_section is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subchapter_chapter(self, mock_db, mock_node):
        # Create a mock subchapter node based on the mock_node
        subchapter_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/5/subchapter/A',
            'type': 'subchapter',
            'id': 'A'
        }
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subchapter_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for subchapter with chapter references
        test_cases = [
            "Subchapter A, Chapter 5",
            "Subchapter A of Chapter 5",
            "Subchapter A of the Chapter 5"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            subchapter_chapter_refs = [ref for ref in references if ref['pattern'] == 'subchapter_chapter']
            assert len(subchapter_chapter_refs) > 0, "Pattern 'subchapter_chapter' must be one of the matches"

            # Verify the subchapter_chapter reference has correct properties
            subchapter_chapter_ref = subchapter_chapter_refs[0]
            assert subchapter_chapter_ref['text'] == text

            # Verify matches
            assert len(subchapter_chapter_ref['matches']) == 1
            match = subchapter_chapter_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == subchapter_node['nodeId']
            assert match['status'] is None

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', '/chapter/5/subchapter/A'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that subchapter_chapter is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subsection(self, mock_db, mock_node):
        # Create a mock subsection node based on the mock_node
        subsection_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(a)',
            'type': 'subsection',
            'id': '(a)'
        }
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subsection_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for subsection references
        test_cases = [
            "Subsection (a)",
            "Subsection (b)",
            "Subsection (c-1)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0

            # Find the reference with our expected pattern - it must exist
            subsection_refs = [ref for ref in references if ref['pattern'] == 'subsection']
            assert len(subsection_refs) > 0, "Pattern 'subsection' must be one of the matches"

            # Verify the subsection reference has correct properties
            subsection_ref = subsection_refs[0]
            assert subsection_ref['text'] == text

            # Verify matches
            assert len(subsection_ref['matches']) == 1
            match = subsection_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == subsection_node['nodeId']
            assert match['status'] is None

            # Extract the subsection from the text (everything between parentheses)
            subsection = text[text.index('('):text.index(')')+1]

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', f'/section/1.001{subsection}'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that subsection is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subdivision(self, mock_db, mock_node):
        # Create a mock subdivision node based on the mock_node
        subdivision_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(a)(1)',
            'type': 'subdivision',
            'id': '(1)'
        }

        # Update the mock_node to include a subsection in the hierarchy
        # and add the isSubsection method
        mock_node_with_subsection = {
            **mock_node,
            'hierarchy': [
                {'type': 'collection', 'id': 'tx'},
                {'type': 'code', 'id': 'gv'},
                {'type': 'chapter', 'id': '1'},
                {'type': 'section', 'id': '1.001'},
                {'type': 'subsection', 'id': '(a)'}
            ],
            'getHierarchy': lambda: [
                {'type': 'collection', 'id': 'tx'},
                {'type': 'code', 'id': 'gv'},
                {'type': 'chapter', 'id': '1'},
                {'type': 'section', 'id': '1.001'},
                {'type': 'subsection', 'id': '(a)'}
            ],
            'isContentNode': lambda: True,
            'isSubsection': lambda: True
        }

        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subdivision_node)
        resolver = TexasCodeResolver(mock_node_with_subsection, mock_db)

        # Test cases for subdivision references
        test_cases = [
            "Subdivision (1)",
            "Subdivision (2)",
            "Subdivision (3)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            subdivision_refs = [ref for ref in references if ref['pattern'] == 'subdivision']
            assert len(subdivision_refs) > 0, "Pattern 'subdivision' must be one of the matches"

            # Verify the subdivision reference has correct properties
            subdivision_ref = subdivision_refs[0]
            assert subdivision_ref['text'] == text

            # Verify matches
            assert len(subdivision_ref['matches']) == 1
            match = subdivision_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == subdivision_node['nodeId']
            assert match['status'] is None

            # Extract the subdivision from the text (everything between parentheses)
            subdivision = text[text.index('('):text.index(')')+1]

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', f'/section/1.001(a){subdivision}'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that subdivision is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subsection_subdivision(self, mock_db, mock_node):
        # Create a mock subdivision node based on the mock_node
        subdivision_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(c)(1)',
            'type': 'subdivision',
            'id': '(1)'
        }

        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subdivision_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for subsection with subdivision references
        test_cases = [
            "Subsection (c)(1)",
            "Subsection (d)(2)",
            "Subsection (e-1)(3)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            subsection_subdivision_refs = [ref for ref in references if ref['pattern'] == 'subsection_subdivision']
            assert len(subsection_subdivision_refs) > 0, "Pattern 'subsection_subdivision' must be one of the matches"

            # Verify the subsection_subdivision reference has correct properties
            subsection_subdivision_ref = subsection_subdivision_refs[0]
            assert subsection_subdivision_ref['text'] == text

            # Verify matches
            assert len(subsection_subdivision_ref['matches']) == 1
            match = subsection_subdivision_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == subdivision_node['nodeId']
            assert match['status'] is None

            # Extract the subsection and subdivision from the text
            subsection_end = text.index(')(') + 1
            subsection = text[text.index('('):subsection_end]
            subdivision = text[subsection_end:text.rindex(')')+1]

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', f'/section/1.001{subsection}{subdivision}'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that subsection_subdivision is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_title(self, mock_db, mock_node):
        # Create a mock title node based on the mock_node
        title_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/title/3',
            'type': 'title',
            'id': '3'
        }

        mock_db.find_node_by_start_end_path = AsyncMock(return_value=title_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for title references
        test_cases = [
            "Title 3",
            "Title 4",
            "Title 10"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            title_refs = [ref for ref in references if ref['pattern'] == 'title']
            assert len(title_refs) > 0, f"Pattern 'title' must be one of the matches for text: {text}"

            # Verify the title reference has correct properties
            title_ref = title_refs[0]
            assert title_ref['text'] == text

            # Verify matches
            assert len(title_ref['matches']) == 1
            match = title_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == title_node['nodeId']
            assert match['status'] is None

            # Extract the title number from the text
            title_number = text.split(' ')[1]

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', f'/title/{title_number}'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that title is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_chapter_list(self, mock_db, mock_node):
        # Create a mock chapter node based on the mock_node
        chapter_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/28',
            'type': 'chapter',
            'id': '28'
        }

        mock_db.find_node_by_start_end_path = AsyncMock(return_value=chapter_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for chapter list references
        test_cases = [
            "Chapters 28, 30, and 32",
            "Chapter 28, 30, or 32",
            "Chapters 28 and 30",
            "Chapter 28 or 30",
            "Chapters 28 through 32"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            chapter_list_refs = [ref for ref in references if ref['pattern'] == 'chapter_list']
            assert len(chapter_list_refs) > 0, f"Pattern 'chapter_list' must be one of the matches for text: {text}"

            # Verify the chapter_list reference has correct properties
            chapter_list_ref = chapter_list_refs[0]
            assert chapter_list_ref['text'] == text

            # Verify matches - should have multiple matches for the chapters
            assert len(chapter_list_ref['matches']) > 1, f"Expected multiple matches for text: {text}"

            # Verify first chapter match
            first_match = chapter_list_ref['matches'][0]
            assert "28" in first_match['text']
            assert first_match['target'] == chapter_node['nodeId']
            assert first_match['status'] is None

            # Check for additional chapter numbers in the matches
            chapter_numbers = ["28", "30", "32"]
            for chapter_number in chapter_numbers:
                chapter_matches = [m for m in chapter_list_ref['matches'] if chapter_number in m['text']]
                if chapter_number in text:
                    assert len(chapter_matches) > 0, f"No match found for chapter {chapter_number} in text: {text}"
                    for match in chapter_matches:
                        assert match['target'] == chapter_node['nodeId']
                        assert match['status'] is None

            # Verify database calls - at least one call should be made
            assert mock_db.find_node_by_start_end_path.called

            # Verify the first chapter call
            expected_call = (('/collection/tx/code/gv', '/chapter/28'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that chapter_list is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_subsection_list(self, mock_db, mock_node):
        # Create a mock subsection node based on the mock_node
        subsection_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(a)',
            'type': 'subsection',
            'id': '(a)'
        }
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subsection_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for subsection lists
        test_cases = [
            "Subsections (a), (b), and (c)",
            "Subsections (a) and (b)",
            "Subsections (a), (b)",
            "Subsections (b) through (j)"  # Testing the "through" syntax
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            subsection_list_refs = [ref for ref in references if ref['pattern'] == 'subsection_list']
            assert len(subsection_list_refs) > 0, f"Pattern 'subsection_list' must be one of the matches for text: '{text}'"

            # Verify the subsection_list reference has correct properties
            subsection_list_ref = subsection_list_refs[0]

            # Check for specific subsections based on the test case
            if text == "Subsections (a), (b), and (c)":
                assert "(a)" in subsection_list_ref['text']
                assert "(b)" in subsection_list_ref['text']
                assert "(c)" in subsection_list_ref['text']
            elif text == "Subsections (a) and (b)":
                assert "(a)" in subsection_list_ref['text']
                assert "(b)" in subsection_list_ref['text']
            elif text == "Subsections (b) through (j)":
                # For "through" syntax, we only need to check the first and last subsections
                assert "(b)" in subsection_list_ref['text']
                assert "(j)" in subsection_list_ref['text']
                # We don't need to check for intermediate subsections like (c), (d), etc.
            else:
                # Extract all subsections from the original text
                subsections = [x for x in text.split() if x.strip(',.').startswith('(')]
                for subsection in subsections:
                    assert subsection.strip(',.') in subsection_list_ref['text'], f"Subsection {subsection} not found in matched text: {subsection_list_ref['text']}"

            # Count expected matches based on subsection patterns
            expected_matches = len([x for x in text.split() if x.strip(',.').startswith('(')])
            assert len(subsection_list_ref['matches']) == expected_matches, f"Expected {expected_matches} matches for text: '{text}', got {len(subsection_list_ref['matches'])}"

            # Verify each match has correct properties
            for match in subsection_list_ref['matches']:
                assert match['target'] == subsection_node['nodeId']
                assert match['status'] is None
                assert isinstance(match['startOffset'], int)
                assert isinstance(match['endOffset'], int)
                assert match['startOffset'] < match['endOffset']
                assert text[match['startOffset']:match['endOffset']] in text

            # Verify database calls - at least one call should be made
            assert mock_db.find_node_by_start_end_path.called

            # Other patterns may match too, that's fine
            # But we've verified that subsection_list is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_section_subsection_subdivision(self, mock_db, mock_node):
        # Create a mock subsection node based on the mock_node
        subdivision_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/61.71(a)(14)',
            'type': 'subdivision',
            'id': '(14)'
        }
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subdivision_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for section with subsection and subdivision references
        test_cases = [
            "Section 61.71(a)(14)",
            "Section 442.001(b)(3)",
            "Sec. 61.71(a)(14)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            section_subsection_subdivision_refs = [ref for ref in references if ref['pattern'] == 'section_subsection_subdivision']
            assert len(section_subsection_subdivision_refs) > 0, f"Pattern 'section_subsection_subdivision' must be one of the matches for text: '{text}'"

            # Verify the section_subsection_subdivision reference has correct properties
            section_subsection_subdivision_ref = section_subsection_subdivision_refs[0]
            assert section_subsection_subdivision_ref['text'] == text

            # Verify matches
            assert len(section_subsection_subdivision_ref['matches']) == 1
            match = section_subsection_subdivision_ref['matches'][0]
            assert match['text'] == text
            assert match['target'] == subdivision_node['nodeId']
            assert match['status'] is None

            # Extract the section, subsection, and subdivision from the text
            section_match = re.search(r'\b(\d+\.\d+|\d+[A-Za-z]?(?:\.\d+)?(?:-[a-z])?)\b', text)
            section = section_match.group(0) if section_match else None
            subsection_match = re.search(r'(\([a-z](?:-\d+)?\))', text)
            subsection = subsection_match.group(0) if subsection_match else None
            subdivision_match = re.search(r'(\(\d+\))', text)
            subdivision = subdivision_match.group(0) if subdivision_match else None

            # Verify database calls
            expected_call = (('/collection/tx/code/gv', f'/section/{section}{subsection}{subdivision}'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that section_subsection_subdivision is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_section_subsection_list(self, mock_db, mock_node):
        # Create a mock subsection node based on the mock_node
        subsection_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/11.39(a)',
            'type': 'subsection',
            'id': '(a)'
        }
        mock_db.find_node_by_start_end_path = AsyncMock(return_value=subsection_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for section with list of subsections
        test_cases = [
            "Sections 11.39(a) and (b)",
            "Sections 11.39(a), (b), and (c)",
            "Sections 11.39(a) through (c)",
            "Section 11.39(a), (b), or (c)",
            "Section 11.39(a) or (c)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Test the reference resolution
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern - it must exist
            section_subsection_list_refs = [ref for ref in references if ref['pattern'] == 'section_subsection_list']
            assert len(section_subsection_list_refs) > 0, f"Pattern 'section_subsection_list' must be one of the matches for text: '{text}'"

            # Verify the section_subsection_list reference has correct properties
            section_subsection_list_ref = section_subsection_list_refs[0]

            # Check for specific subsections based on the test case
            if text == "Sections 11.39(a) and (b)":
                assert "(a)" in section_subsection_list_ref['text']
                assert "(b)" in section_subsection_list_ref['text']
            elif text == "Sections 11.39(a), (b), and (c)":
                # The pattern might not match the entire text, but should at least match (a) and (b)
                assert "(a)" in section_subsection_list_ref['text']
                assert "(b)" in section_subsection_list_ref['text']
                # If (c) is not in the matched text, check that it's in one of the matches
                if "(c)" not in section_subsection_list_ref['text']:
                    assert any("(c)" in match['text'] for match in section_subsection_list_ref['matches'])
            elif text == "Sections 11.39(a) through (c)":
                assert "(a)" in section_subsection_list_ref['text']
                # The pattern might not capture the "through (c)" part
            elif text == "Section 11.39(a), (b), or (c)":
                assert "(a)" in section_subsection_list_ref['text']
                assert "(b)" in section_subsection_list_ref['text']
                # If (c) is not in the matched text, check that it's in one of the matches
                if "(c)" not in section_subsection_list_ref['text']:
                    assert any("(c)" in match['text'] for match in section_subsection_list_ref['matches'])
            elif text == "Section 11.39(a) or (c)":
                assert "(a)" in section_subsection_list_ref['text']
                # If (c) is not in the matched text, check that it's in one of the matches
                if "(c)" not in section_subsection_list_ref['text']:
                    assert any("(c)" in match['text'] for match in section_subsection_list_ref['matches'])

            # Count expected matches based on subsection patterns
            expected_matches = len([x for x in text.split() if x.strip(',.').startswith('(')])
            # Add 1 for the first subsection which is part of the section reference
            expected_matches = expected_matches + 1 if "11.39(a)" in text else expected_matches
            assert len(section_subsection_list_ref['matches']) >= 2, f"Expected at least 2 matches for text: '{text}', got {len(section_subsection_list_ref['matches'])}"

            # Verify first match has the section number with first subsection
            first_match = section_subsection_list_ref['matches'][0]
            assert "11.39(a)" in first_match['text']
            assert first_match['target'] == subsection_node['nodeId']
            assert first_match['status'] is None

            # Verify each additional match is a subsection
            for match in section_subsection_list_ref['matches'][1:]:
                assert match['text'].startswith('(') and match['text'].endswith(')')
                assert match['target'] == subsection_node['nodeId']
                assert match['status'] is None
                assert isinstance(match['startOffset'], int)
                assert isinstance(match['endOffset'], int)
                assert match['startOffset'] < match['endOffset']
                assert text[match['startOffset']:match['endOffset']] in text

            # Verify database calls - at least one call should be made for the first subsection
            expected_call = (('/collection/tx/code/gv', '/section/11.39(a)'),)
            assert expected_call in mock_db.find_node_by_start_end_path.call_args_list

            # Other patterns may match too, that's fine
            # But we've verified that section_subsection_list is definitely one of them

    @pytest.mark.asyncio
    async def test_process_text_mixed_section_list(self, mock_db, mock_node):
        # Create a mock section node based on the mock_node
        section_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/11.61',
            'type': 'section',
            'id': '11.61'
        }

        # Create a mock subsection node
        subsection_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/11.61(b)',
            'type': 'subsection',
            'id': '(b)'
        }

        # Create a mock subdivision node
        subdivision_node = {
            **mock_node,
            'nodeId': '/collection/tx/code/gv/chapter/1/section/11.61(b)(14)',
            'type': 'subdivision',
            'id': '(14)'
        }

        # Configure the mock to return different nodes based on the path
        async def mock_find_node(start_path, end_path):
            if '11.61(b)(14)' in end_path:
                return subdivision_node
            elif '11.61(b)' in end_path:
                return subsection_node
            elif '11.61' in end_path:
                return section_node
            else:
                return section_node  # Default to section node for simplicity

        mock_db.find_node_by_start_end_path = AsyncMock(side_effect=mock_find_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test case for mixed section list
        text = "Section 11.61(b)(14), 22.12, 28.11, 32.17(a)(2), 32.17(a)(3), 61.71(a)(5), 61.71(a)(6), 61.74(a)(14), 69.13, 71.09, 101.04, 101.63, 104.01(a)(4), 106.03, 106.06, or 106.15"

        # Test the reference resolution
        references = await resolver.process_text(text)

        # Verify that we got at least one reference
        assert len(references) > 0, f"No references found for text: {text}"

        # Find the reference with our expected pattern - it must exist
        mixed_section_list_refs = [ref for ref in references if ref['pattern'] == 'mixed_section_list']
        assert len(mixed_section_list_refs) > 0, f"Pattern 'mixed_section_list' must be one of the matches for text: '{text}'"

        # Verify the mixed_section_list reference has correct properties
        mixed_section_list_ref = mixed_section_list_refs[0]
        assert mixed_section_list_ref['text'] == text

        # Verify that we have matches for different section formats
        assert any("11.61(b)(14)" in match['text'] for match in mixed_section_list_ref['matches']), "Should match section with subsection and subdivision"
        assert any("22.12" in match['text'] for match in mixed_section_list_ref['matches']), "Should match simple section"
        assert any("32.17(a)" in match['text'] for match in mixed_section_list_ref['matches']), "Should match section with subsection"

        # Verify that we have the correct number of matches
        # Count the number of section references in the text
        section_count = len(re.findall(r'\d+\.\d+(?:\([a-z](?:-\d+)?\))?(?:\(\d+\))?', text))
        assert len(mixed_section_list_ref['matches']) >= section_count, f"Expected at least {section_count} matches, got {len(mixed_section_list_ref['matches'])}"

    @pytest.mark.asyncio
    async def test_process_text_section_subsection_subdivision_list(self, mock_db, mock_node):
        # Create mock subdivision nodes based on the mock_node
        subdivision_nodes = {
            '(7)': {
                **mock_node,
                'nodeId': '/collection/tx/code/gv/chapter/1/section/501.035(b)(7)',
                'type': 'subdivision',
                'id': '(7)'
            },
            '(8)': {
                **mock_node,
                'nodeId': '/collection/tx/code/gv/chapter/1/section/501.035(b)(8)',
                'type': 'subdivision',
                'id': '(8)'
            },
            '(9)': {
                **mock_node,
                'nodeId': '/collection/tx/code/gv/chapter/1/section/501.035(b)(9)',
                'type': 'subdivision',
                'id': '(9)'
            }
        }

        # Setup mock to return appropriate node based on path
        async def mock_find_node(start_path, end_path):
            if '(7)' in end_path:
                return subdivision_nodes['(7)']
            elif '(8)' in end_path:
                return subdivision_nodes['(8)']
            elif '(9)' in end_path:
                return subdivision_nodes['(9)']
            return None

        mock_db.find_node_by_start_end_path = AsyncMock(side_effect=mock_find_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test text with a list of subdivisions
        text = "Section 501.035(b)(7), (8), or (9)"

        # Process the text
        references = await resolver.process_text(text)

        # Verify that we got at least one reference
        assert len(references) > 0, f"No references found for text: {text}"

        # Find the reference with our expected pattern
        section_subsection_subdivision_list_refs = [ref for ref in references if ref['pattern'] == 'section_subsection_subdivision_list']
        assert len(section_subsection_subdivision_list_refs) > 0, f"Pattern 'section_subsection_subdivision_list' must be one of the matches for text: '{text}'"

        # Verify the section_subsection_subdivision_list reference has correct properties
        ref = section_subsection_subdivision_list_refs[0]
        assert ref['text'] == text
        assert ref['startOffset'] == 0
        assert ref['endOffset'] == len(text)

        # Verify matches - should have 3 matches (one for each subdivision)
        assert len(ref['matches']) == 3

        # Check first match (full section with first subdivision)
        assert ref['matches'][0]['text'] == "Section 501.035(b)(7)"
        assert ref['matches'][0]['target'] == subdivision_nodes['(7)']['nodeId']

        # Check second match (just the subdivision)
        assert ref['matches'][1]['text'] == "(8)"
        assert ref['matches'][1]['target'] == subdivision_nodes['(8)']['nodeId']

        # Check third match (just the subdivision)
        assert ref['matches'][2]['text'] == "(9)"
        assert ref['matches'][2]['target'] == subdivision_nodes['(9)']['nodeId']

        # Verify database calls were made for each subdivision
        expected_calls = [
            (('/collection/tx/code/gv', '/section/501.035(b)(7)'),),
            (('/collection/tx/code/gv', '/section/501.035(b)(8)'),),
            (('/collection/tx/code/gv', '/section/501.035(b)(9)'),)
        ]

        for call in expected_calls:
            assert call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_process_text_external_section_subsection_subdivision_list(self, mock_db, mock_node):
        # Create mock subdivision nodes based on the mock_node but for the Election Code
        subdivision_nodes = {
            '(7)': {
                **mock_node,
                'nodeId': '/collection/tx/code/el/chapter/1/section/501.035(b)(7)',
                'type': 'subdivision',
                'id': '(7)'
            },
            '(8)': {
                **mock_node,
                'nodeId': '/collection/tx/code/el/chapter/1/section/501.035(b)(8)',
                'type': 'subdivision',
                'id': '(8)'
            },
            '(9)': {
                **mock_node,
                'nodeId': '/collection/tx/code/el/chapter/1/section/501.035(b)(9)',
                'type': 'subdivision',
                'id': '(9)'
            }
        }

        # Setup mock to return appropriate node based on path
        async def mock_find_node(start_path, end_path):
            # Check if we're looking in the Election Code
            if '/code/el' in start_path:
                if '(7)' in end_path:
                    return subdivision_nodes['(7)']
                elif '(8)' in end_path:
                    return subdivision_nodes['(8)']
                elif '(9)' in end_path:
                    return subdivision_nodes['(9)']
            return None

        mock_db.find_node_by_start_end_path = AsyncMock(side_effect=mock_find_node)
        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test text with a list of subdivisions and an external code reference
        text = "Section 501.035(b)(7), (8), or (9), Election Code"

        # Process the text
        references = await resolver.process_text(text)

        # Verify that we got at least one reference
        assert len(references) > 0, f"No references found for text: {text}"

        # Find the reference with our expected pattern
        external_section_subsection_subdivision_list_refs = [ref for ref in references if ref['pattern'] == 'external_section_subsection_subdivision_list']
        assert len(external_section_subsection_subdivision_list_refs) > 0, f"Pattern 'external_section_subsection_subdivision_list' must be one of the matches for text: '{text}'"

        # Verify the external_section_subsection_subdivision_list reference has correct properties
        ref = external_section_subsection_subdivision_list_refs[0]
        assert ref['text'] == text
        assert ref['startOffset'] == 0
        assert ref['endOffset'] == len(text)

        # Verify matches - should have 3 matches (one for each subdivision)
        assert len(ref['matches']) == 3

        # Check first match (full section with first subdivision)
        assert ref['matches'][0]['text'] == "Section 501.035(b)(7)"
        assert ref['matches'][0]['target'] == subdivision_nodes['(7)']['nodeId']

        # Check second match (just the subdivision)
        assert ref['matches'][1]['text'] == "(8)"
        assert ref['matches'][1]['target'] == subdivision_nodes['(8)']['nodeId']

        # Check third match (just the subdivision)
        assert ref['matches'][2]['text'] == "(9)"
        assert ref['matches'][2]['target'] == subdivision_nodes['(9)']['nodeId']

        # Verify database calls were made for each subdivision in the Election Code
        expected_calls = [
            (('/collection/tx/code/el', '/section/501.035(b)(7)'),),
            (('/collection/tx/code/el', '/section/501.035(b)(8)'),),
            (('/collection/tx/code/el', '/section/501.035(b)(9)'),)
        ]

        for call in expected_calls:
            assert call in mock_db.find_node_by_start_end_path.call_args_list

    @pytest.mark.asyncio
    async def test_process_text_subdivision_list(self, mock_db, mock_node):
        # Create mock subdivision nodes based on the mock_node
        subdivision_nodes = {
            '(1)': {
                **mock_node,
                'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(a)(1)',
                'type': 'subdivision',
                'id': '(1)'
            },
            '(2)': {
                **mock_node,
                'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(a)(2)',
                'type': 'subdivision',
                'id': '(2)'
            },
            '(3)': {
                **mock_node,
                'nodeId': '/collection/tx/code/gv/chapter/1/section/1.001(a)(3)',
                'type': 'subdivision',
                'id': '(3)'
            }
        }

        # Setup mock to return appropriate node based on path
        async def mock_find_node(start_path, end_path):
            if '/section/1.001(a)(1)' in end_path:
                return subdivision_nodes['(1)']
            elif '/section/1.001(a)(2)' in end_path:
                return subdivision_nodes['(2)']
            elif '/section/1.001(a)(3)' in end_path:
                return subdivision_nodes['(3)']
            return None

        mock_db.find_node_by_start_end_path = AsyncMock(side_effect=mock_find_node)

        # Add section and subsection to hierarchy
        mock_node['hierarchy'] = [
            {'type': 'collection', 'id': 'tx'},
            {'type': 'code', 'id': 'gv'},
            {'type': 'chapter', 'id': '1'},
            {'type': 'section', 'id': '1.001'},
            {'type': 'subsection', 'id': '(a)'}
        ]

        resolver = TexasCodeResolver(mock_node, mock_db)

        # Test cases for subdivision lists
        test_cases = [
            "Subdivisions (1) and (2)",
            "Subdivisions (1), (2), and (3)",
            "Subdivisions (1) through (3)",
            "Subdivision (1) or (2)",
            "Subdivision (1), (2), or (3)"
        ]

        for text in test_cases:
            # Reset mock for each test case
            mock_db.find_node_by_start_end_path.reset_mock()

            # Process the text
            references = await resolver.process_text(text)

            # Verify that we got at least one reference
            assert len(references) > 0, f"No references found for text: {text}"

            # Find the reference with our expected pattern
            subdivision_list_refs = [ref for ref in references if ref['pattern'] == 'subdivision_list']
            assert len(subdivision_list_refs) > 0, f"Pattern 'subdivision_list' must be one of the matches for text: '{text}'"

            # Verify the subdivision_list reference has correct properties
            ref = subdivision_list_refs[0]
            assert ref['text'] == text
            assert ref['startOffset'] == 0
            assert ref['endOffset'] == len(text)

            # Verify matches - should have at least one match
            assert len(ref['matches']) > 0

            # Check first match - should contain "(1)" regardless of singular/plural form
            first_match = ref['matches'][0]
            assert "(1)" in first_match['text'], f"First match should contain '(1)' but got: {first_match['text']}"
            assert first_match['target'] == subdivision_nodes['(1)']['nodeId']

            # If there are multiple subdivisions, check additional matches
            if "(2)" in text:
                # Find the match for the second subdivision
                second_subdivision_matches = [m for m in ref['matches'] if "(2)" in m['text']]
                assert len(second_subdivision_matches) > 0, "No match found for subdivision (2)"
                assert second_subdivision_matches[0]['target'] == subdivision_nodes['(2)']['nodeId']

            if "(3)" in text:
                # Find the match for the third subdivision
                third_subdivision_matches = [m for m in ref['matches'] if "(3)" in m['text']]
                assert len(third_subdivision_matches) > 0, "No match found for subdivision (3)"
                assert third_subdivision_matches[0]['target'] == subdivision_nodes['(3)']['nodeId']

            # Verify database calls were made for each subdivision
            expected_calls = []
            if "(1)" in text:
                expected_calls.append((('/collection/tx/code/gv', '/section/1.001(a)(1)'),))
            if "(2)" in text:
                expected_calls.append((('/collection/tx/code/gv', '/section/1.001(a)(2)'),))
            if "(3)" in text:
                expected_calls.append((('/collection/tx/code/gv', '/section/1.001(a)(3)'),))

            for call in expected_calls:
                assert call in mock_db.find_node_by_start_end_path.call_args_list
