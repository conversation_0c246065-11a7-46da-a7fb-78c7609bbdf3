from typing import Dict, Any, Type
from .resolver import BaseReferenceResolver
from .texas_constitution_resolver import TexasConstitutionResolver
from .texas_code_resolver import TexasCodeResolver
from .db import MongoDB

class ResolverFactory:
    # Map of code types to resolver classes
    RESOLVER_MAP = {
        'cn': TexasConstitutionResolver,  # Constitution
        'ag': TexasCodeResolver,  # Agriculture Code
        'al': TexasCodeResolver,  # Alcoholic Beverage Code
        'bc': TexasCodeResolver,  # Business and Commerce Code
        'bo': TexasCodeResolver,  # Business Organizations Code
        'cp': TexasCodeResolver,  # Civil Practice and Remedies Code
        'cr': TexasCodeResolver,  # Code of Criminal Procedure
        'ed': TexasCodeResolver,  # Education Code
        'el': TexasCodeResolver,  # Election Code
        'es': TexasCodeResolver,  # Estates Code
        'fa': TexasCodeResolver,  # Family Code
        'fi': TexasCodeResolver,  # Finance Code
        'gv': TexasCodeResolver,  # Government Code
        'hs': TexasCodeResolver,  # Health and Safety Code
        'hr': TexasCodeResolver,  # Human Resources Code
        'in': TexasCodeResolver,  # Insurance Code
        'la': TexasCodeResolver,  # Labor Code
        'lg': TexasCodeResolver,  # Local Government Code
        'nr': TexasCodeResolver,  # Natural Resources Code
        'oc': TexasCodeResolver,  # Occupations Code
        'pe': TexasCodeResolver,  # Penal Code
        'pr': TexasCodeResolver,  # Property Code
        'sd': TexasCodeResolver,  # Special District Local Laws Code
        'tx': TexasCodeResolver,  # Tax Code
        'tn': TexasCodeResolver,  # Transportation Code
        'ut': TexasCodeResolver,  # Utilities Code
        'wa': TexasCodeResolver,  # Water Code
        'wl': TexasCodeResolver,  # Auxiliary Water Laws
    }

    @classmethod
    def create_resolver(cls, node: Dict[str, Any], db: MongoDB) -> BaseReferenceResolver:
        """
        Create and return the appropriate resolver based on the node's code type.

        Args:
            node: The statute node to create a resolver for
            db: MongoDB instance for database operations

        Returns:
            An instance of the appropriate resolver class

        Raises:
            ValueError: If no resolver is found for the given code type
        """
        code = node.get('code')
        if not code:
            raise ValueError(f"Node has no code type: {node.get('nodeId')}")

        resolver_class = cls.RESOLVER_MAP.get(code.lower())
        if not resolver_class:
            raise ValueError(f"No resolver found for code type: {code}")

        return resolver_class(node, db)
