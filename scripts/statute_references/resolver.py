from typing import List, Dict, Any, Optional
import re
from .patterns import ReferencePatterns
from .db import MongoDB
import logging

class BaseReferenceResolver:
    def __init__(self, node: Dict[str, Any], db: MongoDB):
        self.node = node
        self.db = db
        self.reference_patterns = ReferencePatterns()
        self.patterns = []

    async def find_references(self, text: str) -> List[Dict[str, Any]]:
        """Find all references in the given text"""
        if not text:
            return []

        references = []
        for pattern_info in self.patterns:
            pattern = pattern_info['pattern']
            build_link = pattern_info['build_link']
            name = pattern_info.get('name', '')

            # Find all matches for this pattern
            for match in pattern.finditer(text):
                if pattern_info.get('debug', False):
                    logging.debug(f"\nDEBUG pattern match: {name}")
                    logging.debug(f"matched text: '{match.group(0)}'")
                    logging.debug(f"groups: {match.groups()}")
                    logging.debug(f"offset: {match.start()}")

                try:
                    # Get the parameter names from the build_link function
                    import inspect
                    sig = inspect.signature(build_link)
                    param_names = list(sig.parameters.keys())

                    # Build the arguments dictionary
                    args = {}
                    groups = match.groups()
                    for i, group in enumerate(groups):
                        if i + 1 < len(param_names):  # +1 because matched_text is first
                            args[param_names[i + 1]] = group

                    # Build reference from the match
                    reference = await build_link(
                        matched_text=match.group(0),
                        offset=match.start(),
                        **args
                    )

                    if reference:
                        # Add pattern name to reference
                        reference['pattern'] = name
                        reference['nodeId'] = self.node.get('nodeId')

                        # Update status for all matches
                        for match in reference.get('matches', []):
                            match['status'] = 'resolved' if match.get('target') else 'unresolved'

                        references.append(reference)
                except Exception as e:
                    logging.error(f"Error building link for match: {name}, {match.group(0)}, {e}")

        # Debug logging
        if any(p.get('debug', False) for p in self.patterns):
            logging.debug("\nDEBUG all references before filtering:")
            for ref in references:
                logging.debug(f"pattern: {ref.get('pattern')}")
                logging.debug(f"text: '{ref.get('text')}'")
                logging.debug(f"matches: {ref.get('matches')}\n")

        # Sort references by position and filter overlapping ones
        references = self._sort_and_filter_references(references)

        if any(p.get('debug', False) for p in self.patterns):
            logging.debug("\nDEBUG all references after filtering:")
            for ref in references:
                logging.debug(f"pattern: {ref.get('pattern')}")
                logging.debug(f"text: '{ref.get('text')}'")
                logging.debug(f"matches: {ref.get('matches')}\n")

        return references

    def _sort_and_filter_references(self, references: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Sort references by position and filter overlapping ones"""
        if not references:
            return []

        # Sort by multiple criteria:
        # 1. Number of matches (most first)
        # 2. Total match text length (longest first)
        # 3. Overall text length (longest first)
        # 4. Number of resolved matches (most first)
        # 5. Position (earliest first)
        def sort_key(ref):
            matches = ref.get('matches', [])
            total_match_length = sum(len(m.get('text', '')) for m in matches)
            resolved_matches = len([m for m in matches if m.get('target') and m.get('status') != 'unresolved'])
            return (
                -len(matches),  # Negative for descending order
                -total_match_length,  # Negative for descending order
                -len(ref.get('text', '')),  # Negative for descending order
                -resolved_matches,  # Negative for descending order
                ref.get('startOffset', 0)
            )

        references.sort(key=sort_key)

        # Filter out overlapping references, keeping the higher priority ones
        filtered = []
        for ref in references:
            start = ref.get('startOffset', 0)
            end = ref.get('endOffset', 0)

            # Check if this reference overlaps with any higher priority reference
            overlaps = any(
                prev_ref.get('startOffset', 0) < end and
                start < prev_ref.get('endOffset', 0)
                for prev_ref in filtered
            )

            if not overlaps:
                filtered.append(ref)

        # Resort by position for final output
        filtered.sort(key=lambda r: r.get('startOffset', 0))

        return filtered

    def add_patterns(self, patterns: List[Dict[str, Any]]):
        """Add a new patterns to the resolver"""
        self.patterns.extend(patterns)

    def add_pattern(self, pattern: Dict[str, Any]):
        """Add a new pattern to the resolver"""
        self.patterns.append(pattern)

    async def process_text(self, text: str) -> List[Dict[str, Any]]:
        """Process text and find all references"""
        if not text:
            return []

        try:
            references = await self.find_references(text)
            return references
        except Exception as e:
            logging.error(f"Error in process_text: {e}")
            return []
