from typing import Optional, Dict, Any, List, Callable, Awaitable
from .resolver import BaseReferenceResolver
from .db import MongoDB
from .patterns import ReferencePatterns
import re
import logging

class TexasConstitutionResolver(BaseReferenceResolver):
    def __init__(self, node: Dict[str, Any], db: MongoDB):
        super().__init__(node, db)
        self.initialize_patterns()

    # ================================
    # find_node methods
    # ================================

    async def find_article_node(self, article_number: str) -> Optional[Dict[str, Any]]:
        """Find an article node by its number"""
        article_num = self.reference_patterns.get_article_number(article_number)
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/article/{article_num}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_section_node(self, section_number: str, article_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Find a section node by its number and optional article"""
        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"

        # If article is provided, try that path first
        if article_number:
            article_num = self.reference_patterns.get_article_number(article_number)
            end_path = f"/article/{article_num}/section/{section_number}"
            node = await self.db.find_node_by_start_end_path(start_path, end_path)
            if node:
                return node

        # If no article specified or not found in specified article, try direct section path
        end_path = f"/section/{section_number}"
        node = await self.db.find_node_by_start_end_path(start_path, end_path)
        if node:
            return node

        # If not found, try current article context
        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        if article_node:
            end_path = f"/article/{article_node['id']}/section/{section_number}"
            return await self.db.find_node_by_start_end_path(start_path, end_path)

        return None

    async def find_subsection_node(self, section_number: str, subsection: str, article_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Find a subsection node by its section number, subsection, and optional article"""
        section_node = await self.find_section_node(section_number, article_number)
        if not section_node:
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{section_number}{subsection}"
        if article_number:
            article_num = self.reference_patterns.get_article_number(article_number)
            end_path = f"/article/{article_num}{end_path}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_subsection_in_current_section(self, subsection: str) -> Optional[Dict[str, Any]]:
        """Find a subsection in the current section"""
        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        if not context_node:
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{context_node['id']}{subsection}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_subdivision_in_current_subsection(self, subdivision: str) -> Optional[Dict[str, Any]]:
        """Find a subdivision in the current subsection"""
        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'subsection'), None)
        if not context_node:
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"{context_node['nodeId'].split('/')[-1]}{subdivision}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    async def find_subdivision_in_subsection(self, subdivision: str, subsection: str) -> Optional[Dict[str, Any]]:
        """Find a subdivision in a specific subsection"""
        context_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'section'), None)
        if not context_node:
            return None

        start_path = f"/collection/{self.node['collection']}/code/{self.node['code']}"
        end_path = f"/section/{context_node['id']}{subsection}{subdivision}"
        return await self.db.find_node_by_start_end_path(start_path, end_path)

    # ================================
    # build_link methods
    # ================================

    async def build_section_list(self, matched_text, first_ref, first_section, rest_of_refs, last_section, offset) -> Dict[str, Any]:
        logging.debug("\nDEBUG build_section_list match groups:")
        logging.debug(f"matched_text: '{matched_text}'")
        logging.debug(f"first_ref: '{first_ref}'")
        logging.debug(f"first_section: '{first_section}'")
        logging.debug(f"rest_of_refs: '{rest_of_refs}'")
        logging.debug(f"last_section: '{last_section}'")
        logging.debug(f"offset: {offset}")

        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        article_id = article_node['id'] if article_node else None
        target_node1 = await self.find_section_node(first_section, article_id)
        matches = [{
            'text': first_ref,
            'target': target_node1['nodeId'] if target_node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(first_ref),
            'status': None if target_node1 else 'unresolved'
        }]

        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.CN_SECTION_NUMBER.finditer(rest_of_refs):
                section = match.group(0)
                section_pos = offset + rest_of_refs_pos + match.start()
                target_node = await self.find_section_node(section, article_id)
                matches.append({
                    'text': section,
                    'target': target_node['nodeId'] if target_node else None,
                    'startOffset': section_pos,
                    'endOffset': section_pos + len(section),
                    'status': None if target_node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_section_article(self, matched_text, section, article, offset) -> Dict[str, Any]:
        target_node = await self.find_section_node(section, article)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_article_section(self, matched_text, article, section, offset) -> Dict[str, Any]:
        article_num = self.reference_patterns.get_article_number(article)
        target_node = await self.find_section_node(section, article_num)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_section_subsection(self, matched_text, section, subsection, offset) -> Dict[str, Any]:
        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        article_id = article_node['id'] if article_node else None
        target_node = await self.find_subsection_node(section, subsection, article_id)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_subsection_list(self, matched_text, first_ref, first_subsection, rest_of_refs, last_subsection, section, offset) -> Dict[str, Any]:
        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        article_id = article_node['id'] if article_node else None
        target_node1 = await self.find_subsection_node(section, first_subsection, article_id)
        matches = [{
            'text': first_ref,
            'target': target_node1['nodeId'] if target_node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(first_ref),
            'status': None if target_node1 else 'unresolved'
        }]

        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.SUBSECTION.finditer(rest_of_refs):
                subsection = match.group(0)
                subsection_pos = offset + rest_of_refs_pos + match.start()
                target_node = await self.find_subsection_node(section, subsection, article_id)
                matches.append({
                    'text': subsection,
                    'target': target_node['nodeId'] if target_node else None,
                    'startOffset': subsection_pos,
                    'endOffset': subsection_pos + len(subsection),
                    'status': None if target_node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_multiple_sections(self, matched_text, first_ref, section1, section2, offset) -> Dict[str, Any]:
        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        article_id = article_node['id'] if article_node else None
        target_node1 = await self.find_section_node(section1, article_id)
        target_node2 = await self.find_section_node(section2, article_id)
        offset2 = offset + matched_text.index(section2)

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [
                {
                    'text': first_ref,
                    'target': target_node1['nodeId'] if target_node1 else None,
                    'startOffset': offset,
                    'endOffset': offset + len(first_ref),
                    'status': None if target_node1 else 'unresolved'
                },
                {
                    'text': section2,
                    'target': target_node2['nodeId'] if target_node2 else None,
                    'startOffset': offset2,
                    'endOffset': offset2 + len(section2),
                    'status': None if target_node2 else 'unresolved'
                }
            ]
        }

    async def build_section_article_list(self, matched_text, first_ref, section1, rest_of_refs, last_section, article, offset) -> Dict[str, Any]:
        article_num = self.reference_patterns.get_article_number(article)
        article_node = await self.find_article_node(article_num)
        if not article_node:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        target_node1 = await self.find_section_node(section1, article_num)
        matches = [{
            'text': first_ref,
            'target': target_node1['nodeId'] if target_node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(first_ref),
            'status': None if target_node1 else 'unresolved'
        }]

        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.CN_SECTION_NUMBER.finditer(rest_of_refs):
                section = match.group(0)
                section_pos = offset + rest_of_refs_pos + match.start()
                target_node = await self.find_section_node(section, article_num)
                matches.append({
                    'text': section,
                    'target': target_node['nodeId'] if target_node else None,
                    'startOffset': section_pos,
                    'endOffset': section_pos + len(section),
                    'status': None if target_node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_internal_section(self, matched_text, section, offset) -> Dict[str, Any]:
        """Build a reference for an internal section, handling both with and without 'of this article'"""
        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        article_id = article_node['id'] if article_node else None

        # If we can't find an article in the hierarchy, try to get it from the node ID
        if not article_id:
            import re
            match = re.search(r'/article/([^/]+)', self.node['nodeId'])
            article_id = match.group(1) if match else None

        if not article_id:
            return {
                'text': matched_text,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'matches': [{
                    'text': matched_text,
                    'startOffset': offset,
                    'endOffset': offset + len(matched_text),
                    'status': 'unresolved'
                }]
            }

        target_node = await self.find_section_node(section, article_id)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_subsection_with_section(self, matched_text, subsection, section, offset) -> Dict[str, Any]:
        article_node = next((n for n in self.node.get('hierarchy', []) if n.get('type') == 'article'), None)
        article_id = article_node['id'] if article_node else None
        target_node = await self.find_subsection_node(section, subsection, article_id)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_internal_subsection(self, matched_text, subsection, offset) -> Dict[str, Any]:
        target_node = await self.find_subsection_in_current_section(subsection)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    async def build_subdivision_subsection_list(self, matched_text, first_ref, subdivision1, rest_of_refs, last_subdivision, subsection, offset) -> Dict[str, Any]:
        target_node1 = await self.find_subdivision_in_subsection(subdivision1, subsection)
        matches = [{
            'text': first_ref,
            'target': target_node1['nodeId'] if target_node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(first_ref),
            'status': None if target_node1 else 'unresolved'
        }]

        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.SUBDIVISION.finditer(rest_of_refs):
                subdivision = match.group(0)
                subdivision_pos = offset + rest_of_refs_pos + match.start()
                target_node = await self.find_subdivision_in_subsection(subdivision, subsection)
                matches.append({
                    'text': subdivision,
                    'target': target_node['nodeId'] if target_node else None,
                    'startOffset': subdivision_pos,
                    'endOffset': subdivision_pos + len(subdivision),
                    'status': None if target_node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_subdivision_list(self, matched_text, first_ref, subdivision1, rest_of_refs, last_subdivision, offset) -> Dict[str, Any]:
        target_node1 = await self.find_subdivision_in_current_subsection(subdivision1)
        matches = [{
            'text': first_ref,
            'target': target_node1['nodeId'] if target_node1 else None,
            'startOffset': offset,
            'endOffset': offset + len(first_ref),
            'status': None if target_node1 else 'unresolved'
        }]

        if rest_of_refs:
            rest_of_refs_pos = matched_text.index(rest_of_refs)
            for match in self.reference_patterns.SUBDIVISION.finditer(rest_of_refs):
                subdivision = match.group(0)
                subdivision_pos = offset + rest_of_refs_pos + match.start()
                target_node = await self.find_subdivision_in_current_subsection(subdivision)
                matches.append({
                    'text': subdivision,
                    'target': target_node['nodeId'] if target_node else None,
                    'startOffset': subdivision_pos,
                    'endOffset': subdivision_pos + len(subdivision),
                    'status': None if target_node else 'unresolved'
                })

        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': matches
        }

    async def build_subdivision(self, matched_text, subdivision, offset) -> Dict[str, Any]:
        target_node = await self.find_subdivision_in_current_subsection(subdivision)
        return {
            'text': matched_text,
            'startOffset': offset,
            'endOffset': offset + len(matched_text),
            'matches': [{
                'text': matched_text,
                'target': target_node['nodeId'] if target_node else None,
                'startOffset': offset,
                'endOffset': offset + len(matched_text),
                'status': None if target_node else 'unresolved'
            }]
        }

    # ================================
    # initialize_patterns
    # ================================

    def initialize_patterns(self):
        """Initialize all the reference patterns"""
        patterns = [
            # Internal section
            # Example: "Section 1-1"
            # Example: "Section 1-1 of this article"
            {
                'name': 'internal_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER,
                    re.compile(r'(?:\s+of\s+(?:(?:the|this)\s+)?article)?')  # Optional "of this article" as a single group
                ),
                'build_link': self.build_internal_section
            },
            # Article section
            # Example: "Article 1, Section 1-1"
            {
                'name': 'article_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.ARTICLE_PREFIX,
                    self.reference_patterns.ARTICLE_NUMBER,
                    self.reference_patterns.COMMA_AND_SPACE,
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER
                ),
                'build_link': self.build_article_section
            },
            # Section subsection
            # Example: "Section 1-1(a)"
            {
                'name': 'section_subsection',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER,
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_section_subsection
            },
            # Section article
            # Example: "Section 1-1 of Article 1"
            {
                'name': 'section_article',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER,
                    self.reference_patterns.SECTION_CONNECTOR,
                    self.reference_patterns.ARTICLE_PREFIX,
                    self.reference_patterns.ARTICLE_NUMBER
                ),
                'build_link': self.build_section_article
            },
            # Section list
            # Example: "Sections 1-1, 1-2, and 1-3"
            {
                'name': 'section_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER
                ),
                'build_link': self.build_section_list
            },
            # Multiple sections with and/or/through
            # Example: "Sections 1-1, 1-2, and 1-3"
            {
                'name': 'multiple_sections_with_and_or_through',
                'pattern': self.reference_patterns.COMBINE(
                    [self.reference_patterns.SECTION_PREFIX, self.reference_patterns.CN_SECTION_NUMBER],
                    self.reference_patterns.AND_OR_THROUGH,
                    self.reference_patterns.CN_SECTION_NUMBER
                ),
                'build_link': self.build_multiple_sections
            },
            # Section article list
            # Example: "Section 1-1, 1-2, and 1-3 of Article 1"
            {
                'name': 'section_article_list',
                'pattern': self.reference_patterns.LIST_OF_WITH_SUFFIX(
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER,
                    self.reference_patterns.COMMA_AND_SPACE,
                    self.reference_patterns.COMBINE(
                        self.reference_patterns.ARTICLE_PREFIX,
                        self.reference_patterns.ARTICLE_NUMBER
                    )
                ),
                'build_link': self.build_section_article_list
            },
            # Internal subsection
            # Example: "Subsection (a)"
            {
                'name': 'internal_subsection',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_internal_subsection
            },
            # Subsection with section
            # Example: "Subsection (a) of Section 1-1"
            {
                'name': 'subsection_with_section',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION,
                    self.reference_patterns.OF_THE_THIS,
                    self.reference_patterns.SECTION_PREFIX,
                    self.reference_patterns.CN_SECTION_NUMBER
                ),
                'build_link': self.build_subsection_with_section
            },
            # Subsection list
            # Example: "Subsections (a), (b), and (c)"
            {
                'name': 'subsection_list',
                'pattern': self.reference_patterns.LIST_OF_WITH_SUFFIX(
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION,
                    self.reference_patterns.OF_THE_THIS,
                    self.reference_patterns.COMBINE(
                        self.reference_patterns.SECTION_PREFIX,
                        self.reference_patterns.CN_SECTION_NUMBER
                    )
                ),
                'build_link': self.build_subsection_list
            },
            # Subdivision subsection list
            # Example: "Subdivisions (1), (2), and (3) of Subsection (a)"
            {
                'name': 'subdivision_subsection_list',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.LIST_OF(
                        self.reference_patterns.SUBDIVISION_PREFIX,
                        self.reference_patterns.SUBDIVISION
                    ),
                    self.reference_patterns.OF_THE_THIS,
                    self.reference_patterns.SUBSECTION_PREFIX,
                    self.reference_patterns.SUBSECTION
                ),
                'build_link': self.build_subdivision_subsection_list
            },
            # Subdivision list
            # Example: "Subdivisions (1), (2), and (3)"
            {
                'name': 'subdivision_list',
                'pattern': self.reference_patterns.LIST_OF(
                    self.reference_patterns.SUBDIVISION_PREFIX,
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_subdivision_list
            },
            # Subdivision
            # Example: "Subdivision (1)"
            {
                'name': 'subdivision',
                'pattern': self.reference_patterns.COMBINE(
                    self.reference_patterns.SUBDIVISION_PREFIX,
                    self.reference_patterns.SUBDIVISION
                ),
                'build_link': self.build_subdivision
            }
        ]

        # Register each pattern with the resolver
        self.add_patterns(patterns)
