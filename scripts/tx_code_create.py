#!/usr/bin/env python3

import json
import re
import logging
import traceback
from pathlib import Path
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional, Type, Iterator
import argparse
from enum import Enum, auto

# Import code configuration - all keys should be lowercase
CODE_CONFIG = {
    'al': 'Alcoholic Beverage Code',
    'ag': 'Agriculture Code',
    'wl': 'Auxiliary Water Laws',
    'bc': 'Business and Commerce Code',
    'bo': 'Business Organizations Code',
    'cp': 'Civil Practice and Remedies Code',
    'cr': 'Code of Criminal Procedure',
    'ed': 'Education Code',
    'el': 'Election Code',
    'es': 'Estates Code',
    'fa': 'Family Code',
    'fi': 'Finance Code',
    'gv': 'Government Code',
    'hs': 'Health and Safety Code',
    'hr': 'Human Resources Code',
    'in': 'Insurance Code',
    'i1': 'Insurance Code - Not Codified',
    'la': 'Labor Code',
    'lg': 'Local Government Code',
    'nr': 'Natural Resources Code',
    'oc': 'Occupations Code',
    'pw': 'Parks and Wildlife Code',
    'pe': 'Penal Code',
    'pr': 'Property Code',
    'sd': 'Special District Local Laws Code',
    'tx': 'Tax Code',
    'cn': 'Texas Constitution',
    'tn': 'Transportation Code',
    'ut': 'Utilities Code',
    'cv': "Vernon's Civil Statutes",
    'wa': 'Water Code',
}

class NodeType(Enum):
    CODE = 'code'
    TITLE = 'title'
    SUBTITLE = 'subtitle'
    CHAPTER = 'chapter'
    SUBCHAPTER = 'subchapter'
    SECTION = 'section'
    SUBSECTION = 'subsection'
    SUBDIVISION = 'subdivision'
    SUBPARAGRAPH = 'subparagraph'
    SUBITEM = 'subitem'
    SUBPART = 'subpart'

    ARTICLE = 'article'
    AMENDMENT = 'amendment'
    PRE_AMENDMENT = 'pre_amendment'

    def __str__(self):
        return self.value

    @property
    def plural(self) -> str:
        """Returns the plural form of the node type"""
        # Define plural forms for each node type
        plurals = {
            NodeType.CODE: 'codes',
            NodeType.TITLE: 'titles',
            NodeType.SUBTITLE: 'subtitles',
            NodeType.CHAPTER: 'chapters',
            NodeType.SUBCHAPTER: 'subchapters',
            NodeType.ARTICLE: 'articles',
            NodeType.SECTION: 'sections',
            NodeType.SUBSECTION: 'subsections',
            NodeType.SUBDIVISION: 'subdivisions',
            NodeType.SUBPARAGRAPH: 'subparagraphs',
            NodeType.SUBITEM: 'subitems',
            NodeType.SUBPART: 'subparts',
            NodeType.AMENDMENT: 'amendments',
            NodeType.PRE_AMENDMENT: 'pre_amendments',
        }
        return plurals[self]

    @classmethod
    def from_str(cls, value: str) -> 'NodeType':
        try:
            return cls(value.lower())
        except ValueError:
            raise ValueError(f"'{value}' is not a valid NodeType")


class TexasCodeParser:
    """Base parser for Texas legislative codes

    This parser processes HTML files containing Texas legislative codes and builds
    a hierarchical structure representing the code organization (titles, chapters,
    sections, etc.).

    Args:
        base_path: Path to directory containing HTML files for a specific code

    Attributes:
        base_path: Path to the code's HTML files
        code: Two-letter code identifier (e.g., 'ag' for Agriculture Code)
        parser: HierarchyParser instance that manages the node hierarchy

    Note:
        Expects HTML files to follow Texas Legislature Online format and naming conventions.
        File names should start with the code identifier (e.g., 'ag.1.htm' for Agriculture Code)
    """
    def __init__(self, base_path: Path):
        self.base_path = base_path
        self.logger = logging.getLogger(__name__)
        self.amendment_patterns = (
            'Acts ',
            'Added by',
            'Amended by',
            'Renumbered from',
            'Text of section as amended by',
            'For text of section as amended by',
            'Prior law',
            'Reenacted and amended by',
            'Transferred',
            'Redesignated from',
            'Redesignated by',
            'Reenacted by',
        )
        self.pre_amendment_patterns = (
            'added by Acts',
            'Repealed by Acts',
            'repealed by Acts',
            'amended by Acts',
            'as described by Acts',
            'In accordance with Acts',
            'For expiration of this chapter',
            'For contingent expiration of this chapter',
            'this article was repealed',
            'this article were repealed',
            'this chapter was repealed by',
            'this subchapter was repealed by',
            'this section was repealed by',
            'this subsection was repealed by',
            'this paragraph was repealed by',
            'this subdivision was repealed by',
            'Text of chapter effective ',
            'Text of chapter heading effective ',
            'Text of chapter as added by',
            'Text of chapter as amended by',
            'Text of chapter as repealed by',
            'Text of subchapter effective ',
            'Text of subchapter heading effective ',
            'Text of subchapter as added by',
            'Text of subchapter as amended by',
            'Text of subchapter as repealed by',
            'Text of section effective ',
            'Text of section heading effective ',
            'Text of section as added by',
            'Text of section as amended by',
            'Text of section as repealed by',
            'Text of subsection effective ',
            'Text of subsection heading effective ',
            'Text of subsection as added by',
            'Text of subsection as amended by',
            'Text of subsection as repealed by',
            'Text of paragraph effective ',
            'Text of paragraph heading effective ',
            'Text of paragraph as added by',
            'Text of paragraph as amended by',
            'Text of paragraph as repealed by',
            'Text of subdivision effective ',
            'Text of subdivision heading effective ',
            'Text of subdivision as added by',
            'Text of subdivision as amended by',
            'Text of subdivision as repealed by',
        )
        self.extraction_patterns = {
            NodeType.CODE: f'(?i)^({"|".join(re.escape(name) for name in CODE_CONFIG.values())})$',
            NodeType.TITLE: r'(?i)^(TITLE\s+(\d+(?:-?[A-Z])?)\.\s+(.+))$',
            NodeType.SUBTITLE: r'(?i)^(SUBTITLE\s+([A-Z]+(?:\d+|-\d+)?)\.\s+(.+))$',
            NodeType.CHAPTER: r'(?i)^(CHAPTER\s+(\d+(?:[A-Z]|-[A-Z]|-\d+(?:\/\d+)?)?)\.\s+(.+))$',
            NodeType.SUBCHAPTER: r'(?i)^(SUBCHAPTER\s+([A-Z](?:-\d+)?)\.\s+(.+))$',
            NodeType.SECTION: r'^((?:Sec(?:tion)?|Art(?:icle)?)\.\s+(\d+[a-zA-Z]?\.\d+(?:\d*))\.?\s+([^.]+))\.?\s*(.*)$',
            NodeType.ARTICLE: r'^(?:(?:Art(?:icle)?)\.\s+)((\d+[a-zA-Z]?\.\d+(?:\d*))\.?\s+([^.]+))\.?\s*(.*)$',
            NodeType.SUBSECTION: r'^(\([a-z0-9A-Z-]+\)(?:\([a-z0-9A-Z-]+\))*)'  # Match nested IDs
        }

        # Define default hierarchy
        self.HIERARCHY = {
            NodeType.CODE: {
                'patterns': [self.extraction_patterns[NodeType.CODE]],
                'can_contain': [NodeType.TITLE]
            },
            NodeType.TITLE: {
                'patterns': [self.extraction_patterns[NodeType.TITLE]],
                'can_contain': [NodeType.SUBTITLE, NodeType.CHAPTER]
            },
            NodeType.SUBTITLE: {
                'patterns': [self.extraction_patterns[NodeType.SUBTITLE]],
                'can_contain': [NodeType.CHAPTER]
            },
            NodeType.CHAPTER: {
                'patterns': [self.extraction_patterns[NodeType.CHAPTER]],
                'can_contain': [NodeType.SUBCHAPTER, NodeType.SECTION]
            },
            NodeType.SUBCHAPTER: {
                'patterns': [self.extraction_patterns[NodeType.SUBCHAPTER]],
                'can_contain': [NodeType.SECTION]
            },
            NodeType.SECTION: {
                'patterns': [self.extraction_patterns[NodeType.SECTION]],
                'can_contain': [NodeType.SUBSECTION]
            },
            NodeType.SUBSECTION: {
                'patterns': [self.extraction_patterns[NodeType.SUBSECTION]],
                'can_contain': [None]
            },
            NodeType.AMENDMENT: {
                'patterns': [],
                'can_contain': [None]
            },
            NodeType.PRE_AMENDMENT: {
                'patterns': [],
                'can_contain': [None]
            }
        }

        self.code = self._determine_code()
        self.parser = HierarchyParser(self.code, self.HIERARCHY)


    def _determine_code(self) -> str:
        """Determine code from files in directory

        Returns:
            Two-letter code identifier

        Raises:
            ValueError: If no HTML files found or code cannot be determined
        """
        html_files = list(self.base_path.glob('*.htm'))
        if not html_files:
            raise ValueError(f"No HTML files found in {self.base_path}")

        first_file = html_files[0].name
        code_match = re.match(r'([a-z0-9]+)\.', first_file)
        if not code_match:
            raise ValueError(f"Unable to determine code from filename: {first_file}")

        code = code_match.group(1)
        if code not in CODE_CONFIG:
            raise ValueError(f"Unknown code: {code}")

        return code


    def _get_html_files(self) -> Iterator[tuple[float, str, Path]]:
        """Get sorted HTML files with chapter IDs

        Returns:
            Iterator of tuples containing (sort_key, chapter_id, file_path)
        """
        files = []
        for file_path in self.base_path.glob('*.htm'):
            # Skip index files
            if file_path.name.startswith('index.'):
                continue

            # Extract chapter ID from filename (e.g., 'es.001.htm' -> '001')
            match = re.search(r'[a-z]{2}\.([^.]+)\.htm', file_path.name)
            if not match:
                self.logger.warning(f"Skipping file with unexpected name format: {file_path.name}")
                continue

            chapter_id = match.group(1)

            try:
                # Try to extract a numeric value for sorting
                base_num = float(re.match(r'\d+', chapter_id).group())
                # Handle special cases where chapter has a letter suffix
                letter_suffix = re.search(r'[a-zA-Z]$', chapter_id)
                if letter_suffix:
                    # Add a small fraction based on letter position (a=0.1, b=0.2, etc.)
                    base_num += (ord(letter_suffix.group().lower()) - ord('a') + 1) / 10
            except (ValueError, AttributeError):
                # If we can't parse a number, use infinity to sort to end
                base_num = float('inf')
                self.logger.warning(f"Could not parse chapter number from {file_path.name}")

            files.append((base_num, chapter_id, file_path))

        return iter(sorted(files))


    def _extract_text_with_links(self, element) -> str:
        """Extract text from element, preserving links

        Args:
            element: BeautifulSoup element to extract text from

        Returns:
            String containing the extracted text with preserved link text
        """
        if element is None:
            return ""

        parts = []
        for child in element.children:
            if child.name == 'a':
                parts.append(child.get_text() or "")
            elif isinstance(child, str):
                parts.append(child)
        return ''.join(parts).strip().replace('  ', ' ')


    def _matches_pattern(self, text: str, node_type: NodeType) -> bool:
        """Check if text matches any pattern for the given node type

        Args:
            text: Text content to check against patterns
            node_type: Type of node to get patterns for ('title', 'chapter', etc.)

        Returns:
            Boolean indicating whether text matches any pattern for the node type
        """
        return any(re.match(pattern, text) for pattern in self.parser.HIERARCHY[node_type]['patterns'])


    def _get_indent_level(self, element) -> int:
        """Get subsection level from text-indent style and content

        Args:
            element: BeautifulSoup element to check for indentation

        Returns:
            Integer representing the indentation level (1-based)
        """
        if not element:
            return 1

        # First try to determine level from text-indent style
        style = element.get('style', '')
        indent_match = re.search(r'text-indent:\s*(\d+)ex', style)
        if indent_match:
            indent = int(indent_match.group(1))
            if indent >= 7:
                # Convert indent to level: (indent - 7) // 6 + 1
                return ((indent - 7) // 6) + 1

        # If no style indent, try to determine from content structure
        text = element.get_text().strip()
        if text:
            # Check for nested subsection markers
            if re.match(r'^\([A-Z]\)', text):  # (A), (B), etc.
                return 3
            elif re.match(r'^\(\d+\)', text):  # (1), (2), etc.
                return 2
            elif re.match(r'^\([a-z]\)', text):  # (a), (b), etc.
                return 1

        return 1  # Default to level 1


    def _create_node(self, node_type: NodeType, element=None) -> Optional[Dict[str, Any]]:
        """Create a node of the specified type from element"""

        text = element.get_text()
        if not text:
            return None

        # For other node types, use pattern matching
        node_pattern = self.extraction_patterns[node_type]
        node_match = re.search(node_pattern, text)

        if not node_match:
            self.logger.warning(f"Could not extract ID for {node_type} from: {text}")
            return None

        # Create the node
        name = node_match.group(1)
        id = node_match.group(2)
        node = {
            "text": name,
            "id": id,
            "type": node_type.value,
            "code": self.code,
        }

        return node

    def _handle_section(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a section node"""
        if not element:
            return None

        text = element.get_text()
        if not text:
            return None

        section_pattern = self.extraction_patterns[NodeType.SECTION]
        section_match = re.match(section_pattern, text)
        if not section_match:
            self.logger.warning(f"Section not identified in: {text}")
            return None

        section_name, section_id, _, subsection_text = section_match.groups()
        level = self._get_indent_level(element) if element else 1
        subsection = self._create_subsection(subsection_text, level)

        # Create the section node
        section = {
            "text": section_name,
            "id": section_id,
            "type": "section",
            "code": self.code,
        }

        if subsection:
            if 'subsections' not in section:
                section['subsections'] = []
            section['subsections'].append(subsection)

        section['amendment_history'] = []

        return section

    def _create_subsection(self, text: str, level: int) -> Dict[str, Any]:
        """Create a subsection node"""
        if not text.strip():
            return None

        # Get the correct subsection type (subsection, subdivision, subparagraph, subpart)
        level_map = {1: "subsection", 2: "subdivision", 3: "subparagraph", 4: "subpart", 5: "subitem"}
        subsection_type = level_map.get(level, "subsection")

        # Extract subsection ID from text (e.g., "(a)", "(1)", etc.)
        subsection_pattern = self.extraction_patterns[NodeType.SUBSECTION]
        subsection_match = re.match(subsection_pattern, text)
        subsection_id = subsection_match.group(1) if subsection_match else "(0)"

        return {
            "text": text,
            "id": subsection_id,
            "type": subsection_type,
            "code": self.code,
            "level": level
        }

    def find_most_recent_at_level(self, section: Dict, target_level: int) -> Optional[Dict]:
        """
        Find the most recently encountered node at target_level using depth-first traversal.
        Returns None if no node found at target level.
        """
        most_recent = None

        def traverse(node: Dict, level: int):
            nonlocal most_recent
            # If this node is at our target level, update most_recent
            if level == target_level:
                most_recent = node

            # Traverse children if they exist
            for subsection in node.get('subsections', []):
                traverse(subsection, subsection.get('level', 0))

        # Start traversal from section's direct subsections
        for subsection in section.get('subsections', []):
            traverse(subsection, subsection.get('level', 0))

        return most_recent

    def find_parent_for_level(self, section: Dict, current_level: int) -> Dict:
        """
        Find appropriate parent for a node at current_level.
        Falls back through levels until finding a parent or returns section.
        """
        # Try each level from current_level-1 down to 1
        for target_level in range(current_level - 1, 0, -1):
            parent = self.find_most_recent_at_level(section, target_level)
            if parent is not None:
                return parent

        # If no parent found, return section
        return section

    def _handle_subsection(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a subsection node"""
        if not element:
            return None

        text = element.get_text().strip()
        if not text:
            return None

        # Get current section
        section = self.parser.state.get(NodeType.SECTION)
        if not section:
            self.logger.warning("No section found for subsection")
            return None

        # Create subsection
        current_level = self._get_indent_level(element)
        subsection = self._create_subsection(text, current_level)
        if not subsection:
            return None

        # Find appropriate parent
        parent = self.find_parent_for_level(section, current_level)

        # Add subsection to parent
        if 'subsections' not in parent:
            parent['subsections'] = []
        parent['subsections'].append(subsection)

        self.logger.debug(f"Added level {current_level} subsection to parent: {text[:50]}...")

        return subsection


    def _handle_code(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a code node"""
        text = element.get_text()
        if not text:
            return None

        # Find the code ID by looking up the name in CODE_CONFIG (case-insensitive)
        code_name = text.strip().upper()
        code_id = next((k for k, v in CODE_CONFIG.items() if v.upper() == code_name), None)
        if not code_id:
            self.logger.warning(f"Code not found in CODE_CONFIG: {code_name}")
            return None

        return {
            "text": code_name,
            "id": code_id,
            "type": "code",
            "code": code_id,
        }

    def _handle_pre_amendment(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a pre-amendment node"""
        text = element.get_text()
        if not text:
            return None

        amendment = [text]

        # Initialize pre-amendment list if None
        if self.parser.state[NodeType.PRE_AMENDMENT] is None:
            self.parser.state[NodeType.PRE_AMENDMENT] = []
        self.parser.state[NodeType.PRE_AMENDMENT].extend(amendment)
        self.logger.info(f"Stored pre-amendment: {amendment}")
        return None

    def _handle_amendment(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle an amendment node"""
        text = element.get_text()
        if not text:
            return None

        history = [text]

        # If we have a current section, add this to its amendment history
        section = self.parser.state.get(NodeType.SECTION)
        if not section:
            self.logger.warning("No section found for amendment")
            return None

        # Initialize amendment_history array if it doesn't exist
        if 'amendment_history' not in section:
            section['amendment_history'] = []

        # Add the entries to the section's amendment history
        section['amendment_history'].extend(history)


    def _handle_title(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a title node"""
        return self._create_node(NodeType.TITLE, element)


    def _handle_subtitle(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a subtitle node"""
        return self._create_node(NodeType.SUBTITLE, element)


    def _handle_chapter(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a chapter node"""
        return self._create_node(NodeType.CHAPTER, element)


    def _handle_subchapter(self, element=None) -> Optional[Dict[str, Any]]:
        """Handle a subchapter node"""
        return self._create_node(NodeType.SUBCHAPTER, element)


    def _parse_node(self, element=None) -> Optional[Dict[str, Any]]:
        """Parse a node from element"""
        text = element.get_text()
        if not text:
            return None

        node_type = self._detect_node_type(element)

        match node_type:
            case NodeType.CODE:
                return self._handle_code(element)
            case NodeType.TITLE:
                return self._handle_title(element)
            case NodeType.SUBTITLE:
                return self._handle_subtitle(element)
            case NodeType.CHAPTER:
                return self._handle_chapter(element)
            case NodeType.SUBCHAPTER:
                return self._handle_subchapter(element)
            case NodeType.SECTION:
                return self._handle_section(element)
            case NodeType.SUBSECTION:
                return self._handle_subsection(element)
            case NodeType.AMENDMENT:
                return self._handle_amendment(element)
            case NodeType.PRE_AMENDMENT:
                return self._handle_pre_amendment(element)
            case _:
                return self._handle_unknown_node(element)

    def _handle_unknown_node(self, element) -> Optional[Dict[str, Any]]:
        """Handle unknown node type"""
        self.logger.warning(f"Unknown node type for element: {element}")
        return self._handle_continuation_node(element)

    def _handle_continuation_node(self, element):
        """Handle nodes that are continuations of previous nodes"""
        if not self.parser.state or not isinstance(self.parser.state, dict):
            return None

        # Get the last node type processed
        try:
            last_node_type = next(reversed(self.parser.state.keys()))
        except StopIteration:
            return None

        # Check if the state contains a list or dict
        current_state = self.parser.state[last_node_type]

        if isinstance(current_state, list):
            # If it's a list, append to the last item's text
            if current_state and isinstance(current_state[-1], dict):
                last_item = current_state[-1]
                if 'text' in last_item:
                    last_item['text'] += f" {element.get_text().strip()}"
        elif isinstance(current_state, dict):
            # If it's a dict, append to its text directly
            if 'text' in current_state:
                current_state['text'] += f" {element.get_text().strip()}"

        return None

    def build_index(self) -> None:
        """Build the complete index by processing all HTML files"""
        for _, chapter_id, file_path in self._get_html_files():
            try:
                self.logger.info(f"Processing chapter {chapter_id} from {file_path.name}")
                self.process_file(file_path)
            except Exception as e:
                self.logger.error(f"Error processing {file_path}: {str(e)}")
                self.logger.debug(traceback.format_exc())
                raise


    def save_index(self, output_path: Path) -> None:
        """Save the built index to a JSON file

        Args:
            output_path: Path where the JSON file should be saved
        """
        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.parser.root, f, indent=2, ensure_ascii=False)
        self.logger.info(f"Created index file: {output_path}")


    def process_file(self, file_path: Path) -> Dict[str, Any]:
        """Process a file and return the hierarchy

        Args:
            file_path: Path to the HTML file to process

        Returns:
            Dictionary containing the complete hierarchy for the processed file

        Raises:
            Exception: If there's an error processing the file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')

            self.parser.reset_state_for_new_file()

            # Process all paragraphs in order
            for p in soup.find_all('p'):
                # Pre-process the text with links
                cleaned_text = self._extract_text_with_links(p)
                if not cleaned_text:
                    continue
                # Replace the element's text with processed version
                p.string = cleaned_text
                # Parse the node using the pre-processed text
                node = self._parse_node(p)
                if node:
                    self.parser._add_node(node)

            return self.parser.root

        except Exception as e:
            self.logger.error(f"Error processing {file_path}: {str(e)}")
            self.logger.debug(traceback.format_exc())
            raise


    def _detect_node_type(self, element) -> Optional[NodeType]:
        """Detect node type from text based on patterns

        Args:
            text: Text content to check against patterns

        Returns:
            NodeType enum if detected, None otherwise
        """
        text = element.get_text()

        if self._is_pre_amendment(text) and 'center' in element.get('class', []):
            return NodeType.PRE_AMENDMENT

        if self._is_amendment(text):
            return NodeType.AMENDMENT

        # Then check for structural nodes
        found_node_type = None
        for node_type, info in self.parser.HIERARCHY.items():
            if self._matches_pattern(text, node_type):
                found_node_type = node_type

        if not found_node_type:
            if self.parser.state.get(NodeType.SECTION):
                return NodeType.SUBSECTION
            else:
                self.logger.warning(f"Unknown node type: {node_type} for element: {element}")
                return None

        return found_node_type


    def _is_pre_amendment(self, text: str) -> bool:
        """Check if text is a pre-amendment

        Args:
            text: Text content to check

        Returns:
            True if text is a pre-amendment, False otherwise
        """
        is_pre_amendment = any(pattern in text for pattern in self.pre_amendment_patterns)
        return is_pre_amendment

    def _is_post_amendment(self, text: str) -> bool:
        """Check if text is a post-amendment

        Args:
            text: Text content to check

        Returns:
            True if text is a post-amendment, False otherwise
        """
        is_post_amendment = any(text.startswith(pattern) for pattern in self.amendment_patterns)
        return is_post_amendment

    def _is_amendment(self, text: str) -> bool:
        """Check if text is an amendment

        Args:
            text: Text content to check

        Returns:
            True if text is an amendment, False otherwise
        """
        # Check for pre-amendment or post-amendment
        is_pre_amendment = self._is_pre_amendment(text)
        is_post_amendment = self._is_post_amendment(text)

        return is_pre_amendment or is_post_amendment


class WaterLawsParser(TexasCodeParser):
    """Parser for Auxiliary Water Laws which have a different structure"""
    def __init__(self, base_path: Path):
        super().__init__(base_path)
        self.extraction_patterns = {
            NodeType.CODE: TexasCodeParser.extraction_patterns[NodeType.CODE],
            NodeType.CHAPTER: r'(?i)^(CHAPTER\s+\d+(?:[A-Z]|-[A-Z])?\.)',
            NodeType.ARTICLE: r'^Art\.\s+\d+',
            NodeType.SECTION: r'^(?:Sec(?:tion)?\.\s+)(\d+[A-Z]?\.\d+(?:\d*))\.',
            NodeType.SUBSECTION: r'^\([a-z0-9A-Z-]+\)'
        }

        # Override hierarchy for Water Laws
        self.HIERARCHY = {
            NodeType.CODE: {
                'patterns': [self.extraction_patterns[NodeType.CODE]],
                'can_contain': [NodeType.CHAPTER]
            },
            NodeType.CHAPTER: {
                'patterns': [self.extraction_patterns[NodeType.CHAPTER]],
                'can_contain': [NodeType.ARTICLE]
            },
            NodeType.ARTICLE: {
                'patterns': [self.extraction_patterns[NodeType.ARTICLE]],
                'can_contain': [NodeType.SECTION]
            },
            NodeType.SECTION: {
                'patterns': [self.extraction_patterns[NodeType.SECTION]],
                'can_contain': [NodeType.SUBSECTION]
            },
            NodeType.SUBSECTION: {
                'patterns': [self.extraction_patterns[NodeType.SUBSECTION]],
                'can_contain': [None]
            },
            NodeType.AMENDMENT: {
                'patterns': [],
                'can_contain': [None]
            },
            NodeType.PRE_AMENDMENT: {
                'patterns': [],
                'can_contain': [None]
            }
        }
        # Initialize hierarchy parser with complete custom hierarchy
        self.parser = HierarchyParser(self.code, self.HIERARCHY)


class CivilStatutesParser(TexasCodeParser):
    """Parser for Vernon's Civil Statutes which have complex file naming"""
    def __init__(self, base_path: Path):
        super().__init__(base_path)

        self.HIERARCHY = {
            NodeType.CODE: {
                'patterns': [self.extraction_patterns[NodeType.CODE]],
                'can_contain': [NodeType.TITLE]
            },
            NodeType.TITLE: {
                'patterns': [self.extraction_patterns[NodeType.TITLE]],
                'can_contain': [NodeType.CHAPTER, NodeType.SECTION]
            },
            NodeType.CHAPTER: {
                'patterns': [self.extraction_patterns[NodeType.CHAPTER]],
                'can_contain': [NodeType.SECTION]
            },
            NodeType.SECTION: {
                'patterns': [self.extraction_patterns[NodeType.SECTION]],
                'can_contain': [NodeType.SUBSECTION]
            },
            NodeType.SUBSECTION: {
                'patterns': [self.extraction_patterns[NodeType.SUBSECTION]],
                'can_contain': [None]
            },
            NodeType.AMENDMENT: {
                'patterns': [],
                'can_contain': [None]
            },
            NodeType.PRE_AMENDMENT: {
                'patterns': [],
                'can_contain': [None]
            }
        }
        # Initialize hierarchy parser with complete custom hierarchy
        self.parser = HierarchyParser(self.code, self.HIERARCHY)

    def _get_html_files(self) -> Iterator[tuple[float, str, Path]]:
        """Get sorted HTML files with complex naming patterns

        Returns:
            Iterator of tuples containing (sort_key, chapter_id, file_path)
        """
        files = []
        # Match cv.X.Y.htm patterns where X and Y can contain digits, letters, dots, and dashes
        for file_path in self.base_path.glob('cv.*.htm*'):
            match = re.search(r'cv\.([^.]+(?:\.[^.]+)*)\.html?$', file_path.name, re.IGNORECASE)
            if match:
                chapter_id = match.group(1)
                # For sorting, use first number in the ID
                chapter_num = int(re.match(r'\d+', chapter_id).group())
                files.append((chapter_num, chapter_id, file_path))
            else:
                self.logger.warning(f"Skipping file with unexpected name format: {file_path.name}")

        return iter(sorted(files))

class HierarchyParser:
    """Parser that manages a hierarchy of nodes"""

    def __init__(self, code: str, hierarchy: Dict):
        self.code = code
        self.HIERARCHY = hierarchy
        self.logger = logging.getLogger(__name__)
        self.root = {
            "text": CODE_CONFIG[code].upper(),
            "id": code,
            "type": "code",
            "code": code,
        }
        # Initialize current based on hierarchy structure
        self.state = {node_type: None for node_type in self.HIERARCHY.keys()}

    def reset_state_for_new_file(self) -> None:
        """Reset the parser state to initial values, preserving code, title, and subtitle"""
        preserved_types = {NodeType.CODE, NodeType.TITLE, NodeType.SUBTITLE}
        for node_type in self.HIERARCHY.keys():
            if node_type not in preserved_types:
                self.state[node_type] = None


    def _add_node(self, node: Dict[str, Any]) -> None:
        """Add a node to the hierarchy

        Args:
            node: Dictionary containing the node data to be added
        """
        node_type = NodeType.from_str(node['type'])

        if node_type == NodeType.SUBSECTION:
            return

        # Check if this is the same as current node at this level
        if node_type in (NodeType.CODE, NodeType.TITLE, NodeType.SUBTITLE):
            current_node = self.state.get(node_type)
            if current_node and current_node.get('id') == node.get('id'):
                return  # Skip if we're already at this node

        # Check for any pending pre-amendments and add them
        if self.state.get(NodeType.PRE_AMENDMENT):
            # Initialize amendment_history array if it doesn't exist
            if 'amendment_history' not in node:
                node['amendment_history'] = []
            node['amendment_history'].extend(self.state[NodeType.PRE_AMENDMENT])
            self.logger.debug(f"Adding pre-amendment to node: {node}")
            # Clear the pending amendment after adding it
            self.state[NodeType.PRE_AMENDMENT] = None

        parent = self._find_parent(node_type)
        if not parent:
            if node_type != NodeType.CODE:
                self.logger.warning(f"No parent found for node type: {node_type}")
            return

        # Initialize container if it doesn't exist
        container = node_type.plural
        if container not in parent:
            parent[container] = []

        # Add node to parent's container
        parent[container].append(node)

        # Update current to point to the actual node in the hierarchy
        self._update_current_node(node_type, parent[container][-1])

    def _find_parent(self, node_type: NodeType) -> Optional[Dict[str, Any]]:
        """Find the appropriate parent for a node type

        Looks through the hierarchy to find the first current node that can contain
        this node type.

        Args:
            node_type: Type of node to find parent for

        Returns:
            The closest valid parent node in the hierarchy, or None if no valid parent found
        """
        # CODE is the root node and doesn't need a parent
        if node_type == NodeType.CODE:
            return None

        # Check each level in reverse order
        for parent_type, info in reversed(self.HIERARCHY.items()):
            # If this level can contain our node type
            if node_type in info['can_contain']:
                # And we have a current node of this type
                if self.state[parent_type]:
                    return self.state[parent_type]
                # Special case for root/code level
                elif parent_type == NodeType.CODE:
                    return self.root

        self.logger.warning(f"No valid parent found for {node_type}")
        return None

    def _get_hierarchy_depth(self, node_type: NodeType) -> int:
        """Get the hierarchical depth/position of a node type in the structure

        Args:
            node_type: Type of node ('code', 'title', 'chapter', etc.)

        Returns:
            Integer representing the depth in the hierarchy (0 for 'code', 1 for 'title', etc.)
        """
        return list(self.HIERARCHY.keys()).index(node_type)

    def _update_current_node(self, node_type: NodeType, node: Dict[str, Any]):
        """Update the current node pointer and clear lower levels"""
        self._clear_lower_levels(node_type)
        self.state[node_type] = node

        # Special handling for sections to maintain amendment tracking
        if node_type == NodeType.SECTION:
            self.state[NodeType.SECTION] = node


    def _clear_lower_levels(self, node_type: NodeType):
        """Clear all levels below the given node type"""
        level = self._get_hierarchy_depth(node_type)
        hierarchy_levels = list(self.HIERARCHY.keys())
        for lower_level in hierarchy_levels[level + 1:]:
            self.state[lower_level] = None

        # Don't clear section reference when processing subsections
        if node_type != NodeType.SUBSECTION:
            self.state[NodeType.SECTION] = None

def get_parser_class(code: str) -> Type[TexasCodeParser]:
    """Return appropriate parser class for the given code

    Args:
        code: Two-letter code identifier (e.g., 'ag' for Agriculture)

    Returns:
        Parser class appropriate for the given code

    Note:
        Returns base TexasCodeParser for most codes, but specialized parsers
        for certain codes like Water Laws (wl) and Civil Statutes (cv)
    """
    parsers = {
        'wl': WaterLawsParser,
        'cv': CivilStatutesParser,
        'cn': None  # Skip - handled by cn_create.py
    }
    return parsers.get(code, TexasCodeParser)

def main():
    """Main entry point for the code parser"""
    parser = argparse.ArgumentParser(description='Create index file from HTML statute files')
    parser.add_argument('--input-dir', '-i', type=Path,
                       help='Directory containing HTML files (default: script directory)',
                       default=Path(__file__).resolve().parent.parent / "data" / "tx")
    parser.add_argument('--output-dir', '-o', type=Path,
                       help='Directory to write index file (default: script directory)',
                       default=Path(__file__).resolve().parent.parent / "public" / "index" / "tx")
    parser.add_argument('--all', action='store_true',
                       help='Process all codes from Texas directory')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')

    args = parser.parse_args()

    # Set logging level based on debug flag
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('code_create.log')
        ]
    )

    if args.all:
        process_all_codes(args)
    else:
        process_single_code(args)

def process_all_codes(args: argparse.Namespace) -> None:
    """Process all Texas codes"""
    SCRIPT_DIR = Path(__file__).resolve().parent
    base_input = SCRIPT_DIR.parent / "data" / "tx"
    output_dir = SCRIPT_DIR.parent / "public" / "index" / "tx"
    output_dir.mkdir(parents=True, exist_ok=True)

    for code, name in CODE_CONFIG.items():
        try:
            if code == 'cn':
                logging.info(f"Skipping {code} - use cn_create.py instead")
                continue

            input_dir = base_input / code
            if not input_dir.exists():
                logging.warning(f"Skipping {code}: directory not found at {input_dir}")
                continue

            logging.info(f"Processing {code} ({name})")
            logging.info("=" * 64)

            parser_class = get_parser_class(code)
            if not parser_class:
                raise ValueError(f"No parser available for code: {code}")

            code_parser = parser_class(input_dir)
            code_parser.build_index()
            output_path = output_dir / f'{code}.index.json'
            code_parser.save_index(output_path)

        except Exception as e:
            logging.error(f"Error processing {code}: {str(e)}")
            logging.debug(traceback.format_exc())

def process_single_code(args: argparse.Namespace) -> None:
    """Process a single code directory"""
    args.output_dir.mkdir(parents=True, exist_ok=True)

    # Create temporary parser to determine code
    temp_parser = TexasCodeParser(args.input_dir)
    code = temp_parser.code.lower()  # Ensure code is lowercase

    parser_class = get_parser_class(code)
    if not parser_class:
        raise ValueError(f"No parser available for code: {code}")

    code_parser = parser_class(args.input_dir)
    code_parser.build_index()
    output_path = args.output_dir / f'{code}.index.json'
    code_parser.save_index(output_path)

if __name__ == '__main__':
    main()
