#!/usr/bin/env python3
import json
import sys
import argparse
import logging
import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pathlib import Path

def parse_arguments():
    """Parse and validate command line arguments."""
    parser = argparse.ArgumentParser(
        description='Flatten hierarchical JSON legal code into MongoDB documents.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument('filename', help='Path to the JSON file to process')
    parser.add_argument('--mongo-uri', default=os.getenv('MONGO_URI', 'mongodb://localhost:27017/'), help='MongoDB connection URI')
    parser.add_argument('--database', default=os.getenv('MONGO_DATABASE', 'statutes_db'), help='MongoDB database name')
    parser.add_argument('--collection', default=os.getenv('MONGO_COLLECTION', 'statutes'), help='MongoDB collection name')
    parser.add_argument('--statute-collection', default=os.getenv('STATUTE_COLLECTION', 'tx'), help='Statute collection identifier (e.g., tx, us)')
    parser.add_argument('--output-json', help='Output to a JSON file instead of MongoDB')

    return parser.parse_args()

def setup_logging():
    """Configures logging for better debugging."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def load_json(filename):
    """Loads JSON data from a file."""
    try:
        with open(filename, "r") as file:
            return json.load(file)
    except FileNotFoundError:
        logging.error(f"File '{filename}' not found.")
        sys.exit(1)
    except json.JSONDecodeError:
        logging.error(f"File '{filename}' contains invalid JSON.")
        sys.exit(1)

def connect_to_mongo(mongo_uri, database, collection):
    """Establishes connection to MongoDB and ensures the database and collection exist."""
    try:
        client = MongoClient(mongo_uri)
        db = client[database]
        logging.info(f"Connected to database: {database}")

        # Force database creation by inserting a temporary document (if needed)
        if database not in client.list_database_names():
            db.command("ping")  # Ensures database existence
            logging.info(f"Database '{database}' created.")

        if collection not in db.list_collection_names():
            db.create_collection(collection)
            logging.info(f"Created new collection: {collection}")

        return db[collection]
    except Exception as e:
        logging.error(f"Error connecting to MongoDB: {e}")
        sys.exit(1)

def flatten_hierarchy(item, parent_path=None, documents=[], statute_collection="tx", counter=[0], code=None):
    """Recursively flattens the hierarchical JSON into MongoDB-friendly documents while storing only child IDs."""
    # Increment counter for each node
    counter[0] += 1

    # Get or set the code
    if code is None:
        code = item.get('code')  # Get code from root node

    # Create document for current node
    special_types = ('subsection', 'subdivision', 'subparagraph', 'subpart')
    if parent_path and item['type'] in special_types:
        node_id = f"{parent_path}{item['id']}"
    else:
        node_id = f"{parent_path}/{item['type']}/{item['id']}" if parent_path else f"/collection/{statute_collection}/code/{code}"

    logging.info(f"Processing node: type={item['type']}, id={item['id']}, nodeId={node_id}")

    # Determine the type based on the level for subsections, subdivisions, subparagraphs, subparts
    level_to_type = {1: "subsection", 2: "subdivision", 3: "subparagraph", 4: "subpart", 5: "subitem"}
    type_to_level = {"subsection": 1, "subdivision": 2, "subparagraph": 3, "subpart": 4, "subitem": 5}

    if item['type'] in special_types:
        # If level is provided, use it to determine type
        if 'level' in item:
            item_type = level_to_type.get(item['level'], item['type'])
        else:
            # If no level is provided, set it based on the type
            item_type = item['type']
            item['level'] = type_to_level.get(item['type'])
    else:
        item_type = item['type']

    parent_path = parent_path or f"/collection/{statute_collection}"

    doc = {
        "nodeId": node_id,
        "id": item["id"],
        "text": item.get("text", item.get("name", "")),
        "type": item_type,
        "code": code,  # Use the passed down code
        "parentId": parent_path,
        "collection": statute_collection,
        "order": counter[0]
    }

    # Copy any additional optional fields
    for field in ["amendment_history", "level"]:
        if field in item:
            doc[field] = item[field]

    # Add document to collection BEFORE processing children
    logging.info(f"Adding document to collection: {node_id}")
    documents.append(doc)

    # Process children in sequential order as they appear in the input
    child_ids = []

    # Get all possible child containers
    containers = [
        "articles",         # Code -> Article (optional, in cn)
        "subarticles",      # Article -> Subarticle (optional, in cn)
        "titles",           # Code -> Title
        "subtitles",        # Title -> Subtitle (optional)
        "chapters",         # Subtitle/Title -> Chapter
        "subchapters",      # Chapter -> Subchapter (optional)
        "sections",         # Subchapter/Chapter -> Section
        "subsections",      # Section -> Subsection (optional)
        "subdivisions",     # Subsection -> Subdivision (optional)
        "subparagraphs",    # Subdivision -> Subparagraph (optional)
        "subparts",         # Subparagraph -> Subpart (optional)
        "subitems"          # Subpart -> Subitem (optional)
    ]

    # First collect all children while preserving their original order
    all_children = []
    for container in containers:
        if container in item:
            for child in item[container]:
                all_children.append((container, child))

    # Now process children in the order they were found
    for container, child in all_children:
        logging.debug(f"Processing child from '{container}' in node {item['type']}/{item['id']}")
        child_id = flatten_hierarchy(child, node_id, documents, statute_collection, counter, code)
        if child_id:
            child_ids.append(child_id)

    # Only add children array if we actually have children
    if child_ids:
        doc["children"] = child_ids

    logging.debug(f"Finished processing node {node_id} with {len(child_ids)} children")
    return node_id

def import_data(collection, data, statute_collection, output_json=None):
    """Processes the hierarchy and either imports it into MongoDB or writes to a JSON file."""
    documents = []
    flatten_hierarchy(data, documents=documents, statute_collection=statute_collection, counter=[0])

    if output_json:
        with open(output_json, 'w') as f:
            json.dump(documents, f, indent=4)
        logging.info(f"Data written to JSON file: {output_json}")
    else:
        try:
            if documents:
                collection.insert_many(documents, ordered=False)
                logging.info(f"Successfully inserted {len(documents)} documents.")
        except Exception as e:
            logging.error(f"Error inserting data into MongoDB: {e}")
            sys.exit(1)

def main():
    load_dotenv(Path(__file__).parent.parent / '.env')
    args = parse_arguments()
    setup_logging()
    data = load_json(args.filename)

    if args.output_json:
        import_data(None, data, args.statute_collection, args.output_json)
    else:
        collection = connect_to_mongo(args.mongo_uri, args.database, args.collection)
        import_data(collection, data, args.statute_collection)

    logging.info("JSON processing completed successfully!")

if __name__ == "__main__":
    main()
