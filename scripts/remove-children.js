import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get filename from command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('Please provide a filename as an argument');
  console.log('Usage: node add-order.js <filename>');
  process.exit(1);
}

const inputFile = args[0];
const filePath = join(process.cwd(), inputFile);

try {
  // Read the file
  const data = JSON.parse(readFileSync(filePath, 'utf8'));

  // Add order to each record, starting from 1
  const orderedData = data.map((record, index) => ({
    ...record,
    order: index + 1
  }));

  // Write the updated data back to the file
  writeFileSync(filePath, JSON.stringify(orderedData, null, 2));

  console.log(`Successfully added order to ${orderedData.length} records in ${inputFile}`);
} catch (error) {
  console.error('Error processing file:', error.message);
  process.exit(1);
}
