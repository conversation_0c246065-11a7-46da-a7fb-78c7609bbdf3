#!/usr/bin/env python3

import json
import re
import logging
import argparse
from pathlib import Path
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional
import traceback

# Configuration for file path mapping
CODE_CONFIG = {
    'al': 'Alcoholic Beverage Code',
    'ag': 'Agriculture Code',
    'wl': 'Auxillary Water Laws',
    'bc': 'Business and Commerce Code',
    'bo': 'Business Organizations Code',
    'cp': 'Civil Practice and Remedies Code',
    'cr': 'Code of Criminal Procedure',
    'ed': 'Education Code',
    'el': 'Election Code',
    'es': 'Estates Code',
    'fa': 'Family Code',
    'fi': 'Finance Code',
    'gv': 'Government Code',
    'hs': 'Health and Safety Code',
    'hr': 'Human Resources Code',
    'in': 'Insurance Code',
    'i1': 'Insurance Code - Not Codified',
    'la': 'Labor Code',
    'lg': 'Local Government Code',
    'nr': 'Natural Resources Code',
    'oc': 'Occupations Code',
    'pw': 'Parks and Wildlife Code',
    'pe': 'Penal Code',
    'pr': 'Property Code',
    'sd': 'Special District Local Laws Code',
    'tx': 'Tax Code',
    'cn': 'Texas Constitution',
    'tn': 'Transportation Code',
    'ut': 'Utilities Code',
    'cv': 'Vernon\'s Civil Statutes',
    'wa': 'Water Code',
}

class StatuteIndexBuilder:
    @staticmethod
    def determine_code(index_path: str) -> str:
        """
        Determine the code from index file path or content
        
        Args:
            index_path: Path to index file (e.g., 'public/Texas/Tax Code/tx.index.txt')
            
        Returns:
            str: The code identifier (e.g., 'tx')
        """
        path = Path(index_path)
        
        # Try to get code from filename (e.g., tx.index.txt -> tx)
        if match := re.match(r'([a-z0-9]+)\.index\.txt$', path.name.lower()):
            code = match.group(1)
            if code in CODE_CONFIG:
                return code
                
        # Try to match against code names in the file content
        try:
            with open(index_path) as f:
                first_line = f.readline().strip()
                for code, name in CODE_CONFIG.items():
                    if name == first_line:
                        return code
        except Exception as e:
            logging.error(f"Error reading index file: {e}")
            
        raise ValueError(f"Could not determine code from index file: {index_path}")

    def __init__(self, index_path: str, base_path: str = "Texas"):
        """
        Initialize the index builder
        
        Args:
            index_path: Path to index file
            base_path: Root directory containing statute files
        """
        self.code = self.determine_code(index_path)
        self.base_path = Path(base_path)
        self.setup_logging()
        
        self.logger.info(f"Initialized builder for code: {self.code} ({CODE_CONFIG[self.code]})")
        
        # Initialize root structure
        self.root = {
            "name": CODE_CONFIG[self.code].upper(),
            "code": self.code,
            "type": "code",
            "id": self.code,
        }

    def setup_logging(self):
        """Configure logging"""
        self.logger = logging.getLogger(f"{self.code}_index_builder")
        self.logger.setLevel(logging.DEBUG)
        
        # Console handler with DEBUG level
        console = logging.StreamHandler()
        console.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console.setFormatter(formatter)
        self.logger.addHandler(console)
        
        # File handler
        file_handler = logging.FileHandler(f"{self.code}_index_builder.log")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def parse_index_file(self, index_path: str):
        """Parse the index file to build basic hierarchy"""
        self.logger.info(f"Parsing index file: {index_path}")
        
        current = {
            "title": None,
            "subtitle": None, 
            "chapter": None,
            "subchapter": None
        }
        
        try:
            with open(index_path) as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        if line.startswith("TITLE"):
                            self._handle_title(line, current)
                        elif line.startswith("SUBTITLE"):
                            self._handle_subtitle(line, current)
                        elif line.startswith("CHAPTER"):
                            self._handle_chapter(line, current)
                        elif line.startswith("SUBCHAPTER"):
                            self._handle_subchapter(line, current)
                        elif line.startswith("\tSec.") or line.startswith("Sec."):
                            self._handle_section(line, current)
                        else:
                            self.logger.warning(f"Unhandled line format: {line}")
                    except Exception as e:
                        self.logger.error(f"Error processing line {line_num}: {line}")
                        self.logger.error(str(e))
                        self.logger.error(traceback.format_exc())
                        raise
        except Exception as e:
            self.logger.error(f"Failed to parse index file: {index_path}")
            self.logger.error(str(e))
            raise

    def _handle_title(self, line: str, current: Dict):
        """Handle a title line from the index"""
        # Extract title number and name (e.g., "TITLE 1. PROPERTY TAX CODE")
        match = re.match(r'TITLE (\d+)\.\s*(.*)', line)
        if not match:
            self.logger.error(f"Invalid title format: {line}")
            raise ValueError(f"Invalid title format: {line}")
            
        title = {
            "name": line,
            "code": self.code,
            "type": "title",
            "id": match.group(1),
        }
        if 'titles' not in self.root:
            self.root['titles'] = []
        self.root['titles'].append(title)
        current["title"] = title
        current["subtitle"] = None
        current["chapter"] = None
        current["subchapter"] = None

    def _handle_subtitle(self, line: str, current: Dict):
        """Handle a subtitle line from the index"""
        # Extract subtitle letter and name (e.g., "SUBTITLE A. GENERAL PROVISIONS")
        match = re.match(r'SUBTITLE ([A-Z])\.\s*(.*)', line)
        if not match:
            self.logger.error(f"Invalid subtitle format: {line}")
            raise ValueError(f"Invalid subtitle format: {line}")
            
        if not current["title"]:
            raise ValueError("Subtitle found before title")
            
        subtitle = {
            "name": line,
            "code": self.code,
            "type": "subtitle",
            "id": match.group(1),
        }
        if 'subtitles' not in current['title']:
            current['title']['subtitles'] = []
        current['title']['subtitles'].append(subtitle)
        current["subtitle"] = subtitle
        current["chapter"] = None
        current["subchapter"] = None

    def _handle_chapter(self, line: str, current: Dict):
        """Handle a chapter line from the index"""
        if not current["subtitle"]:
            self.logger.error("Chapter found before subtitle")
            raise ValueError("Chapter found before subtitle")
        
        # Updated pattern to handle formats like "41", "41A", "313A", etc.
        match = re.match(r'CHAPTER\s+([0-9]+[A-Z]?)[.]\s*(.*)', line)
        if not match:
            self.logger.error(f"Invalid chapter format: {line}")
            raise ValueError(f"Invalid chapter format: {line}")
        
        chapter = {
            "name": line,
            "code": self.code,
            "type": "chapter",
            "id": match.group(1),
        }

        
        if 'chapters' not in current['subtitle']:
            current['subtitle']['chapters'] = []
        current['subtitle']['chapters'].append(chapter)
        current["chapter"] = chapter
        current["subchapter"] = None  # Reset subchapter when new chapter starts
        
    def _handle_subchapter(self, line: str, current: Dict):
        """Handle a subchapter line from the index"""
        if not current["chapter"]:
            self.logger.error("Subchapter found before chapter")
            raise ValueError("Subchapter found before chapter")
        
        # Updated pattern to handle formats like "A", "I-1", "B-1", etc.
        match = re.match(r'SUBCHAPTER\s+([A-Z](?:-[0-9]+)?)[.]\s*(.*)', line)
        if not match:
            self.logger.error(f"Invalid subchapter format: {line}")
            raise ValueError(f"Invalid subchapter format: {line}")
        
        subchapter = {
            "name": line,
            "code": self.code,
            "type": "subchapter",
            "id": match.group(1),
        }
        
        if 'subchapters' not in current['chapter']:
            current['chapter']['subchapters'] = []
        current['chapter']['subchapters'].append(subchapter)
        current["subchapter"] = subchapter
        
    def _handle_section(self, line: str, current: Dict):
        """Handle a section line from the index"""
        # Extract section number and name (e.g., "Sec. 1.01. SHORT TITLE")
        match = re.match(r'\s*Sec\.\s*([0-9A-Z.-]+)\.\s*(.*)', line)
        if not match:
            self.logger.error(f"Invalid section format: {line}")
            raise ValueError(f"Invalid section format: {line}")
            
        if not current["chapter"]:
            self.logger.error("Section found before chapter")
            raise ValueError("Section found before chapter")
            
        section_id = match.group(1)
        section_name = match.group(2)
        
        section = {
            "name": f"Sec. {section_id}. {section_name}",
            "code": self.code,
            "type": "section",
            "id": section_id,
        }
                
        # Initialize sections arrays if they don't exist
        if current["subchapter"]:
            if 'sections' not in current['subchapter']:
                current['subchapter']['sections'] = []
            current['subchapter']['sections'].append(section)
        else:
            if 'sections' not in current['chapter']:
                current['chapter']['sections'] = []
            current['chapter']['sections'].append(section)
    
    def _extract_section_content(self, html_path: str, section_id: str) -> Dict:
        """Extract section content including nested subsections from HTML"""
        
        def get_indent_level(p_tag) -> int:
            """Get the level based on text-indent style"""
            style = p_tag.get('style', '')
            match = re.search(r'text-indent:(\d+)ex', style)
            if match:
                indent = int(match.group(1))
                level = ((indent - 7) // 6) + 1 if indent >= 7 else 1
                return level
            return 1
        
        def is_amendment_history(text):
            amendment_patterns = (
                'Acts ',
                'Added by',
                'Amended by',
                'Renumbered from',
                'Text of section as amended by',
                'For text of section as amended by',
                'Prior law',
                'Reenacted and amended by',
            )
            return text.startswith(amendment_patterns)

        def parse_subsection_id(text: str) -> Optional[str]:
            """Extract subsection ID if present"""
            match = re.match(r'^\(([^)]+)\)', text.strip())
            return match.group(1) if match else None

        def clean_section_text(text: str) -> str:
            """Remove section header from text and return content"""
            # Get the full section name without leading/trailing whitespace
            # Match section header pattern: "Sec. X.XX. TITLE" with flexible title formatting
            section_pattern = r'^Sec\.\s+(\d+(?:\.\d+)?[A-Za-z]*)\.\s+([^.]+(?:\.|$))'
            match = re.match(section_pattern, text)
            if not match:
                return text
                
            full_name = match.group(0).strip()
            # If the text starts with the full section name, remove it and any following period/space
            if text.startswith(full_name):
                remaining_text = text[len(full_name):].strip()
                # Remove leading period and space if present
                if remaining_text.startswith('.'):
                    remaining_text = remaining_text[1:].strip()
                return remaining_text
            
            return text.strip()

        try:
            with open(html_path) as f:
                html_content = f.read()
                soup = BeautifulSoup(html_content, 'html.parser')

            content = {
                "sections": [],
                "amendment_history": []
            }

            section_anchor = soup.find('a', attrs={'name': section_id})
            if not section_anchor:
                self.logger.warning(f"Section {section_id} anchor not found in {html_path}")
                return content

            section_header = section_anchor.find_parent('p')
            if not section_header:
                self.logger.warning(f"Could not find parent paragraph for section {section_id}")
                return content

            # Get all paragraphs until the next section anchor
            current = section_header
            section_text = []
            
            while current:
                # Check if we've hit the next section
                next_anchor = current.find('a', attrs={'name': True})
                if next_anchor and next_anchor != section_anchor and next_anchor.get('name', '').replace('.', '').isdigit():
                    break
                    
                text = current.get_text().strip()
                if text:
                    if is_amendment_history(text):
                        content["amendment_history"].append(text)
                    else:
                        # Clean section header from text if this is the first paragraph
                        text = clean_section_text(text)
                        if text:  # Only add if there's text remaining
                            level = get_indent_level(current)
                            section_text.append((text, level))
                current = current.find_next('p')

            # Process section text to build hierarchy
            section_stack = []
            
            for text, level in section_text:
                subsection_id = parse_subsection_id(text)
                
                new_section = {
                    "text": text,
                    "level": level
                }
                if subsection_id:
                    new_section["id"] = subsection_id

                # Pop stack until we find the appropriate parent level
                while section_stack and section_stack[-1]["level"] >= level:
                    section_stack.pop()

                if section_stack:
                    # Add to parent's subsections
                    parent = section_stack[-1]
                    if 'subsections' not in parent:
                        parent['subsections'] = []
                    parent['subsections'].append(new_section)
                else:
                    # Top level section
                    if 'sections' not in content:
                        content['sections'] = []
                    content['sections'].append(new_section)

                section_stack.append(new_section)

            return content
            
        except Exception as e:
            self.logger.error(f"Failed to extract content from {html_path}")
            self.logger.error(str(e))
            self.logger.error(traceback.format_exc())
            raise

    def enhance_with_content(self):
        """Add content from HTML files to the hierarchy"""
        
        def process_sections(sections, html_path):
            """Process sections in a chapter/subchapter"""
            if not sections:
                return
            
            for section in sections:
                try:
                    content = self._extract_section_content(
                        html_path,
                        section["id"]
                    )
                    section["content"] = content
                except Exception as e:
                    self.logger.error(f"Failed to process section {section['id']}")
                    self.logger.error(str(e))
                    self.logger.error(traceback.format_exc())
                    
        for title in self.root.get('titles', []):
            # Handle titles with subtitles
            if 'subtitles' in title:
                for subtitle in title['subtitles']:
                    for chapter in subtitle.get('chapters', []):
                        html_path = self.base_path / f"{CODE_CONFIG[self.code]}" / f"{self.code}.{chapter['id']}.htm"
                        
                        if html_path.exists():
                            # Process sections in chapter
                            if 'sections' in chapter:
                                process_sections(chapter['sections'], str(html_path))
                            
                            # Process sections in subchapters
                            if 'subchapters' in chapter:
                                for subchapter in chapter['subchapters']:
                                    if 'sections' in subchapter:
                                        process_sections(subchapter['sections'], str(html_path))
                        else:
                            self.logger.warning(f"HTML file not found: {html_path}")
            
            # Handle titles without subtitles (direct chapters)
            elif 'chapters' in title:
                for chapter in title['chapters']:
                    html_path = self.base_path / f"{CODE_CONFIG[self.code]}" / f"{self.code}.{chapter['id']}.htm"
                    
                    if html_path.exists():
                        # Process sections in chapter
                        if 'sections' in chapter:
                            process_sections(chapter['sections'], str(html_path))
                        
                        # Process sections in subchapters
                        if 'subchapters' in chapter:
                            for subchapter in chapter['subchapters']:
                                if 'sections' in subchapter:
                                    process_sections(subchapter['sections'], str(html_path))
                    else:
                        self.logger.warning(f"HTML file not found: {html_path}")

    def save_index(self, output_path: str):
        """Save the generated index to JSON file"""
        self.logger.info(f"Saving index to: {output_path}")
                
        try:
            with open(output_path, 'w') as f:
                json.dump(self.root, f, indent=2)
            self.logger.info("Successfully saved index file")
        except Exception as e:
            self.logger.error("Failed to save index file")
            self.logger.error(str(e))
            raise

def main():
    parser = argparse.ArgumentParser(description='Build statute index with content')
    parser.add_argument('index_file', help='Path to index file')
    parser.add_argument('--base-path', default='Texas', help='Base path for statute files')
    parser.add_argument('--output-dir', default='index/tx', help='Output directory')
    args = parser.parse_args()

    try:
        # Create builder instance - code is determined automatically
        builder = StatuteIndexBuilder(args.index_file, args.base_path)
        
        # Parse index file
        builder.parse_index_file(args.index_file)
        # Enhance with content
        builder.enhance_with_content()
        
        # Create output directory if it doesn't exist
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save result
        output_path = output_dir / f"{builder.code}.index_v2.json"
        builder.save_index(str(output_path))
        
    except Exception as e:
        logging.error(f"Failed to build index: {str(e)}")
        raise

if __name__ == "__main__":
    main()