#!/usr/bin/env python3

import os
import re
import zipfile
import logging
import argparse
from pathlib import Path
from typing import List, Tuple
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Configuration for files to skip per code
SKIP_FILES = {
    'es': ["es.2.htm", "es.i.htm", "es.y.htm", "es.y.v2.htm"],
    # Add more codes and their skip files as needed
    # 'code_id': ['file1.htm', 'file2.htm']
}

class TexasCodeDownloader:
    """Downloads and extracts Texas legislative code files"""
    
    def __init__(self, base_url: str, output_dir: Path):
        self.base_url = base_url
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def get_download_links(self) -> List[Tuple[str, str]]:
        """Get list of code zip file download links
        
        Returns:
            List of tuples containing (code_id, download_url)
        """
        try:
            # Get the download page
            response = requests.get(self.base_url)
            response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all zip file links matching pattern
            links = []
            for link in soup.find_all('a', href=re.compile(r'.*/Zips/.*\.htm\.zip')):
                href = link.get('href')
                if not href:
                    continue
                    
                # Extract code ID from filename (e.g., 'CN' from 'CN.htm.zip')
                code_match = re.search(r'/([^/]+)\.htm\.zip$', href)
                if not code_match:
                    continue
                    
                code_id = code_match.group(1).lower()
                full_url = urljoin(self.base_url, href)
                links.append((code_id, full_url))
            
            return links
            
        except requests.RequestException as e:
            self.logger.error(f"Error fetching download page: {e}")
            raise

    def download_and_extract(self, code_id: str, url: str) -> None:
        """Download and extract a code zip file
        
        Args:
            code_id: Code identifier (e.g., 'cn' for Constitution)
            url: URL to download the zip file from
        """
        try:
            # Create directory for this code
            code_dir = self.output_dir / code_id
            code_dir.mkdir(exist_ok=True)
            
            # Get list of files to skip for this code
            skip_files = SKIP_FILES.get(code_id, [])
            
            # Download zip file
            self.logger.info(f"Downloading {code_id} from {url}")
            response = requests.get(url)
            response.raise_for_status()
            
            # Save to temporary zip file
            zip_path = code_dir / f"{code_id}.zip"
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # Extract contents
            self.logger.info(f"Extracting {code_id} to {code_dir}")
            with zipfile.ZipFile(zip_path) as zf:
                # Process each file in the zip
                for file_info in zf.filelist:
                    # Convert filename to lowercase and replace '..' with '.'
                    new_filename = file_info.filename.lower().replace('..', '.')
                    
                    # Skip files in the skip list for this code
                    if new_filename in skip_files:
                        self.logger.info(f"Skipping {new_filename} for {code_id}")
                        continue
                    
                    # Extract with new filename
                    with zf.open(file_info) as source, open(code_dir / new_filename, 'wb') as target:
                        target.write(source.read())
            
            # Clean up zip file
            zip_path.unlink()
            
        except (requests.RequestException, zipfile.BadZipFile) as e:
            self.logger.error(f"Error processing {code_id}: {e}")
            raise

def main():
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Download and extract Texas legislative codes')
    parser.add_argument('--codes', nargs='+', help='Specific codes to download (e.g., es ag cp)')
    parser.add_argument('--output-dir', '-o', type=Path,
                       help='Directory to save downloaded files',
                       default=Path(__file__).resolve().parent.parent / "public" / "data" / "tx")
    args = parser.parse_args()

    # Configuration
    BASE_URL = "https://statutes.capitol.texas.gov/Download.aspx"

    try:
        # Initialize downloader
        downloader = TexasCodeDownloader(BASE_URL, args.output_dir)
        
        # Get download links
        links = downloader.get_download_links()
        logger.info(f"Found {len(links)} code files available")
        
        # Filter links if specific codes were requested
        if args.codes:
            requested_codes = [code.lower() for code in args.codes]
            links = [(code, url) for code, url in links if code in requested_codes]
            logger.info(f"Filtered to {len(links)} requested code files")
            
            # Warn about any requested codes that weren't found
            found_codes = {code for code, _ in links}
            missing_codes = set(requested_codes) - found_codes
            if missing_codes:
                logger.warning(f"Could not find the following requested codes: {', '.join(missing_codes)}")
        
        # Process each link
        for code_id, url in links:
            try:
                downloader.download_and_extract(code_id, url)
            except Exception as e:
                logger.error(f"Failed to process {code_id}: {e}")
                continue
                
        logger.info("Download and extraction complete")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        raise

if __name__ == "__main__":
    main() 