#!/usr/bin/env python3

# This script is used to enhance the index files with section content from HTML files.
# It reads the index files, locates the corresponding HTML files, and extracts the section content.
# The enhanced index is then saved to a new JSON file.

import json
import sys
import logging
from bs4 import BeautifulSoup
import re
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ProcessingResult:
    """Class to track success/failure of processing operations"""
    success: bool
    error_message: Optional[str] = None
    data: Any = None

class IndexEnhancer:
    """
    Enhances existing statute index files by adding section content from HTML files.
    
    The enhancer:
    1. Reads an existing index.json file
    2. Locates and parses corresponding HTML files
    3. Extracts section content while maintaining structure and hierarchy
    4. Creates a new index_v2.json file with the enhanced content
    """

    def __init__(self, base_path: str = "public", log_level: int = logging.INFO):
        """
        Initialize the IndexEnhancer.
        
        Args:
            base_path: Root directory containing index and statute files
            log_level: Logging level (default: INFO)
        """
        self.base_path = Path(base_path)
        self.setup_logging(log_level)
        
        # Validate base directory structure
        self.validate_directory_structure()

    def setup_logging(self, log_level: int) -> None:
        """
        Configure logging with appropriate format and level.
        
        Args:
            log_level: Desired logging level
        """
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stderr),
                logging.FileHandler('enhance_index.log')
            ]
        )
        self.logger = logging.getLogger(__name__)

    def validate_directory_structure(self) -> None:
        """
        Validate required directories exist in the expected structure.
        Assumes script is being run from within public directory.
        
        Raises:
            FileNotFoundError: If required directories are missing
        """
        required_dirs = [
            'index',
            'Texas'
        ]
        
        for directory in required_dirs:
            dir_path = self.base_path / directory
            if not dir_path.exists():
                error_msg = f"Required directory not found: {dir_path}"
                self.logger.error(error_msg)
                raise FileNotFoundError(error_msg)

    def get_indent_level(self, element: Any) -> int:
        """
        Convert HTML text-indent value to a numeric level.
        
        Args:
            element: BeautifulSoup element containing style attribute
            
        Returns:
            int: Indentation level (0 for base level, 1+ for subsections)
        """
        if 'style' not in element.attrs:
            return 0
            
        style = element['style']
        match = re.search(r'text-indent:(\d+)ex', style)
        if not match:
            return 0
            
        indent = int(match.group(1))
        # Convert indent values to levels:
        # 7ex -> level 1
        # 13ex -> level 2
        # 19ex -> level 3
        # etc.
        return (indent - 1) // 6

    def clean_text(self, text: str) -> str:
        """
        Clean extracted text by removing unwanted whitespace characters.
        
        Args:
            text: Raw text to clean
            
        Returns:
            Cleaned text with normalized whitespace
        """
        if not text:
            return ""
            
        # Remove tabs and newlines, replace with spaces
        cleaned = re.sub(r'[\n\t]+', ' ', text)
        # Remove multiple spaces
        cleaned = re.sub(r'\s+', ' ', cleaned)
        # Strip leading/trailing whitespace
        return cleaned.strip()

    def extract_content(self, section_element: Any) -> Dict[str, Any]:
        """
        Extract content from a section element with proper nesting structure.
        
        Args:
            section_element: BeautifulSoup element containing the section
            
        Returns:
            Dict containing structured section content with:
            - sections: List of section texts and their subsections
            - amendment_history: List of amendment text entries
        """
        content = {
            "sections": [],
            "amendment_history": []
        }

        try:
            content_div = section_element.find('div', class_='section-content')
            if not content_div:
                self.logger.warning("No content div found in section")
                return content

            current_section = None
            section_stack = []
            
            # Process each paragraph in the content
            for p in content_div.find_all('p'):
                text = self.clean_text(p.get_text())
                if not text:
                    continue

                # Check for amendment history
                amendment_patterns = (
                    'Acts ',
                    'Added by',
                    'Amended by',
                    'Renumbered from',
                    'Text of section as amended by',
                    'For text of section as amended by',
                    'Prior law',
                    'Reenacted and amended by',
                )
                if text.startswith(amendment_patterns):
                    # Split multiple amendments if they exist
                    amendments = re.split(r'(?<=\.)\s+(?=(?:Acts|Added by|Amended by|Renumbered from))', text)
                    content["amendment_history"].extend(amendment.strip() for amendment in amendments)
                    continue

                # Get indentation level
                level = self.get_indent_level(p)
                
                # Create new section structure
                new_section = {
                    "text": text,
                    "level": level
                }

                # Handle nesting
                if level == 0:
                    new_section["subsections"] = []
                    content["sections"].append(new_section)
                    current_section = new_section
                    section_stack = [current_section]
                else:
                    # Find appropriate parent by level
                    while section_stack and section_stack[-1]["level"] >= level:
                        section_stack.pop()
                    
                    if section_stack:
                        if "subsections" not in section_stack[-1]:
                            section_stack[-1]["subsections"] = []
                        section_stack[-1]["subsections"].append(new_section)
                    else:
                        # If no parent found, treat as top-level section
                        new_section["subsections"] = []
                        content["sections"].append(new_section)
                        
                    section_stack.append(new_section)

            # Clean up: remove empty subsections arrays
            self._remove_empty_subsections(content["sections"])

        except Exception as e:
            self.logger.error(f"Error extracting content: {str(e)}")
            self.logger.debug("Section element:", section_element)
            raise

        return content

    def _remove_empty_subsections(self, sections: List[Dict[str, Any]]) -> None:
        """
        Recursively remove empty subsections arrays from the content structure.
        
        Args:
            sections: List of sections to clean
        """
        if not sections:
            return
            
        for section in sections:
            if "subsections" in section:
                if not section["subsections"]:
                    del section["subsections"]
                else:
                    self._remove_empty_subsections(section["subsections"])

    def process_html_file(self, file_path: Path) -> ProcessingResult:
        """
        Process a single HTML file and extract section contents.
        
        Args:
            file_path: Path to the HTML file
            
        Returns:
            ProcessingResult containing:
            - success: Boolean indicating success/failure
            - error_message: Description of any error that occurred
            - data: Dictionary mapping section IDs to their content
        """
        try:
            self.logger.info(f"Processing HTML file: {file_path}")
            
            if not file_path.exists():
                return ProcessingResult(
                    False, 
                    f"HTML file not found: {file_path}"
                )

            with open(file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')

            sections = {}
            for section in soup.find_all('div', class_='full-section'):
                try:
                    # Extract section ID from title
                    title_div = section.find('div', class_='section-title')
                    if not title_div:
                        self.logger.warning(f"Section missing title div in {file_path}")
                        continue
                        
                    title_text = title_div.get_text(strip=True)
                    section_match = re.search(
                        r'(Sec\.|Section|Art\.|Article)\s+(\d+[A-Z]?\.\d+[A-Z]?)', 
                        title_text
                    )
                    
                    if not section_match:
                        self.logger.warning(
                            f"Could not extract section ID from title: {title_text}"
                        )
                        continue
                        
                    section_id = section_match.group(2)
                    sections[section_id] = self.extract_content(section)
                    
                    self.logger.debug(f"Successfully processed section {section_id}")

                except Exception as e:
                    self.logger.error(f"Error processing section in {file_path}: {str(e)}")
                    # Continue processing other sections
                    continue

            return ProcessingResult(True, data=sections)

        except Exception as e:
            error_msg = f"Error processing {file_path}: {str(e)}"
            self.logger.error(error_msg)
            return ProcessingResult(False, error_msg)

    def enhance_index(self, index_file: str) -> ProcessingResult:
        """
        Enhance an existing index file with section contents.
        
        Args:
            index_file: Name of the index file to enhance
            
        Returns:
            ProcessingResult indicating success/failure of the enhancement
        """
        try:
            self.logger.info(f"Starting enhancement of {index_file}")
            
            # Read existing index
            index_path = self.base_path / 'index' / index_file
            if not index_path.exists():
                return ProcessingResult(False, f"Index file not found: {index_path}")

            with open(index_path, 'r') as f:
                index = json.load(f)

            # Extract and validate code info
            code_id = index.get('code')
            code_name = index.get('name')
            
            if not code_id or not code_name:
                return ProcessingResult(
                    False,
                    f"Missing code information in {index_file}"
                )

            # Process all chapters
            self._enhance_node(index, code_id, code_name)

            # Save enhanced index
            output_file = self.base_path / 'index' / f'{code_id}.index_v2.json'
            with open(output_file, 'w') as f:
                json.dump(index, f, indent=2)

            self.logger.info(f"Successfully created enhanced index: {output_file}")
            return ProcessingResult(True)

        except Exception as e:
            error_msg = f"Error enhancing index {index_file}: {str(e)}"
            self.logger.error(error_msg)
            return ProcessingResult(False, error_msg)

    def _enhance_node(self, node: Dict[str, Any], code_id: str, code_name: str) -> None:
        """
        Recursively enhance a node in the index.
        
        Args:
            node: Current node in the index hierarchy
            code_id: ID of the current code
            code_name: Name of the current code
        """
        try:
            # Process sections if this node has them
            if node.get('sections') and 'file' in node:
                html_path = self.base_path / node['file'].lstrip('/')
                
                if html_path.exists():
                    result = self.process_html_file(html_path)
                    
                    if result.success and result.data:
                        # Add content to each section
                        for section in node['sections']:
                            if section.get('id') in result.data:
                                section['content'] = result.data[section['id']]
                                self.logger.debug(
                                    f"Added content to section {section['id']}"
                                )
                    else:
                        self.logger.warning(
                            f"Failed to process HTML file: {html_path}"
                        )
                else:
                    self.logger.warning(f"HTML file not found: {html_path}")

            # Recursively process child nodes
            for child_type in ['titles', 'subtitles', 'chapters', 'subchapters']:
                if child_type in node:
                    for child in node[child_type]:
                        self._enhance_node(child, code_id, code_name)

        except Exception as e:
            self.logger.error(f"Error enhancing node: {str(e)}")
            raise

def main():
    """
    Main entry point for the script.
    Process single files or entire directories based on input path type.
    """
    import argparse
    import os
    from pathlib import Path
    
    try:
        # Set up argument parser
        parser = argparse.ArgumentParser(
            description='Enhance statute index files with section content.'
        )
        parser.add_argument(
            'input_path',
            help='Input file or directory containing index files to enhance'
        )
        parser.add_argument(
            'output_path',
            help='Output file (if input is file) or directory (if input is directory)'
        )
        parser.add_argument(
            '--debug',
            action='store_true',
            help='Enable debug logging'
        )
        parser.add_argument(
            '--base-path',
            default='.',
            help='Base path where index and Texas directories are located (default: current directory)'
        )
        
        args = parser.parse_args()
        
        # Set logging level based on debug flag
        log_level = logging.DEBUG if args.debug else logging.INFO
        
        # Initialize enhancer
        enhancer = IndexEnhancer(base_path=args.base_path, log_level=log_level)
        
        input_path = Path(args.input_path)
        output_path = Path(args.output_path)
        
        if not input_path.exists():
            print(f"Error: Input path not found: {input_path}", file=sys.stderr)
            sys.exit(1)
            
        # Handle single file case
        if input_path.is_file():
            if not input_path.name.endswith('.json'):
                print(f"Error: Input file must be a JSON file", file=sys.stderr)
                sys.exit(1)
                
            # Create output directory if needed
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            try:
                print(f"Processing {input_path.name}...")
                
                # Copy file to output location
                import shutil
                shutil.copy2(input_path, output_path)
                
                # Process the index file
                result = enhancer.enhance_index(output_path.name)
                
                if not result.success:
                    print(f"Error processing {input_path.name}: {result.error_message}", 
                          file=sys.stderr)
                    sys.exit(1)
                    
                print("\nProcessing complete: Successfully processed file")
                sys.exit(0)
                
            except Exception as e:
                print(f"Error processing {input_path.name}: {str(e)}", file=sys.stderr)
                sys.exit(1)
        
        # Handle directory case
        else:
            # Create output directory if it doesn't exist
            output_path.mkdir(parents=True, exist_ok=True)
            
            success = True
            processed = 0
            
            # Process all .json files in input directory
            for index_file in input_path.glob('*.json'):
                try:
                    print(f"Processing {index_file.name}...")
                    
                    # Create output filename
                    output_file = output_path / f"{index_file.stem}_v2.json"
                    
                    # Copy file to output directory before processing
                    import shutil
                    shutil.copy2(index_file, output_file)
                    
                    # Process the index file
                    result = enhancer.enhance_index(output_file.name)
                    
                    if result.success:
                        processed += 1
                    else:
                        success = False
                        print(f"Error processing {index_file.name}: {result.error_message}", 
                              file=sys.stderr)
                        
                except Exception as e:
                    success = False
                    print(f"Error processing {index_file.name}: {str(e)}", file=sys.stderr)
                    continue
            
            # Print summary
            print(f"\nProcessing complete:")
            print(f"Successfully processed {processed} files")
            if not success:
                print("Some files failed to process. Check error messages above.", 
                      file=sys.stderr)
                sys.exit(1)
                
            sys.exit(0)

    except Exception as e:
        print(f"Unhandled error: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main()