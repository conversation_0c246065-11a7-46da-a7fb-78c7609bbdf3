#!/usr/bin/env python3

# This script is used to parse the index text files into a hierarchical JSON structure.
# It handles the different hierarchy levels and creates a standardized container entry with code ID.
# The parsed index is then saved to a JSON file.

import re
import json
import sys

# Configuration for file path mapping
CODE_CONFIG = {
    'al': 'Alcoholic Beverage Code',
    'ag': 'Agriculture Code',
    'wl': 'Auxillary Water Laws',
    'bc': 'Business and Commerce Code',
    'bo': 'Business Organizations Code',
    'cp': 'Civil Practice and Remedies Code',
    'cr': 'Code of Criminal Procedure',
    'ed': 'Education Code',
    'el': 'Election Code',
    'es': 'Estates Code',
    'fa': 'Family Code',
    'fi': 'Finance Code',
    'gv': 'Government Code',
    'hs': 'Health and Safety Code',
    'hr': 'Human Resources Code',
    'in': 'Insurance Code',
    'i1': 'Insurance Code - Not Codified',
    'la': 'Labor Code',
    'lg': 'Local Government Code',
    'nr': 'Natural Resources Code',
    'oc': 'Occupations Code',
    'pw': 'Parks and Wildlife Code',
    'pe': 'Penal Code',
    'pr': 'Property Code',
    'sd': 'Special District Local Laws Code',
    'tx': 'Tax Code',
    'cn': 'Texas Constitution',
    'tn': 'Transportation Code',
    'ut': 'Utilities Code',
    'cv': 'Vernon\'s Civil Statutes',
    'wa': 'Water Code',
}

def extract_id(text):
    """Extract ID from different hierarchy levels"""
    section_match = re.match(r'(?:Sec\.|Section|Art\.|Article)\s*(\d+(?:[A-Za-z])?(?:\.\d+)?[A-Za-z\-]*)', text.rstrip('.'))
    if section_match:
        return section_match.group(1)
    
    clean_text = re.sub(r'^(TITLE|SUBTITLE|CHAPTER|SUBCHAPTER|ARTICLE)\s*', '', text.strip())
    clean_text = clean_text.split('.')[0].split('(')[0].strip()
    
    return clean_text

def create_container_entry(line, container_type, code_id):
    """Create a standardized container entry with code ID"""
    entry = {
        "name": line,
        "code": code_id,
        "type": container_type.rstrip('s'),
        "id": extract_id(line),
    }
    return entry

def parse_index(input_file):
    """Parse index text file into hierarchical JSON structure"""
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Extract filename from path and get code prefix
    filename = input_file.split('/')[-1]
    code_prefix = filename[:2].lower()
    
    # Initialize root structure
    index = {
        "name": CODE_CONFIG.get(code_prefix, lines[0].strip()).upper(),
        "code": code_prefix,
        "type": "code",
        "id": code_prefix,
    }
    
    current_containers = {
        'title': None,
        'subtitle': None,
        'chapter': None,
        'subchapter': None
    }
    
    current_container = index
    
    for line in lines[1:]:
        line = line.strip()
        if not line:
            continue
            
        if line.startswith('TITLE'):
            current_containers['title'] = create_container_entry(line, 'titles', code_prefix)
            index['titles'] = index.get('titles', [])
            index['titles'].append(current_containers['title'])
            current_containers['subtitle'] = None
            current_containers['chapter'] = None
            current_containers['subchapter'] = None
            current_container = current_containers['title']
            continue
            
        if line.startswith('SUBTITLE'):
            current_containers['subtitle'] = create_container_entry(line, 'subtitles', code_prefix)
            if current_containers['title']:
                current_containers['title']['subtitles'] = current_containers['title'].get('subtitles', [])
                current_containers['title']['subtitles'].append(current_containers['subtitle'])
            current_containers['chapter'] = None
            current_containers['subchapter'] = None
            current_container = current_containers['subtitle']
            continue
            
        if line.startswith('CHAPTER'):
            current_containers['chapter'] = create_container_entry(line, 'chapters', code_prefix)
            if code_prefix in CODE_CONFIG:
                current_containers['chapter']['file'] = f"/Texas/{CODE_CONFIG[code_prefix]}/{code_prefix}.{current_containers['chapter']['id'].lower()}.html"
            
            if current_containers['subtitle']:
                current_containers['subtitle']['chapters'] = current_containers['subtitle'].get('chapters', [])
                current_containers['subtitle']['chapters'].append(current_containers['chapter'])
            elif current_containers['title']:
                current_containers['title']['chapters'] = current_containers['title'].get('chapters', [])
                current_containers['title']['chapters'].append(current_containers['chapter'])
            else:
                index['chapters'] = index.get('chapters', [])
                index['chapters'].append(current_containers['chapter'])
            
            current_containers['subchapter'] = None
            current_container = current_containers['chapter']
            continue
            
        if line.startswith('SUBCHAPTER'):
            current_containers['subchapter'] = create_container_entry(line, 'subchapters', code_prefix)
            if current_containers['chapter']:
                current_containers['subchapter']['file'] = current_containers['chapter']['file']
                current_containers['chapter']['subchapters'] = current_containers['chapter'].get('subchapters', [])
                current_containers['chapter']['subchapters'].append(current_containers['subchapter'])
            current_container = current_containers['subchapter']
            continue
            
        if re.match(r'(?:Sec\.|Section|Art\.|Article)', line):
            section = {
                "name": line,
                "code": code_prefix,
                "type": "section",
                "id": extract_id(line),
                "file": current_containers['chapter']['file'] if current_containers['chapter'] else ""  # Set section file to parent chapter's file
            }
            
            if current_containers['subchapter']:
                current_containers['subchapter']['sections'] = current_containers['subchapter'].get('sections', [])
                current_containers['subchapter']['sections'].append(section)
            elif current_containers['chapter']:
                current_containers['chapter']['sections'] = current_containers['chapter'].get('sections', [])
                current_containers['chapter']['sections'].append(section)
    
    return index


def main():
    if len(sys.argv) != 3:
        print("Usage: python index_parser.py input.txt output.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    parsed_index = parse_index(input_file)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(parsed_index, f, indent=4, ensure_ascii=False)
    
    print(f"Parsed index saved to {output_file}")

if __name__ == "__main__":
    main()