#!/usr/bin/env python3

# This script parses the Texas Constitution and creates an index of all articles.
# It handles the preamble and appendix separately, and processes all other articles in a unified manner.
# Source: https://statutes.capitol.texas.gov/Docs/Zips/CN.htm.zip
# from https://statutes.capitol.texas.gov/Download.aspx

import os
import json
import re
from bs4 import BeautifulSoup
from pathlib import Path
import argparse
from typing import Dict, Any


def extract_section_name(text):
    """Extract section name from text."""
    # Match section names with various patterns:
    # - Standard sections (e.g., "Sec. 1. FREEDOM AND SOVEREIGNTY OF STATE.")
    # - Sections with letters (e.g., "Sec. 1-A.", "Sec. 49-b.")
    # - Sections with numbers (e.g., "Sec. 49-d-1.", "Sec. 50b-4.")
    # - Sections with mixed case (e.g., "Sec. 50b-6A.")

    # Handle special cases first: repealed, expired, and blank sections
    if '(Repealed' in text:
        match = re.match(r'^Sec\.\s+(\d+(?:[a-z]|-[a-z])?)', text)
        if match:
            section_num = match.group(1)
            return f"Sec. {section_num}. Repealed"

    if '(Blank' in text:
        match = re.match(r'^Sec\.\s+(\d+(?:[a-z]|-[a-z])?)', text)
        if match:
            section_num = match.group(1)
            return f"Sec. {section_num}. Blank"

    if '; expired' in text:
        match = re.match(r'^Sec\.\s+(\d+(?:[a-z]|-[a-z])?)', text)
        if match:
            section_num = match.group(1)
            return f"Sec. {section_num}. Expired"

    # Find the first three periods in the text
    if not text.startswith('Sec.'):
        return ''

    periods = [i for i, char in enumerate(text) if char == '.']
    if len(periods) < 3:
        return ''

    # Extract everything up to the third period
    section_name = text[:periods[2]]
    return section_name


def extract_section_text(text, name):
    """Extract section text, removing section name."""

    # Skip empty text or if no name to remove
    if not text or not name:
        return text

    # For repealed sections, only return the parenthetical part
    if '(Repealed' in text:
        match = re.search(r'\((Repealed[^)]+)\)', text)
        if match:
            return f"({match.group(1)})"

    # For blank sections, only return the parenthetical part
    if '(Blank' in text:
        match = re.search(r'\((Blank[^)]+)\)', text)
        if match:
            return f"({match.group(1)})"

    # For expired sections, only return the parenthetical part
    if '; expired' in text:
        match = re.search(r'\((.*?)\)', text)
        if match:
            return f"{match.group(0)}"

    # Simply remove the section name and any following whitespace/periods
    if text.startswith(name):
        text = text[len(name):]
        text = text.lstrip(' .')

    return text


def parse_content_level(indent):
    """Determine content level based on indentation."""
    match = re.search(r'(\d+)ex', indent)
    if not match:
        return 1

    level = int(match.group(1))
    # Convert indent values to levels:
    # 7ex -> level 1
    # 13ex -> level 2
    # 19ex -> level 3
    # etc.
    return (level - 1) // 6


def is_amendment_history(text):
    """Determine if text is amendment history."""
    # Skip empty text
    if not text.strip():
        return False

    # Check if it's a repealed subsection marker - pattern like "(e)  (Repealed.)"
    if re.match(r'^\([a-zA-Z0-9]\)\s+\(Repealed\.?\)$', text.strip()):
        return False

    # First check if it's a subsection marker - if so, return False
    # Match (a), (A), (1), (i), (I), etc.
    if re.match(r'^\([a-zA-Z0-9]\)$', text.strip()) or re.match(r'^\([ivxIVX]+\)$', text.strip()):
        return False

    # If it's a single-letter subsection with content, it's not amendment history
    if re.match(r'^\([a-zA-Z0-9]\)\s+[A-Z][a-z]', text.strip()):
        return False

    # Common amendment history patterns
    if text.strip().startswith('(') and text.strip().endswith(')'):
        # Catch-all for any parenthetical text with at least 6 chars between parentheses
        if re.match(r'^\(.{6,}\)$', text.strip()):
            return True

        # Check for simple date format first
        if re.match(r'^\([A-Z][a-z]+\.?\s+\d{1,2},\s+\d{4}\.\)$', text.strip()):
            return True

        # Check for complex amendment histories
        if any(keyword in text for keyword in [
            'Added', 'amended', 'Amended', 'repealed', 'Repealed',
            'deleted', 'redesignated', 'expired', 'combined', 'reenacted',
            'TEMPORARY', 'Former', 'Text of'
        ]):
            # Count opening and closing parentheses
            open_count = text.count('(')
            close_count = text.count(')')

            # If they match and there's at least one amendment keyword, it's likely amendment history
            if open_count == close_count:
                return True

    # Additional patterns for special cases
    patterns = [
        r'^Text of (?:section|subsection) as (?:added|amended|proposed)',
        r'^Former (?:section|subsection) .+ (?:repealed|amended|expired)',
        r'^TEMPORARY PROVISION',
        r'^TEMPORARY TRANSITION PROVISION',
        r'^†.*'  # Footnotes
    ]

    # Check if text matches any pattern
    for pattern in patterns:
        if re.match(pattern, text.strip()):
            return True

    return False


def extract_subsection_id(text):
    """Extract subsection ID from text like (a), (1), (A), etc."""
    match = re.match(r'^(\([a-zA-Z0-9-]+\))\s+', text)
    return match.group(1) if match else "(0)"


def parse_section_content(paragraphs, is_appendix=False):
    """Parse section content from paragraphs."""
    subsections = []
    amendment_history = []
    current_sections = {}  # Track current section at each level

    seen_amendments = set()

    level_map = {1: "subsection", 2: "subdivision", 3: "subparagraph", 4: "subpart", 5: "subitem"}

    for p in paragraphs:
        original_text = p.get_text().strip()
        if not original_text:
            continue

        # Only check for amendment history if not in appendix
        if not is_appendix and is_amendment_history(original_text):
            if original_text not in seen_amendments:
                amendment_history.append(original_text)
                seen_amendments.add(original_text)
            continue

        # Check if this is a footnote
        if original_text.startswith('†'):
            link = p.find('a')
            if link:
                original_text += f" {link['href']}"
            if not is_appendix and original_text not in seen_amendments:
                amendment_history.append(original_text)
                seen_amendments.add(original_text)
            continue

        # Skip the HJR/SJR header as it's handled as the section name
        if re.match(r'^\d+\.\s+[HS]\.J\.R\.', original_text):
            continue

        # Parse regular content
        indent = p.get('style', '').split('text-indent:')[1].split(';')[0] if 'text-indent:' in p.get('style', '') else '7ex'
        level = parse_content_level(indent)

        # Extract section name if present and get cleaned text
        text = original_text
        section_name = extract_section_name(original_text)
        if section_name:
            text = extract_section_text(original_text, section_name)
            current_sections[level] = section_name

            # Clean up any deeper levels since we're at a new section
            deeper_levels = [l for l in current_sections.keys() if l > level]
            for l in deeper_levels:
                del current_sections[l]

        # Handle temporary provisions
        temporary_provisions = ["TEMPORARY PROVISION.", "TEMPORARY TRANSITION PROVISION."]
        is_temporary = False
        for provision in temporary_provisions:
            if text.startswith(provision):
                is_temporary = True
                # Split into title and content parts
                title_part = provision
                content_part = text[len(title_part):].strip()

                # Add title subsection
                subsections.append({
                    "text": title_part,
                    "level": level,
                    "type": level_map.get(level, "subsection"),
                    "code": "cn",
                    "id": "(0)"
                })

                # Add content subsection if it exists
                if content_part:
                    subsections.append({
                        "text": content_part,
                        "level": level,
                        "type": level_map.get(level, "subsection"),
                        "code": "cn",
                        "id": extract_subsection_id(content_part)
                    })
                break
        if is_temporary:
            continue

        subsection_id = extract_subsection_id(text)
        subsection_type = level_map.get(level, "subsection")

        content = {
            "text": text,
            "level": level,
            "type": subsection_type,
            "code": "cn",
            "id": subsection_id
        }

        # Add content to appropriate level
        if level == 1:
            subsections.append(content)
            current_sections[1] = content
        else:
            parent_level = level - 1
            if parent_level in current_sections:
                parent = current_sections[parent_level]
                if 'subsections' not in parent:
                    parent['subsections'] = []
                parent['subsections'].append(content)
                current_sections[level] = content

    return subsections, amendment_history


def parse_html_file(file_path):
    """Parse a constitution HTML file and extract article information."""
    print(f"Parsing {file_path}")

    with open(file_path, 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f.read(), 'html.parser')

    # Extract article title - find all center paragraphs and get the second one
    title_elems = soup.find_all('p', class_='center')
    if len(title_elems) < 2:
        print(f"Could not find article title in {file_path}")
        return None
    article_name = title_elems[1].get_text().strip()

    # Get article ID
    if 'PREAMBLE' in article_name:
        article_id = 'pr'
    elif 'APPENDIX' in article_name:
        article_id = 'ap'
    else:
        match = re.search(r'ARTICLE (\d+)\.', article_name)
        if not match:
            print(f"Could not extract article ID from title: {article_name}")
            return None
        article_id = match.group(1)

    # Special handling for constitution preamble
    if article_id == 'pr':
        return parse_preamble(article_name, article_id, soup)
    elif article_id == 'ap':
        return
    else:
        return parse_article(article_name, article_id, soup)


def parse_preamble(article_name, article_id, soup):
    """Parse the constitution preamble article."""
    preamble_text = soup.find('p', style=lambda s: s and 'text-indent:7ex' in s)
    if preamble_text:
        preamble_article = {
            'text': article_name,
            'code': 'cn',
            'type': 'article',
            'id': article_id,
            'sections': [{
                'id': 'pr',
                'text': 'PREAMBLE',
                'type': 'section',
                'code': 'cn',
                'subsections': [{
                    'text': preamble_text.get_text().strip(),
                    'type': 'subsection',
                    'id': '(0)',
                    'code': 'cn',
                    'level': 1,
                }],
            }]
        }
        return preamble_article
    return None


def parse_article(article_name, article_id, soup):
    """Parse article content from soup."""
    sections = []
    subarticles = []
    current_section = None
    current_subarticle = None
    section_paragraphs = []

    # Get all paragraphs
    paragraphs = soup.find_all('p')

    for para in paragraphs:
        text = para.get_text().strip()
        if not text:
            continue

        # Check for subarticle header
        if is_valid_subarticle_header(text, para):
            # Save current section if exists
            if current_section and section_paragraphs:
                subsections, amendment_history = parse_section_content(section_paragraphs)
                current_section['subsections'] = subsections
                if amendment_history:
                    current_section['amendment_history'] = amendment_history
                if current_subarticle:
                    if 'sections' not in current_subarticle:
                        current_subarticle['sections'] = []
                    current_subarticle['sections'].append(current_section)
                else:
                    sections.append(current_section)
                section_paragraphs = []
                current_section = None

            # Create new subarticle
            current_subarticle = {
                'text': text,
                'code': 'cn',
                'type': 'subarticle',
                'id': text,
                'sections': []
            }
            subarticles.append(current_subarticle)
            continue

        # Check for section name
        if text.startswith('Sec.'):
            # If we have a current section, save it
            if current_section and section_paragraphs:
                subsections, amendment_history = parse_section_content(section_paragraphs)
                current_section['subsections'] = subsections
                if amendment_history:
                    current_section['amendment_history'] = amendment_history
                if current_subarticle:
                    current_subarticle['sections'].append(current_section)
                else:
                    sections.append(current_section)
                section_paragraphs = []

            # Extract section name and create new section
            section_name = extract_section_name(text)
            if section_name:
                section_match = re.match(r'^Sec\.\s+(\d+(?:[a-zA-Z0-9-]+)?)', text)
                section_id = section_match.group(1) if section_match else None

                if section_id:
                    current_section = {
                        'text': section_name,
                        'code': 'cn',
                        'type': 'section',
                        'id': section_id,
                    }
                    section_paragraphs = [para]
        else:
            # Add paragraph to current section if we have one
            if current_section:
                section_paragraphs.append(para)

    # Add last section
    if current_section and section_paragraphs:
        subsections, amendment_history = parse_section_content(section_paragraphs)
        current_section['subsections'] = subsections
        if amendment_history:
            current_section['amendment_history'] = amendment_history
        if current_subarticle:
            current_subarticle['sections'].append(current_section)
        else:
            sections.append(current_section)

    return {
        'text': article_name,
        'code': 'cn',
        'type': 'article',
        'id': article_id,
        'sections': sections,
        'subarticles': subarticles if subarticles else None
    }


def create_constitution_index(input_dir, output_dir):
    """Create index of all constitution articles.

    Args:
        input_dir (Path): Directory containing the constitution HTML files
        output_dir (Path): Directory where the index file will be written
    """
    # Ensure directories exist
    input_dir = Path(input_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Initialize index structure
    index = {
        'text': 'TEXAS CONSTITUTION',
        'code': 'cn',
        'type': 'code',
        'id': 'cn',
        'articles': []
    }

    # First process preamble
    preamble_file = input_dir / 'cn.pr.htm'
    if preamble_file.exists():
        article = parse_html_file(preamble_file)
        if article:
            index['articles'].append(article)

    # Sort files by article number
    def article_number_sort(file_path):
        # Extract number from filename (e.g., 'cn.1.htm' -> 1)
        match = re.match(r'cn\.(\d+)\.htm', file_path.name)
        return int(match.group(1)) if match else float('inf')

    # Process all cn.*.htm files in numeric order
    for file_path in sorted(input_dir.glob('cn.*.htm'), key=article_number_sort):
        if file_path.name not in ['cn.pr.htm', 'cn.ap.htm']:  # Skip preamble and appendix
            article = parse_html_file(file_path)
            if article:
                index['articles'].append(article)
            else:
                print(f"Failed to parse {file_path}")

    # Process appendix
    appendix_file = input_dir / 'cn.ap.htm'
    if appendix_file.exists():
        article = parse_appendix_file(appendix_file)
        if article:
            index['articles'].append(article)
        else:
            print(f"Failed to parse appendix file at {appendix_file}")
    else:
        print(f"Could not find appendix file at {appendix_file}")

    # Sort articles (preamble first, appendix last, others by number)
    def article_sort_key(article):
        if article['id'] == 'pr':
            return -1
        if article['id'] == 'ap':
            return 999
        return int(article['id'])

    index['articles'].sort(key=article_sort_key)

    # Write index file
    output_path = output_dir / 'cn.index.json'
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(index, f, indent=2, ensure_ascii=False)

    print(f"\nCreated constitution index at {output_path}")
    print(f"Processed {len(index['articles'])} articles")


def parse_appendix_file(file_path):
    """Parse the constitution appendix file."""
    print(f"Parsing {file_path}")
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    soup = BeautifulSoup(content, 'html.parser')

    # Extract article title
    title_elems = soup.find_all('p', class_='center')
    if len(title_elems) < 2:
        print(f"Could not find article title in {file_path}")
        return None

    article_name = title_elems[1].get_text().strip()

    sections = []

    # Find all paragraphs
    paragraphs = soup.find_all('p', class_='left')

    # Process each paragraph
    i = 0
    while i < len(paragraphs):
        p = paragraphs[i]
        text = p.get_text().strip()
        if not text:
            i += 1
            continue

        # Check if this is a section header - now handles both H.J.R. and S.J.R.
        match = re.match(r'^(\d+)\.\s+[HS]\.J\.R\.', text)
        if match:
            current_section_num = int(match.group(1))
            section_paragraphs = [p]

            # Look ahead to find where this section ends
            j = i + 1
            while j < len(paragraphs):
                next_text = paragraphs[j].get_text().strip()
                if not next_text:
                    j += 1
                    continue

                # Check if we've hit the next section - also handle both H.J.R. and S.J.R.
                next_match = re.match(r'^(\d+)\.\s+[HS]\.J\.R\.', next_text)
                if next_match and int(next_match.group(1)) == current_section_num + 1:
                    break

                section_paragraphs.append(paragraphs[j])
                j += 1

            # Create section
            subsections, amendment_history = parse_section_content(section_paragraphs, is_appendix=True)
            section = {
                'id': str(current_section_num),
                'type': 'section',
                'code': 'cn',
                'text': section_paragraphs[0].get_text().strip(),  # Use the HJR/SJR header as the name
                'subsections': subsections,
            }
            if amendment_history:  # Only add amendment_history if it's not empty
                section['amendment_history'] = amendment_history
            sections.append(section)

            i = j  # Move to the next section
        else:
            i += 1

    return {
        'text': article_name,
        'code': 'cn',
        'type': 'article',
        'id': 'ap',
        'sections': sections
    }


def is_valid_subarticle_header(text, para):
    """
    Determine if a paragraph is a valid subarticle header.
    Only 9 specific subarticles exist in the Texas Constitution.
    """
    VALID_SUBARTICLES = {
        "PROCEEDINGS",
        "REQUIREMENTS AND LIMITATIONS",
        "THE PUBLIC FREE SCHOOLS",
        "ASYLUMS",
        "UNIVERSITY",
        "COUNTY SEATS",
        "HOME RULE CHARTERS",
        "HOSPITAL DISTRICTS",
        "ADDRESS"
    }

    # Must be centered and uppercase
    if not (para.get('class') == ['center'] and text.isupper()):
        return False

    # Must exactly match one of our known valid subarticles
    return text in VALID_SUBARTICLES


def main():
    parser = argparse.ArgumentParser(description='Create index file from HTML constitution files')
    parser.add_argument('--input-dir', '-i', type=Path,
                       help='Directory containing HTML files (default: script directory)',
                       default=Path(__file__).resolve().parent.parent / "data" / "tx" / "cn")
    parser.add_argument('--output-dir', '-o', type=Path,
                       help='Directory to write index file (default: script directory)',
                       default=Path(__file__).resolve().parent.parent / "public" / "index" / "tx")

    args = parser.parse_args()
    create_constitution_index(args.input_dir, args.output_dir)


if __name__ == '__main__':
    main()
