#!/usr/bin/env python3

# This script is used to transform the HTML files into a structured format.
# It extracts the hierarchy and transforms the content into a new format.
# The transformed content is then saved to a new HTML file.

from bs4 import BeautifulSoup
import re
import sys
import glob
import os
from pathlib import Path

class StatuteTransformer:
    def __init__(self, input_html):
        """Initialize the transformer with input HTML content."""
        self.soup = BeautifulSoup(input_html, 'html.parser')
        self.output = []
        self.debug = True  # Set debug to True by default

    def log_debug(self, message, data=None):
        """Print debug message if debug mode is enabled."""
        if self.debug:
            debug_msg = f"DEBUG: {message}"
            if data is not None:
                debug_msg += f"\nDATA: {repr(data)}"
            print(debug_msg, file=sys.stderr)

    def extract_hierarchy(self):
        """Extract hierarchical elements."""
        pre_tag = self.soup.find('pre')
        if not pre_tag:
            raise ValueError("Could not find pre tag containing statute content")

        self.log_debug("Found pre tag, searching for center paragraphs")
        center_paragraphs = pre_tag.find_all('p', class_='center')
        
        hierarchy = {
            'code': None,
            'titles': [],
            'subtitles': [],
            'chapters': [],
            'subchapters': []
        }
        
        for p in center_paragraphs:
            text = p.text.strip()
            if not text:
                continue
            
            self.log_debug(f"Processing center paragraph: {text}")
            
            if (text.startswith('CODE') or text.endswith('CODE') or text.endswith('LAWS')) and (not text.startswith('SUBCHAPTER') and not text.startswith('CHAPTER') and not text.startswith('TITLE') and not text.startswith('SUBTITLE')):     
                hierarchy['code'] = text
                self.log_debug(f"Found code: {text}")
            elif text.startswith('TITLE'):
                hierarchy['titles'].append(text)
                self.log_debug(f"Found title: {text}")
            elif text.startswith('SUBTITLE'):
                hierarchy['subtitles'].append(text)
                self.log_debug(f"Found subtitle: {text}")
            elif text.startswith('CHAPTER'):
                hierarchy['chapters'].append(text)
                self.log_debug(f"Found chapter: {text}")
            elif text.startswith('SUBCHAPTER'):
                hierarchy['subchapters'].append(text)
                self.log_debug(f"Found subchapter: {text}")
        
        self.log_debug("Final hierarchy structure:", hierarchy)
        return hierarchy

    def transform(self):
        """Transform the document while maintaining complete hierarchy."""
        self.log_debug("Starting transformation...")
        hierarchy = self.extract_hierarchy()
        doc_structure = []
        
        doc_structure.append('<!-- Start Statute Document -->')
        doc_structure.append('<div class="statute-container">')
        
        if hierarchy['code']:
            self.log_debug(f"Processing code: {hierarchy['code']}")
            doc_structure.append(f'\t<h1 class="code">{hierarchy["code"]}</h1>')
        
        for title in hierarchy['titles']:
            self.log_debug(f"Processing title: {title}")
            doc_structure.append('\t<div class="title-container">')
            doc_structure.append(f'\t\t<h2 class="title">{title}</h2>')
            
            if hierarchy['subtitles']:
                self.log_debug("Found subtitles, processing with subtitle structure")
                for subtitle in hierarchy['subtitles']:
                    self.log_debug(f"Processing subtitle: {subtitle}")
                    doc_structure.append('\t\t<div class="subtitle-container">')
                    doc_structure.append(f'\t\t\t<h3 class="subtitle">{subtitle}</h3>')
                    
                    self._process_chapters(doc_structure, hierarchy['chapters'], '\t\t\t')
                    
                    doc_structure.append('\t\t</div><!-- close subtitle-container -->')
            else:
                self.log_debug("No subtitles found, processing chapters directly under title")
                self._process_chapters(doc_structure, hierarchy['chapters'], '\t\t')
                
            doc_structure.append('\t</div><!-- close title-container -->')
        
        doc_structure.append('</div><!-- close statute-container -->')
        doc_structure.append('<!-- End Statute Document -->')
        
        self.log_debug("Transformation complete")
        self.output = doc_structure
        return '\n'.join(doc_structure)

    def _process_chapters(self, doc_structure, chapters, base_indent):
        """Helper method to process chapters with proper indentation."""
        for chapter in chapters:
            self.log_debug(f"Processing chapter: {chapter}")
            doc_structure.append(f'{base_indent}<div class="chapter-container">')
            doc_structure.append(f'{base_indent}\t<h3 class="chapter">{chapter}</h3>')
            
            sections = self.get_sections()
            current_subchapter = None
            in_subchapter = False
            
            for section_elements in sections:
                if not section_elements:
                    continue
                
                text = section_elements[0].text.strip()
                self.log_debug(f"Processing section element: {text[:50]}...")
                
                if text.startswith('SUBCHAPTER'):
                    if in_subchapter:
                        doc_structure.append(f'{base_indent}\t</div><!-- close subchapter-container -->')
                    current_subchapter = text.split()[1].rstrip('.')
                    doc_structure.append(f'{base_indent}\t<div class="subchapter-container">')
                    doc_structure.append(f'{base_indent}\t\t<h3 class="subchapter">{text}</h3>')
                    in_subchapter = True
                    self.log_debug(f"Found subchapter: {text}")
                    continue
                
                if text.startswith('Sec.') or text.startswith('SECTION ') or text.startswith('Art.') or text.startswith('ARTICLE '):
                    transformed = self.transform_section(
                        section_elements,
                        current_subchapter if current_subchapter else None
                    )
                    if transformed:
                        indent = f'{base_indent}\t\t' if in_subchapter else f'{base_indent}\t'
                        transformed_lines = transformed.split('\n')
                        transformed = '\n'.join(indent + line for line in transformed_lines if line)
                        doc_structure.append(transformed)
                        self.log_debug(f"Transformed section added with indent: {indent}")
            
            if in_subchapter:
                doc_structure.append(f'{base_indent}\t</div><!-- close subchapter-container -->')
            
            doc_structure.append(f'{base_indent}</div><!-- close chapter-container -->')

    def get_sections(self):
        """Extract all section elements from the document."""
        pre_tag = self.soup.find('pre')
        if not pre_tag:
            return []

        self.log_debug("Getting sections from document")
        paragraphs = pre_tag.find_all('p')
        sections = []
        current_section = []
        
        for p in paragraphs:
            text = p.text.strip()
            if not text:
                continue

            # Check for subchapter headers
            if text.startswith('SUBCHAPTER'):
                if current_section:
                    sections.append(current_section)
                current_section = [p]
                self.log_debug(f"Found subchapter: {text}")
                continue
            
            # Start new section when we hit "Sec."
            if text.startswith('Sec.') or text.startswith('SECTION ') or text.startswith('Art.') or text.startswith('ARTICLE '):
                if current_section:
                    sections.append(current_section)
                current_section = [p]
                self.log_debug(f"Found section: {text[:50]}...")
            # Add amendment history
            elif text.startswith('Acts ') or text.startswith('Added by ') or text.startswith('Amended by:'):
                if current_section:
                    current_section.append(p)
                    self.log_debug("Added amendment history")
            # Add other paragraph content
            elif current_section:
                current_section.append(p)
                self.log_debug("Added section content")
        
        # Don't forget to add the last section
        if current_section:
            sections.append(current_section)
            
        self.log_debug(f"Total sections found: {len(sections)}")
        return sections

    def transform_section(self, section_elements, subchapter=None):
        """Transform a section into the new format."""
        if not section_elements:
            self.log_debug("No section elements to transform")
            return None

        first_element = section_elements[0]
        text_content = first_element.text.strip()

        self.log_debug(f"Transforming section: {text_content[:50]}...")

        # Handle subchapter headers
        if text_content.startswith('SUBCHAPTER'):
            self.log_debug("Found subchapter header: " + text_content)
            return f'<h3 class="subchapter">{text_content}</h3>'
        
        if not (text_content.startswith('Sec.') or text_content.startswith('SECTION ') or text_content.startswith('Art.') or text_content.startswith('ARTICLE ')):  
            self.log_debug("Section does not start with expected pattern: Sec., SECTION, Art., or ARTICLE")
            return None
        
        section_info = self.extract_section_title_and_content(first_element)
        if not section_info:
            self.log_debug("Could not extract section info from first element")
            return None

        section_div = []
        section_div.append('<div class="full-section">')
        
        # Add metadata
        section_div.append(f'<div class="section-metadata"')
        section_div.append(f' data-chapter="{section_info["chapter"]}"')
        section_div.append(f' data-section="{section_info["section"]}"')
        if subchapter:
            section_div.append(f' data-subchapter="{subchapter}"')
        section_div.append('></div>')
        
        # Add title
        section_div.append('<div class="section-title">')
        section_div.append(f'<h4>{section_info["full_title"]}</h4>')
        section_div.append('</div>')
        
        # Add content
        section_div.append('<div class="section-content">')
        
        # Add the initial content if it exists
        if section_info.get('content'):
            style = 'style="text-indent:7ex;"'
            section_div.append(f'<p class="section-text" {style}>{section_info["content"]}</p>')
        
        # Process remaining elements
        current_subsection = None
        in_amendment_history = False
        
        for element in section_elements[1:]:
            text = element.text.strip()
            if not text:
                continue
                
            if self.is_amendment_history(text):
                if not in_amendment_history:
                    section_div.append('<div class="amendment-history">')
                    in_amendment_history = True
                section_div.append(f'<p>{text}</p>')
            else:
                class_name = self.get_section_class(element)
                style_attr = f' style="{element["style"]}"' if 'style' in element.attrs else ''
                
                if class_name.startswith('subsection-level-') and class_name != current_subsection:
                    if current_subsection:
                        section_div.append('</div>')
                    section_div.append(f'<div class="{class_name}-container">')
                    current_subsection = class_name
                
                section_div.append(f'<p class="{class_name}"{style_attr}>{text}</p>')
        
        if current_subsection:
            section_div.append('</div>')
            
        if in_amendment_history:
            section_div.append('</div>')
            
        section_div.append('</div>')  # close section-content
        section_div.append('</div>')  # close full-section
        
        return '\n'.join(section_div)

    def extract_section_title_and_content(self, element):
        """Extract the section title and content from an element."""
        try:
            # Get the full text content
            text_content = element.text.strip()
            
            # Match the section number and title pattern
            title_match = re.match(r'((Sec\.|Section|Art\.|Article)\s+\d+[A-Z]?\.\d+[A-Z]?\.\s+[^.]+)\.', text_content)
            if not title_match:
                self.log_debug(f"Could not match section title pattern in: {text_content[:50]}...")
                return None

            title_text = title_match.group(1)
            
            # Extract section info
            section_match = re.match(r'(Sec\.|Section|Art\.|Article)\s+(\d+[A-Z]?\.\d+[A-Z]?)\.\s+([^.]+)', title_text)
            if not section_match:
                self.log_debug(f"Could not extract section info from: {title_text}")
                return None
                
            # Get content by removing the title from the text
            full_text = element.get_text(separator=' ', strip=True)
            content_text = full_text[len(title_text) + 1:].strip()  # +1 for the period

            return {
                'full_title': title_text,
                'chapter': section_match.group(2).split('.')[0],
                'section': section_match.group(2),
                'title': section_match.group(3).strip(),
                'content': content_text if content_text else None
            }
        except Exception as e:
            self.log_debug(f"Error in extract_section_title_and_content: {str(e)}")
            self.log_debug(f"Element text: {element.text[:100]}")
            return None

    def get_section_class(self, element):
        """Determine section class based on indentation level."""
        indent_value = self.get_indent_value(element)
        
        if indent_value <= 7:
            return "section-text"
        elif indent_value <= 13:
            return "subsection-level-1"
        elif indent_value <= 19:
            return "subsection-level-2"
        else:
            indent_level = (indent_value - 7) // 6
            return f"subsection-level-{indent_level}"

    def get_indent_value(self, element):
        """Extract the text-indent value from style attribute."""
        if 'style' not in element.attrs:
            return 0
            
        style = element['style']
        match = re.search(r'text-indent:(\d+)ex', style)
        return int(match.group(1)) if match else 0

    def is_amendment_history(self, text):
        """Check if the text is part of amendment history."""
        text = text.strip()
        return (text.startswith('Acts ') or 
                text.startswith('Added by ') or 
                text.startswith('Amended by:') or
                text.startswith('Amended by '))

def get_output_filename(input_pattern, output_pattern, input_file):
    """Generate output filename based on patterns."""
    # Handle single file case (no wildcards)
    if '*' not in input_pattern:
        return output_pattern
        
    # Handle batch processing case
    # Extract the wildcard part from the input filename
    input_prefix = input_pattern[:input_pattern.index('*')]
    input_suffix = input_pattern[input_pattern.index('*')+1:]
    
    # Extract the matching part from the actual input filename
    wildcard_part = input_file[len(input_prefix):-len(input_suffix)]
    
    # Replace wildcard in output pattern with matching part
    return output_pattern.replace('*', wildcard_part)


def transform_statute_files(input_pattern, output_pattern, debug=True):
    """Transform statute files from original format to semantic format."""
    print(f"Starting transformation of files matching: {input_pattern}", file=sys.stderr)
    
    # Get list of input files matching pattern
    input_files = glob.glob(input_pattern)
    
    if not input_files:
        print(f"No files found matching pattern: {input_pattern}", file=sys.stderr)
        return False
        
    success = True
    for input_file in input_files:
        try:
            # Generate output filename for this input file
            output_file = get_output_filename(input_pattern, output_pattern, input_file)
            print(f"Processing {input_file} -> {output_file}", file=sys.stderr)
            
            # Read input content
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Transform content
            transformer = StatuteTransformer(content)
            transformer.debug = debug
            transformed_content = transformer.transform()
            
            # Create output directory if needed
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Write transformed content
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(transformed_content)
                
            print(f"Successfully transformed {input_file} to {output_file}", file=sys.stderr)
            
        except Exception as e:
            print(f"Error processing file {input_file}: {str(e)}", file=sys.stderr)
            import traceback
            traceback.print_exc()
            success = False
            
    return success

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Transform statute HTML files into semantic format'
    )
    parser.add_argument(
        'input_pattern',
        help='Input file or pattern (e.g., "statute.htm" or "ag.*.htm")'
    )
    parser.add_argument(
        'output_pattern', 
        help='Output file or pattern (must match input pattern if wildcards are used)'
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug output'
    )
    
    args = parser.parse_args()
    
    # Validate that input and output patterns match in terms of wildcards
    if ('*' in args.input_pattern) != ('*' in args.output_pattern):
        print("Error: If input pattern contains wildcards, output pattern must also contain wildcards", 
              file=sys.stderr)
        exit(1)
    
    success = transform_statute_files(args.input_pattern, args.output_pattern, args.debug)
    exit(0 if success else 1)