#!/usr/bin/env python3
import json
import sys
import argparse
import logging
import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pathlib import Path
import re

def parse_arguments():
    """Parse and validate command line arguments."""
    parser = argparse.ArgumentParser(
        description='Fix zero IDs in the statutes collection by updating them to (0) format.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument('--mongo-uri', default=os.getenv('MONGO_URI', 'mongodb://localhost:27017/'), help='MongoDB connection URI')
    parser.add_argument('--database', default=os.getenv('MONGO_DATABASE', 'statutes_db'), help='MongoDB database name')
    parser.add_argument('--collection', default=os.getenv('MONGO_COLLECTION', 'statutes'), help='MongoDB collection name')
    parser.add_argument('--output-json', help='Output to a JSON file instead of MongoDB')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be updated without making changes')

    return parser.parse_args()

def setup_logging():
    """Configures logging for better debugging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def connect_to_mongo(mongo_uri, database, collection):
    """Establishes connection to MongoDB and ensures the database and collection exist."""
    try:
        client = MongoClient(mongo_uri)
        db = client[database]
        logging.info(f"Connected to database: {database}")

        if collection not in db.list_collection_names():
            logging.error(f"Collection '{collection}' does not exist in database '{database}'")
            sys.exit(1)

        return db[collection]
    except Exception as e:
        logging.error(f"Error connecting to MongoDB: {e}")
        sys.exit(1)

def find_zero_id_nodes(collection):
    """Find all nodes with id = '0' and their descendants."""
    # Define all hierarchical node types
    node_types = [
        "subsection",
        "subdivision",
        "subparagraph"
        "subitem",
        "subpart",
    ]

    # Find all nodes with id = '0' and type in our list
    zero_nodes = list(collection.find({
        "id": "0",
        "type": {"$in": node_types}
    }))
    logging.info(f"Found {len(zero_nodes)} nodes with id = '0' across types: {', '.join(node_types)}")

    # For each zero node, find all descendants
    all_nodes_to_update = []
    for node in zero_nodes:
        # Create regex pattern to match node and all descendants
        pattern = f"^{re.escape(node['nodeId'])}"
        descendants = list(collection.find({"nodeId": {"$regex": pattern}}))
        # Add the zero node's section number to each descendant for reference
        for descendant in descendants:
            descendant['zero_node_section'] = node['nodeId'].split('/')[-1]
        all_nodes_to_update.extend(descendants)
        logging.info(f"Found {len(descendants)} descendants for {node['type']} node {node['nodeId']}")

    return all_nodes_to_update

def update_node_ids(nodes):
    """Update the id and nodeId fields for all nodes."""
    updates = []
    for node in nodes:
        old_node_id = node['nodeId']
        old_id = node['id']
        old_parent_id = node['parentId']

        # Only update the id field if it's exactly "0"
        new_id = "(0)" if old_id == "0" else old_id

        # Update the nodeId field
        if old_id == "0":
            # For zero nodes, replace the last occurrence of "0" with "(0)"
            new_node_id = re.sub(r'0$', '(0)', old_node_id)
        else:
            # For descendants, only update if the parent node was a zero node
            # This prevents modifying legitimate section numbers ending in zero
            parts = old_node_id.split('/')
            for i, part in enumerate(parts):
                if re.match(r'\d+\.\d+', part):
                    # Only replace "0" with "(0)" if it's at the end of the section number
                    # and the parent node was a zero node
                    if part.endswith('0') and node.get('zero_node_section'):
                        parts[i] = re.sub(r'(\d+\.\d+)0$', r'\1(0)', part)
                    break
            new_node_id = '/'.join(parts)

        # Update the parentId field for descendants
        if old_id != "0" and node.get('zero_node_section'):
            # For descendants of zero nodes, update their parentId to point to the new parent nodeId
            new_parent_id = re.sub(r'0$', '(0)', old_parent_id)
        else:
            new_parent_id = old_parent_id

        # Only create an update if something actually changed
        if (new_id != old_id or
            new_node_id != old_node_id or
            new_parent_id != old_parent_id):
            update = {
                'old_node_id': old_node_id,
                'new_node_id': new_node_id,
                'old_id': old_id,
                'new_id': new_id,
                'old_parent_id': old_parent_id,
                'new_parent_id': new_parent_id,
                'node_type': node.get('type', 'unknown'),
                'zero_node_section': node.get('zero_node_section')
            }
            updates.append(update)
            logging.debug(f"Created update for {node.get('type', 'unknown')} node:")
            logging.debug(f"  Old nodeId: {old_node_id}")
            logging.debug(f"  New nodeId: {new_node_id}")
            logging.debug(f"  Old id: {old_id}")
            logging.debug(f"  New id: {new_id}")
            logging.debug(f"  Old parentId: {old_parent_id}")
            logging.debug(f"  New parentId: {new_parent_id}")

    return updates

def apply_updates(collection, updates, dry_run=False):
    """Apply the updates to MongoDB."""
    for update in updates:
        if dry_run:
            logging.info(f"Would update node {update['old_node_id']}:")
            logging.info(f"  id: {update['old_id']} -> {update['new_id']}")
            logging.info(f"  nodeId: {update['old_node_id']} -> {update['new_node_id']}")
            logging.info(f"  parentId: {update['old_parent_id']} -> {update['new_parent_id']}")
        else:
            try:
                # First try to find by old nodeId
                doc = collection.find_one({"nodeId": update['old_node_id']})
                if not doc:
                    # If not found, try to find by new nodeId (in case it was already updated)
                    doc = collection.find_one({"nodeId": update['new_node_id']})
                    if doc:
                        # Document was already updated, no need to update again
                        logging.info(f"Node {update['old_node_id']} was already updated")
                        continue
                    else:
                        logging.warning(f"Could not find document with either old or new nodeId: {update['old_node_id']}")
                        continue

                # Check if document needs updating
                needs_update = (
                    doc['id'] != update['new_id'] or
                    doc['nodeId'] != update['new_node_id'] or
                    doc['parentId'] != update['new_parent_id']
                )

                if needs_update:
                    result = collection.update_one(
                        {"_id": doc['_id']},
                        {
                            "$set": {
                                "id": update['new_id'],
                                "nodeId": update['new_node_id'],
                                "parentId": update['new_parent_id']
                            }
                        }
                    )
                    if result.modified_count > 0:
                        logging.info(f"Updated node {update['old_node_id']}")
                    else:
                        logging.warning(f"No update made for node {update['old_node_id']}")
                else:
                    logging.info(f"Node {update['old_node_id']} is already up to date")

            except Exception as e:
                logging.error(f"Error updating node {update['old_node_id']}: {e}")

def save_to_json(updates, output_file):
    """Save the updates to a JSON file."""
    try:
        with open(output_file, 'w') as f:
            json.dump(updates, f, indent=2)
        logging.info(f"Updates saved to {output_file}")
    except Exception as e:
        logging.error(f"Error saving updates to JSON file: {e}")
        sys.exit(1)

def main():
    load_dotenv(Path(__file__).parent.parent / '.env')
    args = parse_arguments()
    setup_logging()

    if args.output_json:
        # If outputting to JSON, we'll need to connect to MongoDB to get the data
        collection = connect_to_mongo(args.mongo_uri, args.database, args.collection)
        nodes = find_zero_id_nodes(collection)
        updates = update_node_ids(nodes)
        save_to_json(updates, args.output_json)
    else:
        collection = connect_to_mongo(args.mongo_uri, args.database, args.collection)
        nodes = find_zero_id_nodes(collection)
        updates = update_node_ids(nodes)
        apply_updates(collection, updates, args.dry_run)

    logging.info("Update process completed!")

if __name__ == "__main__":
    main()
