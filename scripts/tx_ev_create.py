#!/usr/bin/env python3
import re
import json
import argparse
from typing import Dict, List, Optional, Any
import logging
from pypdf import PdfReader

# Map for roman numerals to arabic numbers for articles
ROMAN_TO_ARABIC = {
    'I': '1',
    'II': '2',
    'III': '3',
    'IV': '4',
    'V': '5',
    'VI': '6',
    'VII': '7',
    'VIII': '8',
    'IX': '9',
    'X': '10'
}

def setup_logging():
    """Configure logging for better debugging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Convert Texas Rules of Evidence PDF to hierarchical JSON format.'
    )
    parser.add_argument('input_pdf', help='Path to the input PDF file')
    parser.add_argument('output_file', help='Path to the output JSON file')
    return parser.parse_args()

def extract_text_from_pdf(pdf_path: str) -> List[str]:
    """Extract text from PDF using pypdf."""
    reader = PdfReader(pdf_path)
    lines = []

    # Start from page 4 (index 3)
    for page in reader.pages[3:]:
        text = page.extract_text()
        for line in text.splitlines():
            line = line.strip()
            if not line:
                continue

            # Only skip a line if it's BOTH:
            # 1. Contains only digits (is a page number)
            # 2. AND the next line (if exists) starts a new section
            #    (Rule, Article, or subsection marker)
            next_line = None
            if len(lines) > 0:
                next_line = lines[-1]

            if (line.isdigit() and
                (not next_line or
                 next_line.startswith('Rule ') or
                 next_line.startswith('ARTICLE ') or
                 next_line.startswith('('))):
                continue

            lines.append(line)

    return lines

def normalize_quotes(text: str) -> str:
    """Normalize curly quotes, apostrophes, and dashes to standard ASCII characters."""
    quotes_map = {
        '\u2018': "'",  # Left single quotation mark
        '\u2019': "'",  # Right single quotation mark
        '\u201C': '"',  # Left double quotation mark
        '\u201D': '"',  # Right double quotation mark
        '\u2032': "'",  # Prime (sometimes used as apostrophe)
        '\u0060': "'",  # Backtick
        '\u00B4': "'",  # Acute accent
        '\u2033': '"',  # Double prime
        '\u2013': '-',  # En dash
        '\u2014': '-',  # Em dash
    }

    for special, standard in quotes_map.items():
        text = text.replace(special, standard)

    return text

def clean_text(text: str) -> str:
    """Clean and normalize text from PDF extraction."""
    # First normalize quotes and apostrophes
    text = normalize_quotes(text)

    # Remove problematic Unicode characters
    text = text.replace('\u200B', '')  # Zero-width space
    text = text.replace('\u200C', '')  # Zero-width non-joiner
    text = text.replace('\u200D', '')  # Zero-width joiner
    text = text.replace('\u00A0', ' ') # Non-breaking space
    text = text.replace('\u00AD', '')  # Soft hyphen
    text = text.replace('\uFEFF', '')  # Zero-width non-breaking space

    # Normalize all whitespace to single spaces
    text = ' '.join(text.split())

    # Fix spaces before punctuation
    text = text.replace(' .', '.')
    text = text.replace(' ,', ',')
    text = text.replace(" '", "'")
    text = text.replace("' ", "'")
    text = text.replace(" -", "-")
    text = text.replace("- ", "-")

    # Handle bullet points last, after whitespace normalization
    text = text.replace(' •', '\n•')

    return text.strip()

def parse_rule_number(line: str) -> Optional[str]:
    """Extract rule number from a line."""
    # Only match if it starts with 'Rule ' and is followed by digits and a period
    match = re.match(r'^Rule\s+(\d+)\.', line)
    if match:
        return match.group(1)
    return None

def parse_article_number(line: str) -> Optional[str]:
    """Extract article number from a line."""
    if 'ARTICLE' in line:
        print("\nDEBUG ARTICLE PARSING:")
        print("Raw line:", repr(line))
        match = re.match(r'ARTICLE\s+([IVX]+)[.\s]', line)
        if match:
            roman_num = match.group(1)
            arabic_num = ROMAN_TO_ARABIC.get(roman_num)
            print(f"Found match: roman={roman_num}, arabic={arabic_num}")
            return arabic_num
        else:
            print("No match found in line containing ARTICLE")
    return None

def parse_subsection(line: str, current_element=None) -> Optional[Dict[str, Any]]:
    """Parse a line based on both pattern and hierarchical context."""
    line = line.strip()
    if not line:
        return None

    # First check if we're in a sequence of roman numerals
    if current_element and "subparts" in current_element:
        roman_match = re.match(r'^\((i{1,3}|iv|v|vi{1,3}|ix|x)\)(.*)', line)
        if roman_match:
            return {
                "type": "subpart",
                "id": f"({roman_match.group(1)})",
                "text": clean_text(line)
            }

    # Then check other patterns
    subsection_match = re.match(r'^\(([a-z])\)(.*)', line)
    if subsection_match and not re.match(r'^(i{1,3}|iv|v|vi{1,3}|ix|x)$', subsection_match.group(1)):
        return {
            "type": "subsection",
            "id": f"({subsection_match.group(1)})",
            "text": clean_text(line)
        }

    # Rest of the function remains unchanged
    subdivision_match = re.match(r'^\((\d+)\)(.*)', line)
    if subdivision_match:
        return {
            "type": "subdivision",
            "id": f"({subdivision_match.group(1)})",
            "text": clean_text(line)
        }

    subparagraph_match = re.match(r'^\(([A-Z])\)(.*)', line)
    if subparagraph_match:
        return {
            "type": "subparagraph",
            "id": f"({subparagraph_match.group(1)})",
            "text": clean_text(line)
        }

    # Final check for roman numerals in other contexts
    subpart_match = re.match(r'^\((i{1,3}|iv|v|vi{1,3}|ix|x)\)(.*)', line)
    if subpart_match:
        return {
            "type": "subpart",
            "id": f"({subpart_match.group(1)})",
            "text": clean_text(line)
        }

    return None

def create_hierarchical_structure(lines: List[str]) -> Dict[str, Any]:
    """Convert text lines into hierarchical JSON structure."""
    def process_text_buffer(buffer):
        """Helper to process text buffer and remove page numbers."""
        if not buffer:
            return ""
        # Filter out standalone page numbers from buffer
        filtered_buffer = [line for line in buffer if not (line.strip().isdigit())]
        return clean_text(' '.join(filtered_buffer))

    root = {
        "type": "code",
        "id": "ev",
        "code": "ev",
        "text": "Texas Rules of Evidence",
        "articles": []
    }

    current_article = None
    current_rule = None
    current_subsection = None
    current_subdivision = None
    current_subparagraph = None

    # Buffer for accumulating text
    text_buffer = []
    current_element = None

    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue

        # Check for new article
        article_num = parse_article_number(line)
        if article_num:
            # Special case for Article III
            if article_num == "3":
                current_article = {
                    "type": "article",
                    "id": "3",
                    "text": "ARTICLE III. PRESUMPTIONS",
                    "sections": [
                        {
                            "type": "section",
                            "id": "0",
                            "text": "[No rules adopted at this time.]",
                            "subsections": []
                        }
                    ]
                }
                root["articles"].append(current_article)
                # Skip processing until we find Article IV
                while i < len(lines):
                    if lines[i].strip().startswith('ARTICLE IV'):
                        i -= 1  # Back up one line so we process Article IV in the next iteration
                        break
                    i += 1
                continue

            # Process any buffered text for previous element
            if text_buffer and current_element:
                current_element["text"] = process_text_buffer(text_buffer)
                text_buffer = []

            article_text = line
            if i + 1 < len(lines):
                article_text = f"{line} {lines[i+1].strip()}"
                i += 1

            current_article = {
                "type": "article",
                "id": article_num,
                "text": clean_text(article_text),
                "sections": []
            }
            root["articles"].append(current_article)
            current_rule = None
            current_element = current_article
            i += 1
            continue

        # Check for new rule
        if line.startswith('Rule '):
            rule_num = parse_rule_number(line)
            if rule_num:
                # Process any buffered text for previous element
                if text_buffer and current_element:
                    current_element["text"] = process_text_buffer(text_buffer)
                    text_buffer = []

                # Special handling for known multi-line rules
                if rule_num == "705":
                    rule_title = "Rule 705. Disclosing the Underlying Facts or Data and Examining an Expert About Them"
                    i += 1
                elif rule_num == "803":
                    rule_title = "Rule 803. Exceptions to the Rule Against Hearsay—Regardless of Whether the Declarant Is Available as a Witness"
                    i += 1
                elif rule_num == "804":
                    rule_title = "Rule 804. Exceptions to the Rule Against Hearsay—When the Declarant Is Unavailable as a Witness"
                    i += 1
                elif rule_num == "204":
                    rule_title = "Rule 204. Judicial Notice of Texas Municipal and County Ordinances, Texas Register Contents, and Published Agency Rules"
                    i += 1
                else:
                    rule_title = line

                current_rule = {
                    "type": "section",
                    "id": rule_num,
                    "text": clean_text(rule_title),
                    "subsections": []
                }

                # Move to next line and collect content until we hit a break condition
                i += 1
                content_buffer = []
                while i < len(lines):
                    next_line = lines[i].strip()
                    # Break if we hit:
                    # 1. A proper new rule header (not just a reference)
                    # 2. A new article
                    # 3. Comments
                    # 4. Any subsection/subdivision marker
                    # 5. Empty line
                    if (
                        (next_line.startswith('Rule ') and parse_rule_number(next_line)) or
                        next_line.startswith('ARTICLE ') or
                        next_line == 'Notes and Comments' or
                        next_line.startswith('Comment to') or
                        next_line.startswith('(') or
                        not next_line
                    ):
                        break
                    content_buffer.append(next_line)
                    i += 1

                # If we collected any content, create a main subsection
                if content_buffer:
                    main_subsection = {
                        "type": "subsection",
                        "id": "(0)",
                        "text": process_text_buffer(content_buffer)
                    }
                    current_rule["subsections"].append(main_subsection)
                    current_subsection = main_subsection

                if current_article:
                    current_article["sections"].append(current_rule)
                current_element = current_rule
                continue

        # Check for comments - either with header or direct comment
        if line == 'Notes and Comments' or line.startswith('Comment to'):
            amendments = []
            current_comment = []

            # If this is a direct comment (no header), start with this line
            if line.startswith('Comment to'):
                current_comment.append(line)

            i += 1
            # Continue collecting comment lines until we hit a new section or end
            while i < len(lines):
                next_line = lines[i].strip()
                # Only break if we hit a new actual rule (Rule NNN.) or article
                if ((next_line.startswith('Rule ') and re.match(r'Rule\s+\d+\.', next_line)) or
                    next_line.startswith('ARTICLE ')):
                    break

                # If we hit a new comment, save the current one and start a new one
                if next_line.startswith('Comment to'):
                    if current_comment:
                        amendments.append(clean_text(' '.join(current_comment)))
                    current_comment = [next_line]
                else:
                    if next_line:  # Only add non-empty lines
                        current_comment.append(next_line)
                i += 1

            # Add the last comment if there is one
            if current_comment:
                amendments.append(clean_text(' '.join(current_comment)))

            if current_rule:
                current_rule["amendment_history"] = amendments
            continue

        # Parse subsections
        subsection_info = parse_subsection(line, current_element)
        if subsection_info:
            # Process any buffered text for previous element
            if text_buffer and current_element:
                current_element["text"] = process_text_buffer(text_buffer)
                text_buffer = []

            # Start new text buffer with the current line
            text_buffer = [line]

            # Check if this is part of a roman numeral sequence
            is_roman = bool(re.match(r'^\((i{1,3}|iv|v|vi{1,3}|ix|x)\)', line))
            if is_roman and current_subdivision and current_subdivision.get("subparts"):
                # Add to existing subparts
                current_subpart = {
                    "type": "subpart",
                    "id": subsection_info["id"],
                    "text": "",  # Will be filled with buffer later
                }
                current_subdivision["subparts"].append(current_subpart)
                current_element = current_subpart
            else:
                # Regular subsection processing continues as before
                if subsection_info["type"] == "subsection":
                    current_subsection = {
                        "type": "subsection",
                        "id": subsection_info["id"],
                        "text": "",  # Will be filled with buffer later
                    }
                    if current_rule:
                        current_rule["subsections"].append(current_subsection)
                    current_subdivision = None
                    current_element = current_subsection
                elif subsection_info["type"] == "subdivision":
                    current_subdivision = {
                        "type": "subdivision",
                        "id": subsection_info["id"],
                        "text": "",  # Will be filled with buffer later
                    }
                    if current_subsection:
                        if "subdivisions" not in current_subsection:
                            current_subsection["subdivisions"] = []
                        current_subsection["subdivisions"].append(current_subdivision)
                    current_element = current_subdivision
                elif subsection_info["type"] == "subparagraph":
                    current_subparagraph = {
                        "type": "subparagraph",
                        "id": subsection_info["id"],
                        "text": "",  # Will be filled with buffer later
                    }
                    if current_subdivision:
                        if "subparagraphs" not in current_subdivision:
                            current_subdivision["subparagraphs"] = []
                        current_subdivision["subparagraphs"].append(current_subparagraph)
                    current_element = current_subparagraph
                elif subsection_info["type"] == "subpart":
                    current_subpart = {
                        "type": "subpart",
                        "id": subsection_info["id"],
                        "text": "",  # Will be filled with buffer later
                    }
                    if current_subparagraph:
                        if "subparts" not in current_subparagraph:
                            current_subparagraph["subparts"] = []
                        current_subparagraph["subparts"].append(current_subpart)
                    current_element = current_subpart

            # Special handling for (i) and (ii) that are part of the same text
            if subsection_info["type"] == "subpart":
                # Check if this line contains both (i) and (ii)
                full_line = line
                if "(i)" in full_line and "(ii)" in full_line:
                    parts = re.split(r'(?=\(ii\))', full_line)
                    if len(parts) == 2:
                        # Create first subpart for (i)
                        first_subpart = {
                            "type": "subpart",
                            "id": "(i)",
                            "text": clean_text(parts[0])
                        }
                        if current_subparagraph:
                            if "subparts" not in current_subparagraph:
                                current_subparagraph["subparts"] = []
                            current_subparagraph["subparts"].append(first_subpart)

                        # Create second subpart for (ii)
                        second_subpart = {
                            "type": "subpart",
                            "id": "(ii)",
                            "text": clean_text(parts[1])
                        }
                        current_subparagraph["subparts"].append(second_subpart)
                        current_element = second_subpart

            i += 1
            continue

        # If we get here, the line is continuation text for the current element
        if current_element:
            text_buffer.append(line)

        i += 1

    # Process any remaining buffered text
    if text_buffer and current_element:
        current_element["text"] = process_text_buffer(text_buffer)

    return root

def main():
    """Main function to process the PDF file and create JSON output."""
    args = parse_arguments()
    setup_logging()

    try:
        # Extract text from PDF
        logging.info(f"Reading PDF file: {args.input_pdf}")
        lines = extract_text_from_pdf(args.input_pdf)

        # Create hierarchical structure
        logging.info("Creating hierarchical structure...")
        hierarchy = create_hierarchical_structure(lines)

        # Write output JSON
        logging.info(f"Writing output to {args.output_file}...")
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(hierarchy, f, indent=2, ensure_ascii=False)

        logging.info("Conversion completed successfully!")

    except Exception as e:
        logging.error(f"Error processing file: {e}")
        raise

if __name__ == "__main__":
    main()
