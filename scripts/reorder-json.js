import { readFileSync, writeFileSync } from 'fs';

// Get filename from command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('Please provide a filename as an argument');
  console.log('Usage: node reorder-json.js <filename>');
  process.exit(1);
}

const filePath = args[0];

// Read the JSON file
let data;
try {
  data = JSON.parse(readFileSync(filePath, 'utf8'));
  if (!Array.isArray(data)) {
    throw new Error('JSON root is not an array');
  }
} catch (err) {
  console.error('Failed to read or parse JSON:', err.message);
  process.exit(1);
}

// Update the order field
const updated = data.map((record, idx) => ({
  ...record,
  order: idx + 1
}));

// Write back to the same file (or change filePath to write to a new file)
try {
  writeFileSync(filePath, JSON.stringify(updated, null, 2), 'utf8');
  console.log(`Updated ${updated.length} records in ${filePath}`);
} catch (err) {
  console.error('Failed to write JSON:', err.message);
  process.exit(1);
}
