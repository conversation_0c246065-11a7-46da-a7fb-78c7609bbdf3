export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { BookmarkStore } from '@/components/features/bookmarks/stores/mongo.BookmarkStore';

export async function PUT(request) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { bookmarks } = await request.json();

    await BookmarkStore.updateBookmarksOrder(bookmarks, session.user.id);    

    const updatedBookmarks = await BookmarkStore.getBookmarksForUser(session.user.id);
    return NextResponse.json(updatedBookmarks);
  } catch (error) {
    console.error('Reorder error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 