export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { BookmarkStore } from '@/components/features/bookmarks/stores/mongo.BookmarkStore';

// Get all bookmarks for the current user
export async function GET() {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const bookmarks = await BookmarkStore.getBookmarksForUser(session.user.id);
    return NextResponse.json(bookmarks);

  } 
  catch (error) {
    console.error('Load bookmarks error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Create a new bookmark
export async function POST(request) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const bookmarkJson = await request.json();
    const bookmark = await BookmarkStore.addBookmark(bookmarkJson, session.user.id);

    return NextResponse.json(bookmark);
  } 
  catch (error) {
    console.error('Create bookmark error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 