import { GET } from './route';
import { buildNavTree } from './utils';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

// Mock MongoDB client
jest.mock('@/lib/mongodb', () => ({
  clientPromise: Promise.resolve({})
}));

// Mock StatuteStore
jest.mock('@/components/features/statutes/stores/mongo.StatuteStore');

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: (body, options = {}) => ({
      headers: new Map(),
      json: async () => body,
      status: options.status || 200,
    }),
  },
}));

// Mock process.env
const originalEnv = process.env;

describe('Statutes API Route', () => {
  const mockCollections = [
    { id: 'tx', text: 'Texas' }
  ];

  const mockCodes = [
    { id: 'ag', collection: 'tx', text: 'Agriculture Code' },
    { id: 'bc', collection: 'tx', text: 'Business Code' }
  ];

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset process.env
    process.env = { ...originalEnv };
    process.env.NODE_ENV = 'production'; // Set to production for tests

    // Setup default mock implementation
    StatuteStore.getNavTree.mockResolvedValue([
      {
        id: 'root',
        nodeId: '/',
        type: 'root',
        text: 'Statute Collections',
        parentId: null,
        children: []
      },
      {
        id: 'tx',
        nodeId: '/collection/tx',
        type: 'collection',
        text: 'Texas',
        parentId: '/',
        children: []
      },
      {
        id: 'ag',
        nodeId: '/collection/tx/code/ag',
        type: 'code',
        text: 'Agriculture Code',
        parentId: '/collection/tx',
        children: []
      }
    ]);
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('GET handler', () => {
    it('should return a properly structured navigation tree', async () => {
      const response = await GET();
      expect(response.status).toBe(200);

      const data = await response.json();

      // Update expectations to expect array directly
      expect(Array.isArray(data)).toBe(true);

      // Verify array contents
      expect(data.length).toBeGreaterThan(0);

      // Check structure of nodes
      data.forEach(node => {
        expect(node).toHaveProperty('id');
        expect(node).toHaveProperty('nodeId');
        expect(node).toHaveProperty('type');
        expect(node).toHaveProperty('text');
        expect(node).toHaveProperty('children');
        expect(Array.isArray(node.children)).toBe(true);
      });

      // Optional: Verify specific nodes if needed
      const rootNode = data.find(node => node.type === 'root');
      expect(rootNode).toBeDefined();
      expect(rootNode.nodeId).toBe('/');
    });

    it('should set proper cache headers', async () => {
      const response = await GET();

      expect(response.headers.get('Cache-Control')).toBe(
        'public, max-age=3600, stale-while-revalidate=86400'
      );
      expect(response.headers.get('Vary')).toBe('Accept-Encoding');
    });

    it('should handle database errors gracefully', async () => {
      // Spy on console.error and suppress output
      jest.spyOn(console, 'error').mockImplementation(() => {});

      // Mock the error
      StatuteStore.getNavTree.mockRejectedValue(new Error('Database error'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data).toEqual({
        error: 'Database error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('buildNavTree', () => {
    it('should create correct node structure', () => {
      const nodes = buildNavTree(mockCollections, mockCodes);

      expect(nodes).toEqual([
        {
          id: 'root',
          nodeId: '/',
          type: 'root',
          text: 'Statute Collections',
          parentId: null
        },
        {
          id: 'tx',
          nodeId: '/collection/tx',
          type: 'collection',
          text: 'Texas',
          parentId: '/'
        },
        {
          id: 'ag',
          nodeId: '/collection/tx/code/ag',
          type: 'code',
          text: 'Agriculture Code',
          code: 'ag',
          collection: 'tx',
          parentId: '/collection/tx'
        },
        {
          id: 'bc',
          nodeId: '/collection/tx/code/bc',
          type: 'code',
          text: 'Business Code',
          code: 'bc',
          collection: 'tx',
          parentId: '/collection/tx'
        }
      ]);
    });

    it('should handle empty collections and codes', () => {
      const nodes = buildNavTree([], []);

      expect(nodes).toEqual([
        {
          id: 'root',
          nodeId: '/',
          type: 'root',
          text: 'Statute Collections',
          parentId: null
        }
      ]);
    });

    it('should handle collections with no codes', () => {
      const nodes = buildNavTree([mockCollections[0]], []);

      expect(nodes).toEqual([
        {
          id: 'root',
          nodeId: '/',
          type: 'root',
          text: 'Statute Collections',
          parentId: null
        },
        {
          id: 'tx',
          nodeId: '/collection/tx',
          type: 'collection',
          text: 'Texas',
          parentId: '/'
        }
      ]);
    });
  });
});
