import { GET } from './route';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

// Mock StatuteStore
jest.mock('@/components/features/statutes/stores/mongo.StatuteStore');

// Mock NextResponse
jest.mock('next/server', () => {
  const mockHeaders = new Map();

  return {
    NextResponse: {
      json: jest.fn((data, init = {}) => ({
        status: init.status || 200,
        ok: init.status ? init.status >= 200 && init.status < 300 : true,
        headers: {
          get: (key) => init.headers?.[key],
          set: (key, value) => mockHeaders.set(key, value)
        },
        json: () => Promise.resolve(data)
      }))
    }
  };
});

describe('GET', () => {
  // Helper functions
  const createRequest = (path) => ({
    url: `http://localhost/api/v1/statutes/${path}`
  });

  const createContext = (path) => ({
    params: { path: path.split('/') }
  });

  // Setup and teardown
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Input validation', () => {
    it('should return 400 if path is missing', async () => {
      const response = await GET(createRequest(''), { params: {} });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data).toEqual({ error: 'Invalid path' });
    });
  });

  describe('Node retrieval', () => {
    it('should return node data with 200 status when found', async () => {
      const mockNode = {
        nodeId: '/collection/tx',
        type: 'collection',
        text: 'Texas Statutes'
      };
      StatuteStore.getNode.mockResolvedValue(mockNode);

      const response = await GET(
        createRequest('collection/tx'),
        createContext('collection/tx')
      );

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data).toEqual(mockNode);
      expect(StatuteStore.getNode).toHaveBeenCalledWith('/collection/tx');
    });

    it('should return 404 when node is not found', async () => {
      StatuteStore.getNode.mockResolvedValue(null);

      const response = await GET(
        createRequest('invalid/path'),
        createContext('invalid/path')
      );

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data).toEqual({ error: 'Node not found' });
    });

    it('should return 500 on database error', async () => {
      StatuteStore.getNode.mockRejectedValue(new Error('Database error'));

      const response = await GET(
        createRequest('some/path'),
        createContext('some/path')
      );

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data).toEqual({ error: 'Database error' });
    });
  });

  describe('Cache headers', () => {
    const originalNodeEnv = process.env.NODE_ENV;

    afterEach(() => {
      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should set production cache headers', async () => {
      process.env.NODE_ENV = 'production';
      StatuteStore.getNode.mockResolvedValue({ nodeId: 'test' });

      const response = await GET(
        createRequest('test'),
        createContext('test')
      );

      expect(response.headers.get('Cache-Control')).toBe(
        'public, max-age=7200, stale-while-revalidate=86400'
      );
      expect(response.headers.get('Vary')).toBe('Accept-Encoding');
    });

    it('should set development cache headers', async () => {
      process.env.NODE_ENV = 'development';
      StatuteStore.getNode.mockResolvedValue({ nodeId: 'test' });

      const response = await GET(
        createRequest('test'),
        createContext('test')
      );

      expect(response.headers.get('Cache-Control')).toBe(
        'no-store, no-cache, must-revalidate, proxy-revalidate'
      );
    });
  });
});
