export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

function getHeaders() {
  const headers = {
    'Vary': 'Accept-Encoding'
  };

  if (process.env.NODE_ENV === 'production') {
    headers['Cache-Control'] = 'public, max-age=7200, stale-while-revalidate=86400';
  } else {
    headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, proxy-revalidate';
  }

  return headers;
}

export async function GET(request, context) {
  try {
    // Await params before accessing any properties
    const params = await context.params;

    // Ensure we have a path
    if (!params?.path) {
      return NextResponse.json(
        { error: 'Invalid path' },
        { status: 400, headers: getHeaders() }
      );
    }

    // Get the specific node
    const path = '/' + params.path.join('/');
    const node = await StatuteStore.getNode(path);

    if (!node) {
      return NextResponse.json(
        { error: 'Node not found' },
        { status: 404, headers: getHeaders() }
      );
    }

    // Return the structured node data directly
    return NextResponse.json(node, { headers: getHeaders() });
  } catch (error) {
    return NextResponse.json(
      { error: error.message },
      { status: 500, headers: getHeaders() }
    );
  }
}
