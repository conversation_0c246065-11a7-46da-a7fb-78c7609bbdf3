export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

export async function GET(request) {
  try {
    // Get nodeId from query parameters for filtering
    const { searchParams } = new URL(request.url);
    const nodeId = searchParams.get('nodeId');

    // Early return for root node - no navigation needed
    if (nodeId === '/' || nodeId === '') {
      return NextResponse.json([]);
    }

    // Get filtered nodes from StatuteStore
    const nodes = await StatuteStore.getFlatNavTree(nodeId);

    const response = NextResponse.json(nodes);

    // Cache based on whether we're filtering or not
    if (process.env.NODE_ENV === 'production') {
      if (nodeId) {
        // Shorter cache for filtered results since they're context-specific
        response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=600'); // 5 minutes cache
      } else {
        // Longer cache for unfiltered results
        response.headers.set('Cache-Control', 'public, max-age=7200, stale-while-revalidate=86400'); // 2 hours cache
      }
      response.headers.set('Vary', 'Accept-Encoding');
    } else {
      // In development, set no-cache headers
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');
    }

    return response;
  } catch (error) {
    console.error('Error in flat statute data route:', error.stack);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}