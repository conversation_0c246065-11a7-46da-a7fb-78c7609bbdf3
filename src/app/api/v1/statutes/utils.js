// Utility functions for statutes API

export const buildNavTree = (collections, codes) => {
  const root = {
    id: 'root',
    nodeId: '/',
    type: 'root',
    text: 'Statute Collections',
    parentId: null
  };

  const navNodes = [root];

  collections.forEach(collection => {
    navNodes.push({
      id: collection.id,
      nodeId: `/collection/${collection.id}`,
      type: 'collection',
      text: collection.text,
      parentId: '/'
    });

    const collectionCodes = codes.filter(code =>
      code.collection === collection.id
    );

    collectionCodes.forEach(code => {
      navNodes.push({
        id: code.id,
        nodeId: `/collection/${collection.id}/code/${code.id}`,
        type: 'code',
        text: code.text,
        code: code.id,
        collection: collection.id,
        parentId: `/collection/${collection.id}`
      });
    });
  });

  return navNodes;
};