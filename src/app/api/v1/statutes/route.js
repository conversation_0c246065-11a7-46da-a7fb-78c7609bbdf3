export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

export async function GET() {
  try {
    // Get nodes from StatuteStore
    const nodes = await StatuteStore.getNavTree();

    const response = NextResponse.json(nodes);  // Return array directly

    // Only set cache headers in production
    if (process.env.NODE_ENV === 'production') {
      response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');
      response.headers.set('Vary', 'Accept-Encoding');
    } else {
      // In development, set no-cache headers
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');
    }

    return response;
  } catch (error) {
    console.error('Error in statute data route:', error.stack);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

