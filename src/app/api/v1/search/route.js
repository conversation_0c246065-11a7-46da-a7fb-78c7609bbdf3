import { NextResponse } from 'next/server';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

export async function GET(request) {
  // Store searchParams at the top level so it's available in catch block
  let searchParams;

  try {
    searchParams = new URL(request.url).searchParams;
    const query = searchParams.get('q');
    let requestedCodes = searchParams.get('codes')?.split(',').filter(Boolean) || [];
    const searchField = searchParams.get('searchField') || 'text';

    // Validate input
    if (!query || query.trim().length < 3) {
      return NextResponse.json(
        { error: 'Search query must be at least 3 characters long' },
        { status: 400 }
      );
    }

    // Get all enabled codes if none provided
    let searchCodes = requestedCodes;
    if (!requestedCodes.length) {
      const enabledCodes = await StatuteStore.getCodes();
      searchCodes = enabledCodes.map(code => code.code);
    } else {
      // If specific codes requested, filter to only enabled ones
      const enabledCodes = await StatuteStore.getCodes();
      const enabledCodeSet = new Set(enabledCodes.map(code => code.code));
      searchCodes = requestedCodes.filter(code => enabledCodeSet.has(code));
    }

    if (!searchCodes.length) {
      return NextResponse.json(
        { error: 'No valid codes to search against' },
        { status: 400 }
      );
    }

    // Perform search
    const results = await StatuteStore.searchNodes(query.trim(), searchCodes, searchField);

    const response = NextResponse.json(results);

    // Cache search results for a short time in production
    if (process.env.NODE_ENV === 'production') {
      response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=3600');
      response.headers.set('Vary', 'Accept-Encoding');
    } else {
      // In development, set no-cache headers
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');
    }

    return response;
  } catch (error) {
    console.error('Error in search route:', {
      error,
      message: error.message,
      stack: error.stack,
      query: searchParams?.get('q'),
      codes: searchParams?.get('codes')
    });

    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
