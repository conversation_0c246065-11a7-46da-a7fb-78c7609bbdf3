export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { ReferenceStore } from '@/components/features/references/stores/mongo.ReferenceStore';
import { ObjectId } from 'mongodb';
import { isSystemAdmin } from '@/lib/models/roles';

// Get a single reference by ID
export async function GET(request, { params }) {
  try {
    const { id } = params;

    // Get the reference from MongoDB
    const referencesCollection = await ReferenceStore.referencesCollection();
    const reference = await referencesCollection.findOne({
      _id: new ObjectId(id)
    });

    if (!reference) {
      return NextResponse.json({ error: 'Reference not found' }, { status: 404 });
    }

    return NextResponse.json(reference);
  }
  catch (error) {
    console.error('Get reference error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Update a reference
export async function PUT(request, context) {
  try {
    const params = await context.params;
    const { id } = params;

    // Authentication is required to update references
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!isSystemAdmin(session)) {
      return NextResponse.json({ error: 'Forbidden: Admins only' }, { status: 403 });
    }

    const updatedReferenceJson = await request.json();

    // Ensure the ID in the URL matches the ID in the body
    updatedReferenceJson._id = id;

    const reference = await ReferenceStore.updateReference(updatedReferenceJson);

    if (!reference) {
      return NextResponse.json({ error: 'Reference not found' }, { status: 404 });
    }

    return NextResponse.json(reference);
  }
  catch (error) {
    console.error('Update reference error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Delete a reference
export async function DELETE(request, context) {
  const params = await context.params;
  const { id } = params;

  try {
    // Authentication is required to delete references
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!isSystemAdmin(session)) {
      return NextResponse.json({ error: 'Forbidden: Admins only' }, { status: 403 });
    }

    const ref = await ReferenceStore.removeReference(id);

    if (!ref) {
      return NextResponse.json({ error: 'Reference not found' }, { status: 404 });
    }

    return NextResponse.json({ nodeId: ref.nodeId, id: ref._id?.toString?.() || ref._id });
  }
  catch (error) {
    console.error('Delete reference error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
