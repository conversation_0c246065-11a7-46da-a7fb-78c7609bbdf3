import { PUT, DELETE } from './route';
import { auth } from '@/auth';
import { ReferenceStore } from '@/components/features/references/stores/mongo.ReferenceStore';

jest.mock('@/auth', () => ({
  auth: jest.fn()
}));
jest.mock('@/components/features/references/stores/mongo.ReferenceStore', () => ({
  ReferenceStore: {
    updateReference: jest.fn(),
    removeReference: jest.fn()
  }
}));

function mockRequest(json) {
  return {
    json: async () => json,
    url: 'http://localhost/api/v1/references/abc123',
  };
}

const context = { params: { id: 'abc123' } };

describe('PUT /api/v1/references/[id]', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns 401 if not authenticated', async () => {
    auth.mockResolvedValue(null);
    const res = await PUT(mockRequest({ foo: 'bar' }), context);
    expect(res.status).toBe(401);
  });

  it('returns 403 if not systemAdmin', async () => {
    auth.mockResolvedValue({ user: { roles: ['user'] } });
    const res = await PUT(mockRequest({ foo: 'bar' }), context);
    expect(res.status).toBe(403);
  });

  it('returns 200 and reference if systemAdmin', async () => {
    auth.mockResolvedValue({ user: { roles: ['systemAdmin'] } });
    ReferenceStore.updateReference.mockResolvedValue({ id: 'abc123', foo: 'bar' });
    const res = await PUT(mockRequest({ foo: 'bar' }), context);
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data).toEqual({ id: 'abc123', foo: 'bar' });
  });
});

describe('DELETE /api/v1/references/[id]', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns 401 if not authenticated', async () => {
    auth.mockResolvedValue(null);
    const res = await DELETE(mockRequest(), context);
    expect(res.status).toBe(401);
  });

  it('returns 403 if not systemAdmin', async () => {
    auth.mockResolvedValue({ user: { roles: ['user'] } });
    const res = await DELETE(mockRequest(), context);
    expect(res.status).toBe(403);
  });

  it('returns 200 and reference if systemAdmin', async () => {
    auth.mockResolvedValue({ user: { roles: ['systemAdmin'] } });
    ReferenceStore.removeReference.mockResolvedValue({ _id: 'abc123', nodeId: 'n1' });
    const res = await DELETE(mockRequest(), context);
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data).toEqual({ nodeId: 'n1', id: 'abc123' });
  });
});
