export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { ReferenceStore } from '@/components/features/references/stores/mongo.ReferenceStore';
import { isSystemAdmin } from '@/lib/models/roles';

// Get references for a specific node or all references
export async function GET(request) {
  try {
    const url = new URL(request.url);
    const nodeId = url.searchParams.get('node');

    let references;
    if (nodeId) {
      // Get references contained in this node (where this node is the source)
      references = await ReferenceStore.getReferences(nodeId);
    } else {
      references = await ReferenceStore.getAllReferences();
    }

    return NextResponse.json(references);
  }
  catch (error) {
    console.error('Error fetching references:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

// Create a new reference
export async function POST(request) {
  try {
    // Authentication is required to add references
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!isSystemAdmin(session)) {
      return NextResponse.json({ error: 'Forbidden: Admins only' }, { status: 403 });
    }
    const referenceJson = await request.json();
    const reference = await ReferenceStore.addReference(referenceJson);

    return NextResponse.json(reference);
  }
  catch (error) {
    console.error('Create reference error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
