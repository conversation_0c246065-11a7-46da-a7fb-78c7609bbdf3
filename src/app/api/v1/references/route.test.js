import { POST } from './route';
import { auth } from '@/auth';
import { ReferenceStore } from '@/components/features/references/stores/mongo.ReferenceStore';

jest.mock('@/auth', () => ({
  auth: jest.fn()
}));
jest.mock('@/components/features/references/stores/mongo.ReferenceStore', () => ({
  ReferenceStore: {
    addReference: jest.fn()
  }
}));

function mockRequest(json) {
  return {
    json: async () => json,
    url: 'http://localhost/api/v1/references',
  };
}

describe('POST /api/v1/references', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns 401 if not authenticated', async () => {
    auth.mockResolvedValue(null);
    const res = await POST(mockRequest({ foo: 'bar' }));
    expect(res.status).toBe(401);
  });

  it('returns 403 if not systemAdmin', async () => {
    auth.mockResolvedValue({ user: { roles: ['user'] } });
    const res = await POST(mockRequest({ foo: 'bar' }));
    expect(res.status).toBe(403);
  });

  it('returns 200 and reference if systemAdmin', async () => {
    auth.mockResolvedValue({ user: { roles: ['systemAdmin'] } });
    ReferenceStore.addReference.mockResolvedValue({ id: 'abc123', foo: 'bar' });
    const res = await POST(mockRequest({ foo: 'bar' }));
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data).toEqual({ id: 'abc123', foo: 'bar' });
  });
});
