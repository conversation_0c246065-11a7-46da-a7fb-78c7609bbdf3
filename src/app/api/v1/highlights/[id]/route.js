export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { HighlightStore } from '@/components/features/highlights/stores/mongo.HighlightStore';

// Update a highlight
export async function PUT(request, { params }) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const highlightId = params.id;
    if (!highlightId) {
      return NextResponse.json(
        { error: 'Highlight ID is required' },
        { status: 400 }
      );
    }

    try {
      const updateData = await request.json();

      // Let the store handle validation and update
      const result = await HighlightStore.updateHighlight(
        { ...updateData, _id: highlightId },
        session.user.id
      );

      if (!result) {
        return NextResponse.json(
          { error: 'Highlight not found or not authorized' },
          { status: 404 }
        );
      }

      return NextResponse.json(result);
    } catch (error) {
      console.error('Update highlight error:', error);
      const status = error.name === 'ValidationError' ? 400 : 500;
      return NextResponse.json(
        { error: error.message || 'Failed to update highlight' },
        { status }
      );
    }
  }
  catch (error) {
    console.error('Update highlight error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update highlight' },
      { status: 500 }
    );
  }
}

// Delete a highlight
export async function DELETE(request, { params }) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const highlightId = params.id;
    if (!highlightId) {
      return NextResponse.json(
        { error: 'Highlight ID is required' },
        { status: 400 }
      );
    }

    // Delete from database
    const result = await HighlightStore.removeHighlight(highlightId, session.user.id);

    if (!result) {
      return NextResponse.json(
        { error: 'Highlight not found or not authorized' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  }
  catch (error) {
    console.error('Delete highlight error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete highlight' },
      { status: 500 }
    );
  }
}
