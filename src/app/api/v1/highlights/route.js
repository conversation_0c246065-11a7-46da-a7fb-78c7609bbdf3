export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { HighlightStore } from '@/components/features/highlights/stores/mongo.HighlightStore';

// Get highlights for a specific node or all highlights for the current user
export async function GET(request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const nodeId = searchParams.get('node');

    let highlights;
    if (nodeId) {
      highlights = await HighlightStore.getHighlightsForNode(nodeId, session.user.id);
    } else {
      highlights = await HighlightStore.getHighlightsForUser(session.user.id);
    }

    return NextResponse.json(highlights);
  }
  catch (error) {
    console.error('Error fetching highlights:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch highlights' },
      { status: 500 }
    );
  }
}

// Create a new highlight
export async function POST(request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const highlightData = await request.json();

    try {
      // Create and save the highlight (validation happens in the store)
      const savedHighlight = await HighlightStore.addHighlight(
        highlightData,
        session.user.id
      );

      return NextResponse.json(savedHighlight);
    } catch (error) {
      console.error('Create highlight error:', error);
      const status = error.name === 'ValidationError' ? 400 : 500;
      return NextResponse.json(
        { error: error.message || 'Failed to create highlight' },
        { status }
      );
    }
  } catch (error) {
    console.error('Create highlight error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create highlight' },
      { status: 500 }
    );
  }
}
