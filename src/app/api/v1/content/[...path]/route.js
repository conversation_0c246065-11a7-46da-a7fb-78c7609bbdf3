export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

function getHeaders() {
  const headers = {
    'Vary': 'Accept-Encoding'
  };

  if (process.env.NODE_ENV === 'production') {
    headers['Cache-Control'] = 'public, max-age=7200, stale-while-revalidate=86400';
  } else {
    headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, proxy-revalidate';
  }

  return headers;
}

export async function GET(request, context) {
  try {
    // Await params before accessing any properties
    const params = await context.params;

    // Ensure we have a path
    if (!params?.path) {
      return NextResponse.json(
        { error: 'Invalid path' },
        { status: 400, headers: getHeaders() }
      );
    }

    // Get the content nodes
    const path = '/' + params.path.join('/');

    const result = await StatuteStore.getContentNodes(path);

    if (!result?.selectedNode) {
      return NextResponse.json(
        { error: 'Content not found' },
        { status: 404, headers: getHeaders() }
      );
    }

    // Return the content nodes
    return NextResponse.json(result, { headers: getHeaders() });
  } catch (error) {
    console.error('Content API - Error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500, headers: getHeaders() }
    );
  }
}
