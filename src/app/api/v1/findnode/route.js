export const runtime = "nodejs";

import { NextResponse } from 'next/server';
import { StatuteStore } from '@/components/features/statutes/stores/mongo.StatuteStore';

function getHeaders() {
  const headers = {
    'Vary': 'Accept-Encoding'
  };

  if (process.env.NODE_ENV === 'production') {
    headers['Cache-Control'] = 'public, max-age=7200, stale-while-revalidate=86400';
  } else {
    headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, proxy-revalidate';
  }

  return headers;
}

export async function GET(request) {
  try {
    const searchParams = new URL(request.url).searchParams;
    const startPath = searchParams.get('startPath');
    const endPath = searchParams.get('endPath');

    // Validate input
    if (!startPath || !endPath) {
      return NextResponse.json(
        { error: 'Both startPath and endPath parameters are required' },
        { status: 400, headers: getHeaders() }
      );
    }

    // Find the node
    const node = await StatuteStore.findNodeByStartEndPath(startPath, endPath);

    if (!node) {
      return NextResponse.json(
        { error: 'Node not found' },
        { status: 404, headers: getHeaders() }
      );
    }

    // Return the node data
    return NextResponse.json(node, { headers: getHeaders() });
  } catch (error) {
    console.error('Error in findnode route:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500, headers: getHeaders() }
    );
  }
}
