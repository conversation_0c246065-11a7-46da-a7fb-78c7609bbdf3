'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { StatuteContent } from '@/components/features/statutes/components/StatuteContent';
import { useTreeState } from '@/components/features/navtree';

export default function StatutePage() {
  const pathname = usePathname();
  const { selectNode } = useTreeState();

  // Handle initial load and URL changes
  useEffect(() => {
    if (!pathname) return;
    selectNode(pathname);
  }, [pathname, selectNode]);

  return (
    <StatuteContent />
  );
}
