Statute Browser Application Requirements

Contents is organized into Collections of Codes. Think of a Collection like a state such as "Texas Statutes" or "Federal Law".
Codes are the individual codes within the collection. Ex: Texas Penal Code, Texas Consitution, etc.

The statutory documents exist in an outline form with a hierarchy. Code > Title > Subtitle >  Chapter > Subchapter > Section. Not all Titles have Subtitles, they are optional. Not all Chapters have Subchapters, they are optional. There is only ever one Code, but there can be one or more Titles, Subtitles, Chapters, Subchapters, and Sections. Sections can belong to either a Chapter or Subchapter. In some codes Sections are called Articles, but they serve the same purpose and should be treated the same.

This leaves us with four possible outline structures:
1. Code > Title > Subtitle > Chapter > Subchapter > Section
2. Code > Title > Subtitle > Chapter > Section
3. Code > Title > Chapter > Subchapter > Section
4. Code > Title > Chapter > Section

TBD: Some codes have different outline structures, so we will need to come up with a way to support that.
EX: Texas Constitution hierarchy: [Code > Article > Subarticle (optional) > Section]
EX: Criminal Procedure hierarchy: [Code > Title > Subtitle (optional) > Chapter > Subchapter (optional) > Article > Section]

Each Section consists of 3 main parts:
1. Section title. This takes the form of a pattern "Section X.XXX. SECTION TITLE."
2. Section text. This consists of one or more paragraphs. A subsection is an indented paragraph in the section text. A section is an unindented paragraph. Not all sections will have subsections. Subsections can have n-level of subsections, each indented further.
3. Amendment history. This starts with one or more of the three options: "Acts ", "Added by", or "Amended by". Each section will have one amendment history.

We will have more than one Code under the root collection of Texas statutes. Eventually we will have more than one state and will also have federal statutes.

## Bookmarks Feature
We will have a bookmark feature that allows us to bookmark any part of the statute for easy access: Code, Title, Subtitle, Chapter, Subchapter, or Section. A bookmark will have a "name" which is the name of the bookmarked part of the statute. A bookmark will have a "path" which is the outline hierarchy leading to the bookmarked part of the statute. We will display a list of bookmarks and allow removing a bookmark from the list.

## Search Feature
We will have a search feature that allows us to search through the text of each outline level and the text of each section/subsection. Search requires at least 3 characters before performing the search. A search result will have a "name" which is the name of the  part of the statute that matches the search term. A search result will have a "path" which is the outline hierarchy leading to the search result part of the statute. We will highlight the matching search term in the search results. We will allow for full matches, partial matches, and fuzzy matches and results will be returned sorted in that order.

## Table of Contents
In Desktop, we will display the statute outline in a collapsible tree view panel on the left side of the screen that allows for navigation and loading of the statute content in the right panel.
The panels are resizable by clicking and dragging the separator between the left and right panel.
Each node in the navigation tree can be clicked on to load content of that selected part of the statute.
Users can quickly bookmark a node from the navtree.

## Highlights
Users can create their own highlights of text portions in differnt colors
See highlightRequirements.txt for more information.

## References
When statutes have cross-references to other statutes we display a link that will open a preview of the referenced section.
References are stored in the 'references' monngodb collection.
If the reference is unresolved we show a red underline.
SystemAdmin users can create, edit, and delete new references.
Currently, all references are available to all users.
Future: Allow users to create their own references. Use AI to suggest references. Think of it like turning the statute into your own personal wiki.

## Statute Reader
Users can view the selected statute content at the Chapter, Subchapter, or Section level.
When loading content, always load the direct parent nodes of the selected node, the selected node, and all child nodes underneath it recursively.
When selecting a Subchapter or Section, also load the parent chapter, so you always have at least one entire chapter loaded.
Selecting a Collection, Code, Title, or Subtitle will just display an overview since it is too much content to load all at once.
Highlights, References, and Search matches are displayed inline.
There is a quick FooterNavigation bar at the bottom that lets you quickly navigate to next/prev of the currently selected node level.
There is a BreadcrumbPath displayed at the top to provide the user context of where they are at in the statute hierarchy and provide quick nav to each item in the parent hierarcy.
The user can quickly bookmark or select a node from the reader view.
