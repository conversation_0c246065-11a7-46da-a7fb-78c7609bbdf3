'use client';

import { SessionProvider } from "next-auth/react"
import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HighlightProvider } from '@/components/features/highlights';
import { TreeStateProvider } from '@/components/features/navtree';
import { OrderStateProvider } from '@/components/features/navtree';
import { BookmarkProvider, BookmarksPanel } from '@/components/features/bookmarks';
import { SearchProvider, SearchPanel } from '@/components/features/search';
import { ReferenceProvider } from '@/components/features/references';
import { ReferencePopoverProvider } from '@/components/features/references/context/ReferencePopoverContext';
import { ContextSidebarProvider } from '@/components/features/context-sidebar';
import { Header } from '@/components/Header';
import ResizablePanel from '@/components/ui/ResizablePanel';
import { TOCPanel } from '@/components/features/navtree/components/TOCPanel';
import { Suspense, useEffect } from 'react';
import { LayoutProvider, useLayout } from './LayoutContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import '@/components/features/statutes/components/FooterNavigation.css';

// Removed unused constants - using hardcoded values for memory optimization

export default function ClientProviders({ children }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // More reasonable cache times to prevent excessive re-fetching
        staleTime: 5 * 60 * 1000, // 5 minutes before considering stale
        gcTime: 15 * 60 * 1000, // 15 minutes garbage collection
        refetchOnWindowFocus: false, // Disable to reduce queries
        retry: false,
        refetchOnMount: false, // Reduce unnecessary fetches
        refetchOnReconnect: false, // Reduce unnecessary fetches
      },
    },
  }));

  // Panel state management (non-layout panels only)
  const [showBookmarksList, setShowBookmarksList] = useState(false);
  const [showSearchPanel, setShowSearchPanel] = useState(false);

  // CONSERVATIVE MEMORY MANAGEMENT: Clear query cache periodically but much less aggressively
  useEffect(() => {
    const clearCache = () => {
      // Only clear unused queries instead of everything
      queryClient.removeQueries({
        predicate: (query) => query.getObserversCount() === 0
      });
    };

    // Clear unused cache every 30 minutes instead of 5
    const interval = setInterval(clearCache, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, [queryClient]);

  return (
    <LayoutProvider>
      <SessionProvider>
        <QueryClientProvider client={queryClient}>
          <ReferencePopoverProvider>
            <HighlightProvider>
              <TreeStateProvider>
                <OrderStateProvider>
                  <BookmarkProvider>
                    <SearchProvider>
                      <ReferenceProvider>
                        <ContextSidebarProvider>
                          <ClientProvidersContent
                            showBookmarksList={showBookmarksList}
                            setShowBookmarksList={setShowBookmarksList}
                            showSearchPanel={showSearchPanel}
                            setShowSearchPanel={setShowSearchPanel}
                          >
                            {children}
                          </ClientProvidersContent>
                        </ContextSidebarProvider>
                      </ReferenceProvider>
                    </SearchProvider>
                  </BookmarkProvider>
                </OrderStateProvider>
              </TreeStateProvider>
            </HighlightProvider>
          </ReferencePopoverProvider>
          <ToastContainer
            position="bottom-center"
            autoClose={3000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </QueryClientProvider>
      </SessionProvider>
    </LayoutProvider>
  );
}

// Component that uses layout context for UI structure
function ClientProvidersContent({ children, showBookmarksList, setShowBookmarksList, showSearchPanel, setShowSearchPanel }) {
  const { isMobile, showTOCPanel, setShowTOCPanel, showMobileTOCPanel, setShowMobileTOCPanel } = useLayout();

  return (
    <>
      <Header
        showBookmarksList={showBookmarksList}
        setShowBookmarksList={setShowBookmarksList}
        showSearchPanel={showSearchPanel}
        setShowSearchPanel={setShowSearchPanel}
      />
      <SearchPanel
        isOpen={showSearchPanel}
        onClose={() => setShowSearchPanel(false)}
      />
      <BookmarksPanel
        isOpen={showBookmarksList}
        onClose={() => setShowBookmarksList(false)}
      />
      {/* Mobile TOC Panel - only render on mobile */}
      {isMobile && (
        <div className={`fixed bg-white shadow-lg z-[45] transition-transform duration-300 ease-in-out left-0 right-0 bottom-0 top-[60px] border-t border-gray-300 ${showMobileTOCPanel ? 'translate-y-0' : 'translate-y-full'}`}>
          <TOCPanel
            isMobile={true}
            onClose={() => setShowMobileTOCPanel(false)}
            className="bg-white"
          />
        </div>
      )}
      <Suspense fallback={<div>Loading...</div>}>
        {!isMobile ? (
          // Desktop/Tablet: Use ResizablePanel with TOC
          <ResizablePanel
            initialWidth={400}
            minWidth={330}
            maxWidth={600}
            isPanelOpen={showTOCPanel}
          >
            <TOCPanel onClose={() => setShowTOCPanel(false)} />
            <div className="bg-white" style={{ height: 'calc(100vh - 60px)' }}>
              {children}
            </div>
          </ResizablePanel>
        ) : (
          // Mobile: Simple full-width layout
          <div className="bg-white" style={{ height: '100vh', paddingTop: '60px' }}>
            {children}
          </div>
        )}
      </Suspense>
    </>
  );
}
