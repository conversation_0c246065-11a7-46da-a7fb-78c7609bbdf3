'use client';

import { createContext, useContext, useState, useEffect } from 'react';

const LayoutContext = createContext({
  isMobile: false,
  isSmallMobile: false,
  showTOCPanel: true,
  setShowTOCPanel: () => {},
  showMobileTOCPanel: false,
  setShowMobileTOCPanel: () => {}
});

export function LayoutProvider({ children }) {
  // Panel state management
  const [showTOCPanel, setShowTOCPanel] = useState(true);
  const [showMobileTOCPanel, setShowMobileTOCPanel] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isSmallMobile, setIsSmallMobile] = useState(false);

  // Detect mobile viewports with more granular breakpoints
  useEffect(() => {
    const checkViewport = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsSmallMobile(width < 480);
    };
    checkViewport();
    window.addEventListener('resize', checkViewport);
    return () => window.removeEventListener('resize', checkViewport);
  }, []);

  const value = {
    isMobile,
    isSmallMobile,
    showTOCPanel,
    setShowTOCPanel,
    showMobileTOCPanel,
    setShowMobileTOCPanel
  };

  return (
    <LayoutContext.Provider value={value}>
      {children}
    </LayoutContext.Provider>
  );
}

export const useLayout = () => useContext(LayoutContext);