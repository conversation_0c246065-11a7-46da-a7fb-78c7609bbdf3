'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { StatuteContent } from '@/components/features/statutes';
import { useTreeState } from '@/components/features/navtree';

export default function HomePage() {
  const { selectNode } = useTreeState();
  const pathname = usePathname();

  // Handle URL changes
  useEffect(() => {
    if (!pathname) return;

    // Update tree state
    selectNode(pathname);
  }, [pathname, selectNode]);

  return <StatuteContent />;
}
