@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure white background */
html, body {
  background-color: white;
}

/* Add these base styles */
@layer utilities {
  /* Chrome, Safari and Opera */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(209 213 219);
    border-radius: 9999px;
    visibility: hidden;
  }

  .scrollbar-thin:hover::-webkit-scrollbar-thumb {
    visibility: visible;
  }

  /* Firefox */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) transparent;
  }
}

:root {
  --background: #ffffff;
  --foreground: #ffffff;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Georgia, serif;
  line-height: 1.6;
}
