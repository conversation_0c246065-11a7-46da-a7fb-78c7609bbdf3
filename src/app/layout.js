import './globals.css';
import ClientProviders from './ClientProviders';
import { SpeedInsights } from "@vercel/speed-insights/next"
import { Analytics } from '@vercel/analytics/next';
import Script from 'next/script';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        {process.env.NODE_ENV === 'development' && (
          <Script src="http://localhost:8097" strategy="beforeInteractive" />
        )}
      </head>
      <body>
        <ClientProviders>
          {children}
        </ClientProviders>
        <SpeedInsights />
        <Analytics />
      </body>
    </html>
  );
}
