export class Reference {
  constructor({
    id = null,
    nodeId,
    text,
    startOffset,
    endOffset,
    matches = [],
    createdAt = new Date(),
    updatedAt = new Date()
  }) {
    this.id = id;
    this.nodeId = nodeId;
    this.text = text;
    this.startOffset = startOffset;
    this.endOffset = endOffset;
    this.matches = matches;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  toJSON() {
    return {
      _id: this.id,
      nodeId: this.nodeId,
      text: this.text,
      startOffset: this.startOffset,
      endOffset: this.endOffset,
      matches: this.matches,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  static fromJSON(json) {
    if (!json) return null;

    return new Reference({
      id: json._id?.toString() || json.id,
      nodeId: json.nodeId,
      text: json.text,
      startOffset: json.startOffset,
      endOffset: json.endOffset,
      matches: json.matches || [],
      createdAt: json.createdAt ? new Date(json.createdAt) : new Date(),
      updatedAt: json.updatedAt ? new Date(json.updatedAt) : new Date()
    });
  }
}
