export const runtime = "nodejs";

import { mongoClient } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export class ReferenceStore {
  static REFERENCES_COLLECTION = 'references';

  /**
   * Retrieves the MongoDB collection for references.
   * @returns {Promise<Collection>} A promise that resolves to the references collection.
   * @private
   */
  static async referencesCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.REFERENCES_COLLECTION);
  }

  /**
   * Retrieves all references.
   * @returns {Promise<Object[]>} A promise that resolves to an array of reference objects.
   * @throws Will throw an error if the fetch operation fails.
   */
  static async getAllReferences() {
    try {
      const referencesCollection = await this.referencesCollection();
      const references = await referencesCollection.find({}).toArray();
      return references;
    }
    catch (error) {
      console.error('Error getting references:', error);
      throw error;
    }
  }

  /**
   * Retrieves all references contained within a specific node.
   * @param {string} nodeId - The ID of the node containing the references.
   * @returns {Promise<Object[]>} A promise that resolves to an array of reference objects.
   */
  static async getReferences(nodeId) {
    try {
      const referencesCollection = await this.referencesCollection();
      const references = await referencesCollection.find({
        nodeId: nodeId
      }).toArray();
      return references;
    }
    catch (error) {
      console.error('Error getting references for node:', error);
      throw error;
    }
  }

  /**
   * Creates a new reference.
   * @param {Object} referenceJSON - The reference object to create.
   * @returns {Promise<Object>} A promise that resolves to the created reference object.
   * @throws Will throw an error if the create operation fails.
   */
  static async addReference(referenceJSON) {
    try {
      const referencesCollection = await this.referencesCollection();
      const result = await referencesCollection.insertOne({
        ...referenceJSON,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      referenceJSON._id = result.insertedId;
      return referenceJSON;
    }
    catch (error) {
      console.error('Error adding reference:', error);
      throw error;
    }
  }

  /**
   * Updates an existing reference.
   * @param {Object} updatedReferenceJSON - The updated reference object.
   * @returns {Promise<Object>} A promise that resolves to the updated reference object.
   * @throws Will throw an error if the update operation fails.
   */
  static async updateReference(updatedReferenceJSON) {
    try {
      const referencesCollection = await this.referencesCollection();

      // Create update data without the _id field
      const updateData = { ...updatedReferenceJSON };
      delete updateData._id;

      const result = await referencesCollection.updateOne(
        { _id: new ObjectId(updatedReferenceJSON._id) },
        {
          $set: {
            ...updateData,
            updatedAt: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        return null;
      }

      // Fetch and return the updated document
      const freshlyUpdatedReference = await referencesCollection.findOne({
        _id: new ObjectId(updatedReferenceJSON._id)
      });

      return freshlyUpdatedReference;
    }
    catch (error) {
      console.error('Error updating reference:', error);
      throw error;
    }
  }

  /**
   * Removes a reference.
   * @param {string} referenceId - The ID of the reference to remove.
   * @returns {Promise<Object|null>} The deleted reference object, or null if not found.
   * @throws Will throw an error if the remove operation fails.
   */
  static async removeReference(referenceId) {
    try {
      const referencesCollection = await this.referencesCollection();
      // Fetch the reference before deleting
      const ref = await referencesCollection.findOne({ _id: new ObjectId(referenceId) });
      if (!ref) return null;
      await referencesCollection.deleteOne({ _id: new ObjectId(referenceId) });
      return ref;
    }
    catch (error) {
      console.error('Error removing reference:', error);
      throw error;
    }
  }
}
