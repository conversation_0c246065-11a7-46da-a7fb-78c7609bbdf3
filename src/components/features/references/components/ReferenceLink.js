import React, { useRef, useState, useCallback, useId } from 'react';
import PropTypes from 'prop-types';
import { useReferencePopover } from '../context/ReferencePopoverContext';
import { useReferencePopover as usePopoverEvents } from '../hooks/useReferencePopover';
import { ReferenceViewModal } from './ReferenceViewModal';
import { toast } from 'react-toastify';
import { ReferenceEditModal } from './ReferenceEditModal';
import { useReferences } from '../hooks/useReferences';
import { useSession } from 'next-auth/react';
import { isSystemAdmin } from '@/lib/models/roles';

/**
 * StatuteReferenceLink
 * Wraps a statute reference <a> with popover and error toast logic.
 * Forwards className, style, and other props to the anchor.
 */
export function ReferenceLink({ href, reference, children, className = '', style = {}, ...rest }) {
  const anchorRef = useRef(null);
  const instanceId = useId(); // Unique per link instance
  const popoverId = `statute-popover-${(href || '').replace(/[/.]/g, '-')}-${instanceId}`;
  const { openPopoverId, openPopover, closePopover } = useReferencePopover();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editReference, setEditReference] = useState(null);
  const { removeReference } = useReferences(reference?.nodeId);
  const { data: session } = useSession();
  const isAdmin = isSystemAdmin(session);

  // Handle click to toggle modal display
  const handleClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if this modal is currently open
    const isCurrentlyOpen = openPopoverId === popoverId || (editModalOpen && editReference === reference);

    if (isCurrentlyOpen) {
      // Close the modal
      closePopover();
      setEditModalOpen(false);
      setEditReference(null);
      return;
    }

    // Open the appropriate modal
    const hasUnresolvedMatch = reference?.matches?.some(match => match.status === 'unresolved');
    if (reference && hasUnresolvedMatch && isAdmin) {
      // Show edit modal for unresolved references
      closePopover();
      setEditReference(reference);
      setEditModalOpen(true);
    } else if (href) {
      // Show view modal for resolved references
      setEditModalOpen(false);
      setEditReference(null);
      openPopover(popoverId);
    }
  }, [openPopover, closePopover, popoverId, reference, editModalOpen, editReference, isAdmin, href, openPopoverId]);

  // Only use keyboard events from the hook
  const { eventProps } = usePopoverEvents({
    popoverId,
    isOpen: openPopoverId === popoverId,
    onOpen: openPopover,
    onClose: closePopover,
  });
  
  // Extract only the keyboard and accessibility props we want
  const { onKeyDown, tabIndex, 'aria-haspopup': ariaHaspopup, 'aria-expanded': ariaExpanded, 'aria-controls': ariaControls } = eventProps;

  // Edit handler
  const handleEdit = useCallback(() => {
    setEditReference(reference);
    setEditModalOpen(true);
    closePopover();
  }, [reference, closePopover]);

  // Delete handler
  const handleDelete = useCallback(async () => {
    if (!reference) return;
    const loadingToastId = toast.loading('Deleting reference...');
    try {
      await removeReference(reference);
      toast.dismiss(loadingToastId);
      toast.success('Reference deleted successfully');
      closePopover();
    } catch {
      toast.dismiss(loadingToastId);
      toast.error('Failed to delete reference');
    }
  }, [reference, closePopover, removeReference]);

  let linkElement;
  if (!href) {
    linkElement = (
      <span
        ref={anchorRef}
        className={className}
        style={style}
        onClick={handleClick}
        onKeyDown={onKeyDown}
        tabIndex={tabIndex}
        aria-haspopup={ariaHaspopup}
        aria-expanded={ariaExpanded}
        aria-controls={ariaControls}
        {...rest}
      >
        {children}
      </span>
    );
  } else {
    linkElement = (
      <a
        ref={anchorRef}
        href={href}
        className={className}
        style={style}
        onClick={handleClick}
        onKeyDown={onKeyDown}
        tabIndex={tabIndex}
        aria-haspopup={ariaHaspopup}
        aria-expanded={ariaExpanded}
        aria-controls={ariaControls}
        {...rest}
      >
        {children}
      </a>
    );
  }

  return (
    <>
      {linkElement}
      {openPopoverId === popoverId && href && !reference?.matches?.some(match => match.status === 'unresolved') && (
        <ReferenceViewModal
          anchorRef={anchorRef}
          href={href}
          linkText={children}
          popoverId={popoverId}
          onError={() => toast.error('Section not found')}
          onClose={closePopover}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      )}
      {editModalOpen && editReference && isAdmin && (
        <ReferenceEditModal
          open={editModalOpen}
          onClose={() => {
            setEditModalOpen(false);
            setEditReference(null);
          }}
          reference={editReference}
          highlight={{
            targetRect: anchorRef.current?.getBoundingClientRect(),
            nodeId: editReference.nodeId,
            text: editReference.text
          }}
        />
      )}
    </>
  );
}

ReferenceLink.propTypes = {
  href: PropTypes.string,
  reference: PropTypes.object,
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  style: PropTypes.object,
};
