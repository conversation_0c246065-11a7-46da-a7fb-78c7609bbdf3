import React, { useEffect, useRef, useState, useLayoutEffect } from 'react';
import ReactDOM from 'react-dom';
import { useQuery } from '@tanstack/react-query';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';
import { Pencil, Trash } from 'lucide-react';
import { ModalPositioning } from '@/lib/ModalPositioning';
import { useSession } from 'next-auth/react';
import { isSystemAdmin } from '@/lib/models/roles';
import './ReferenceModal.css';

/**
 * ReferenceViewModal
 * Props:
 * - anchorRef: ref to the <a> element (for positioning)
 * - href: string (the statute link)
 * - linkText: string (the text of the <a> link)
 * - popoverId: string (unique id for this popover)
 * - onError: function (called if fetch fails)
 * - onClose: function (called to close popover)
 * - onEdit: function (called when edit is clicked)
 * - onDelete: function (called when delete is clicked)
 */
export function ReferenceViewModal({ anchorRef, href, linkText, popoverId, onError, onClose, onEdit, onDelete }) {
  const popoverRef = useRef(null);
  const [position, setPosition] = useState({ top: 0, left: 0, placement: 'top' });
  const [positioned, setPositioned] = useState(false); // Track if popover is positioned
  const { data: session } = useSession();
  const isAdmin = isSystemAdmin(session);

  // Parse nodeId from href (strip leading slash if present)
  const nodeId = href.startsWith('/') ? href : `/${href}`;

  // Fetch section data using react-query
  const { data, isLoading, isError } = useQuery({
    queryKey: ['statute-section', nodeId],
    queryFn: () => StatuteService.getNode(nodeId),
    staleTime: 1000 * 60 * 10, // 10 minutes
  });

  // Call onError if fetch fails
  useEffect(() => {
    if (isError && onError) onError();
  }, [isError, onError]);

  // Calculate positioning using actual modal dimensions after render
  const getPopoverPosition = (viewportRect, modalElement) => {
    if (!viewportRect) return null;

    // Get actual modal dimensions if available, otherwise use reasonable estimate
    let modalHeight = 200; // fallback
    let modalWidth = 400;   // fallback

    if (modalElement) {
      const modalRect = modalElement.getBoundingClientRect();
      modalHeight = modalRect.height || 200;
      modalWidth = modalRect.width || 400;
    }

    // ModalPositioning now handles coordinate conversion internally
    return ModalPositioning.calculatePosition(viewportRect, {
      width: modalWidth,
      height: modalHeight,
      margin: 8,
      preferAbove: true,
      useFixedPosition: true
    });
  };

  // Position the popover above or below the anchor
  useLayoutEffect(() => {
    if (!anchorRef?.current || !popoverRef.current) {
      setPositioned(false);
      return;
    }

    const anchorRect = anchorRef.current.getBoundingClientRect();

    // Calculate position without causing layout shifts
    const calculatePosition = () => {
      const popoverPosition = getPopoverPosition(anchorRect, popoverRef.current);

      // Fallback positioning if no panel
      const finalPosition = popoverPosition || ModalPositioning.getFallbackPosition('.statute-content', 400, 200);


      setPosition({
        top: finalPosition.top,
        left: finalPosition.left,
        placement: finalPosition.shouldFlip ? 'bottom' : 'top'
      });
      setPositioned(true); // Mark as positioned so popover becomes visible
    };

    // Calculate position immediately to prevent layout shifts
    calculatePosition();

    // Recalculate immediately if not positioned yet
    if (!positioned) {
      calculatePosition();
    }
  }, [anchorRef, isLoading, data, positioned]);

  // Close on click outside or Esc
  useEffect(() => {
    function handleClick(e) {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(e.target) &&
        anchorRef.current &&
        !anchorRef.current.contains(e.target)
      ) {
        onClose();
      }
    }
    function handleKeyDown(e) {
      if (e.key === 'Escape') onClose();
    }
    document.addEventListener('mousedown', handleClick);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('mousedown', handleClick);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose, anchorRef]);

  // Always render the modal (show loading state if needed)

  // Hide the popover until it is positioned to prevent flicker at (0,0)
  const popoverStyle = {
    position: 'fixed',
    top: `${position.top}px`,
    left: `${position.left}px`,
    visibility: positioned ? 'visible' : 'hidden',
    pointerEvents: positioned ? 'auto' : 'none',
    zIndex: 1301,
    willChange: 'transform, opacity', // Hint to browser for optimization
    contain: 'layout style paint', // Prevent layout impact on other elements
  };

  const popoverContent = (
    <div className="absolute inset-0 pointer-events-none" style={{zIndex: 1300}}>
      <div
        ref={popoverRef}
        id={popoverId}
        className="bg-white rounded-lg shadow-lg p-4 pointer-events-auto border max-w-[400px] max-h-[60vh] overflow-y-auto text-sm text-gray-900"
        role="dialog"
        aria-modal="true"
        tabIndex={-1}
        style={popoverStyle}
      >
        {/* Heading */}
        <div className="font-bold mb-2">{linkText}</div>
        {/* Body */}
        <div className="mb-4">
          {isLoading && <span className="text-gray-500">Loading...</span>}
          {isError && <span className="text-red-600">Section not found.</span>}
          {data && (
            <span>
              {data.description || data.text ?
                truncateWords(data.description || data.text, 500) :
                <em>No content available.</em>
              }
            </span>
          )}
        </div>
        {/* Footer */}
        <div className="pt-2 border-t flex items-center justify-between">
          <div className="flex gap-2 items-center">
            {isAdmin && (
              <>
                <button
                  type="button"
                  title="Edit Reference"
                  className="p-1 rounded hover:bg-gray-100 text-gray-600"
                  onClick={onEdit}
                >
                  <Pencil size={16} />
                </button>
                <button
                  type="button"
                  title="Delete Reference"
                  className="p-1 rounded hover:bg-gray-100 text-red-600"
                  onClick={onDelete}
                >
                  <Trash size={16} />
                </button>
              </>
            )}
          </div>
          <a
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            className="active-blue hover:underline font-medium"
          >
            View section →
          </a>
        </div>
      </div>
    </div>
  );

  // Use panel-relative portal like ReferenceEditModal
  return typeof window !== 'undefined'
    ? ReactDOM.createPortal(popoverContent, document.body)
    : null;
}

// Utility to truncate to ~500 words
function truncateWords(text, maxWords) {
  if (!text) return '';
  const words = text.split(/\s+/);
  if (words.length <= maxWords) return text;
  return words.slice(0, maxWords).join(' ') + '…';
}
