import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import { Reference } from '@/components/features/references/models/Reference';
import { useReferences } from '@/components/features/references/hooks/useReferences';
import { SearchService } from '@/components/features/search/services/SearchService';
import { Trash as TrashIcon, ChevronLeft, ChevronRight, X as XIcon } from 'lucide-react';
import { ModalPositioning } from '@/lib/ModalPositioning';
import { CODE_MAP } from '@/lib/codeMaps';
import './ReferenceModal.css';

/**
 * ReferenceEditModal
 * Props:
 * - open: boolean (controls visibility)
 * - onClose: function (called to close modal)
 * - highlight: { text, startOffset, endOffset, nodeId } (context for new reference)
 * - reference: Reference object (for editing, optional)
 * - onSave: function (called with saved reference)
 * - onDelete: function (called after delete)
 */
export function ReferenceEditModal({ open, onClose, highlight, reference, onSave, onDelete }) {
  // Core state
  const [nodeIdValue, setNodeIdValue] = useState('');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Multi-match state
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0);
  const [editedMatchValues, setEditedMatchValues] = useState({});

  // Positioning state
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [positioned, setPositioned] = useState(false);

  // Refs
  const modalRef = useRef(null);
  const selectRef = useRef(null);

  // Context hooks
  const nodeId = reference?.nodeId || highlight?.nodeId;
  const { addReference, updateReference, removeReference } = useReferences(nodeId);

  // Initialize nodeId value and match index when modal opens
  useEffect(() => {
    if (open) {
      setEditedMatchValues({});

      // Find the first unresolved match, or default to 0
      let initialMatchIndex = 0;
      if (reference?.matches) {
        const firstUnresolvedIndex = reference.matches.findIndex(match => match.status === 'unresolved');
        if (firstUnresolvedIndex !== -1) {
          initialMatchIndex = firstUnresolvedIndex;
        }
      }
      setCurrentMatchIndex(initialMatchIndex);

      // For existing references, use the target from the initial match
      // For new references or unresolved matches, use smart text transformation
      let initialValue = '';
      const initialMatch = reference?.matches?.[initialMatchIndex];
      if (initialMatch?.target) {
        initialValue = initialMatch.target;
      } else {
        // Use the match text, reference text, or highlight text for smart transformation
        const textToTransform = initialMatch?.text || reference?.text || highlight?.text || '';
        initialValue = smartReferenceText(textToTransform, reference || highlight) || '';
      }

      setNodeIdValue(initialValue);
      setError(null);
      setSaving(false);
    }
  }, [open, reference, highlight]);

  // Update nodeId value when match index changes
  useEffect(() => {
    if (reference?.matches?.[currentMatchIndex]) {
      // Use edited value if available
      const editedValue = editedMatchValues[currentMatchIndex];
      if (editedValue !== undefined) {
        setNodeIdValue(editedValue);
        return;
      }

      // Use original match target if it exists
      const originalValue = reference.matches[currentMatchIndex].target;
      if (originalValue) {
        setNodeIdValue(originalValue);
        return;
      }

      // For unresolved matches, use smart text transformation
      const currentMatch = reference.matches[currentMatchIndex];
      const textToTransform = currentMatch.text || reference.text || '';
      const transformedValue = smartReferenceText(textToTransform, reference) || '';
      setNodeIdValue(transformedValue);
    }
  }, [currentMatchIndex, reference, editedMatchValues]);

  // Focus the select when positioned and creating new reference
  useEffect(() => {
    if (open && positioned && !reference && selectRef.current) {
      selectRef.current.focus();
    }
  }, [open, positioned, reference]);

  // Position the modal
  useLayoutEffect(() => {
    if (!open) {
      setPositioned(false);
      return;
    }

    // Get actual modal height or use reasonable estimate
    let modalHeight = 240; // More accurate estimate for typical modal with 3-row textarea
    if (modalRef.current) {
      const modalRect = modalRef.current.getBoundingClientRect();
      modalHeight = modalRect.height || 240;
    }

    const finalPosition = highlight?.targetRect
      ? ModalPositioning.calculatePosition(highlight.targetRect, {
          width: 400,
          height: modalHeight,
          useFixedPosition: true,
          preferAbove: true
        })
      : ModalPositioning.getFallbackPosition('.statute-content', 400, modalHeight);

    setPosition({ top: finalPosition.top, left: finalPosition.left });
    setPositioned(true);
  }, [open, highlight?.targetRect]);

  // Close on outside click or escape
  useEffect(() => {
    if (!open) return;

    const handleClickOutside = (event) => {
      if (modalRef.current?.contains(event.target) ||
          event.target.closest('[title="Add a reference"]')) {
        return;
      }
      onClose();
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') onClose();
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [open, onClose]);

  // Input handlers
  const handleInputChange = async (e) => {
    const value = e.target.value;
    setNodeIdValue(value);

    // Store edited value for current match
    setEditedMatchValues(prev => ({
      ...prev,
      [currentMatchIndex]: value
    }));

    if (value.length >= 2) {
      setIsSearching(true);
      setShowDropdown(true);
      try {
        const results = await SearchService.searchNodes(value, [], 'nodeId');
        setSearchResults(results || []);
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    } else {
      setSearchResults([]);
      setShowDropdown(false);
    }
  };

  const handleInputFocus = async () => {
    // When focusing with existing text, trigger search
    if (nodeIdValue && nodeIdValue.length >= 2) {
      setIsSearching(true);
      setShowDropdown(true);
      try {
        const results = await SearchService.searchNodes(nodeIdValue, [], 'nodeId');
        setSearchResults(results || []);
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }
  };

  const handleInputBlur = () => {
    // Delay hiding dropdown to allow clicks
    setTimeout(() => {
      setShowDropdown(false);
    }, 200);
  };

  const handleSelectResult = (result) => {
    setNodeIdValue(result.nodeId);
    setShowDropdown(false);
    setSearchResults([]);

    // Store edited value for current match
    setEditedMatchValues(prev => ({
      ...prev,
      [currentMatchIndex]: result.nodeId
    }));
  };

  // Navigation handlers for multiple matches
  const handlePreviousMatch = () => {
    if (currentMatchIndex > 0) {
      setCurrentMatchIndex(currentMatchIndex - 1);
    }
  };

  const handleNextMatch = () => {
    if (reference?.matches && currentMatchIndex < reference.matches.length - 1) {
      setCurrentMatchIndex(currentMatchIndex + 1);
    }
  };

  // Build reference object for save
  const buildReference = () => {
    const text = reference?.text || highlight?.text || '';
    const startOffset = reference?.startOffset ?? highlight?.startOffset ?? 0;
    const endOffset = reference?.endOffset ?? highlight?.endOffset ?? 0;
    const nodeId = reference?.nodeId || highlight?.nodeId || '';

    // For new references, create a single match
    if (!reference) {
      return Reference.fromJSON({
        text,
        startOffset,
        endOffset,
        nodeId,
        pattern: 'manual',
        matches: [{
          text,
          startOffset,
          endOffset,
          target: nodeIdValue || '',
          status: nodeIdValue ? 'resolved' : 'unresolved',
        }],
      });
    }

    // For existing references, update all edited matches and preserve others
    const updatedMatches = [...(reference.matches || [])];

    // Apply all edited values
    Object.keys(editedMatchValues).forEach(indexStr => {
      const index = parseInt(indexStr);
      if (updatedMatches[index]) {
        const editedValue = editedMatchValues[index];
        updatedMatches[index] = {
          ...updatedMatches[index],
          target: editedValue || '',
          status: editedValue ? 'resolved' : 'unresolved',
        };
      }
    });

    // Also update the current match with the current nodeIdValue (in case it wasn't stored yet)
    if (updatedMatches[currentMatchIndex]) {
      updatedMatches[currentMatchIndex] = {
        ...updatedMatches[currentMatchIndex],
        target: nodeIdValue || '',
        status: nodeIdValue ? 'resolved' : 'unresolved',
      };
    }

    return Reference.fromJSON({
      ...(reference ? { id: reference.id, _id: reference._id } : {}),
      text,
      startOffset,
      endOffset,
      nodeId,
      pattern: reference.pattern || 'manual',
      matches: updatedMatches,
    });
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    try {
      const refObj = buildReference();
      const saved = reference
        ? await updateReference(refObj)
        : await addReference(refObj);
      onSave?.(saved);
      onClose();
    } catch (e) {
      setError(e.message || 'Failed to save reference');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!reference) return;
    setSaving(true);
    setError(null);
    try {
      await removeReference(reference);
      onDelete?.();
      onClose();
    } catch (e) {
      setError(e.message || 'Failed to delete reference');
    } finally {
      setSaving(false);
    }
  };

  if (!open) return null;

  const selectedText = reference?.text || highlight?.text || '';
  const currentMatch = reference?.matches?.[currentMatchIndex];
  const hasMultipleMatches = reference?.matches && reference.matches.length > 1;

  const modalContent = (
    <div
      ref={modalRef}
      className="bg-white rounded-lg shadow-lg p-4 pointer-events-auto border"
      style={{
        position: 'fixed',
        top: `${position.top}px`,
        left: `${position.left}px`,
        width: 400,
        minWidth: 320,
        visibility: positioned ? 'visible' : 'hidden',
        pointerEvents: positioned ? 'auto' : 'none',
        zIndex: 1301,
      }}
    >
        <div className="mb-2 font-semibold text-gray-700">
          {reference ? 'Edit Reference' : 'Create Reference'}
        </div>

        {/* Multi-match navigation */}
        {hasMultipleMatches && (
          <div className="mb-2 flex items-center justify-between bg-blue-50 px-3 py-2 rounded-md">
            <div className="text-xs active-blue font-medium">
              Match {currentMatchIndex + 1} of {reference.matches.length}
            </div>
            <div className="flex gap-1">
              <button
                onClick={handlePreviousMatch}
                disabled={currentMatchIndex === 0}
                className="p-1 rounded hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed active-blue"
                title="Previous match"
              >
                <ChevronLeft size={14} />
              </button>
              <button
                onClick={handleNextMatch}
                disabled={currentMatchIndex >= reference.matches.length - 1}
                className="p-1 rounded hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed active-blue"
                title="Next match"
              >
                <ChevronRight size={14} />
              </button>
            </div>
          </div>
        )}

        {/* Reference text display */}
        {selectedText && (
          <div className="mb-2 text-xs text-gray-500 max-w-full break-words">
            Reference text: <span className="font-mono bg-gray-100 px-1 py-0.5 rounded">&quot;{selectedText}&quot;</span>
          </div>
        )}

        {/* Current match text display (if different from reference text) */}
        {currentMatch?.text && currentMatch.text !== selectedText && (
          <div className="mb-2 text-xs text-gray-500 max-w-full break-words">
            Current match: <span className="font-mono bg-yellow-100 px-1 py-0.5 rounded">&quot;{currentMatch.text}&quot;</span>
          </div>
        )}

        <div className="mb-4">
          <label className="block text-xs text-gray-500 mb-1">Links to:</label>
          <div className="relative">
            <textarea
              ref={selectRef}
              value={nodeIdValue}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              placeholder="Enter or search for nodeId..."
              rows={3}
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none reference-focus-ring focus:ring-2 focus:border-blue-500 resize-none text-gray-900 text-sm font-mono leading-relaxed${nodeIdValue ? ' pr-8' : ''}`}
            />
            {nodeIdValue && (
              <button
                type="button"
                className="absolute top-2 right-2 p-1 rounded hover:bg-gray-200 focus:outline-none"
                title="Clear"
                onClick={() => {
                  setNodeIdValue('');
                  setEditedMatchValues(prev => ({
                    ...prev,
                    [currentMatchIndex]: ''
                  }));
                }}
                tabIndex={0}
              >
                <XIcon size={16} className="text-gray-500" />
              </button>
            )}
            {showDropdown && searchResults.length > 0 && (
              <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto">
                {searchResults.map((result) => (
                  <div
                    key={result.nodeId}
                    className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm text-gray-900 font-mono leading-relaxed break-words border-b border-gray-100 last:border-b-0"
                    onClick={() => handleSelectResult(result)}
                  >
                    {result.nodeId}
                  </div>
                ))}
              </div>
            )}
            {isSearching && (
              <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-md shadow-lg z-50 px-3 py-2 text-sm text-gray-600">
                Searching...
              </div>
            )}
          </div>
        </div>

        {error && <div className="text-xs text-red-500 mb-2">{error}</div>}

        <div className="flex gap-2 justify-end">
          {reference && (
            <button
              className="p-1 rounded hover:bg-gray-100 text-red-600 mr-auto flex items-center"
              title="Delete Reference"
              onClick={handleDelete}
              disabled={saving}
              type="button"
            >
              <TrashIcon size={16} />
            </button>
          )}
          <button
            className="text-xs px-3 py-1 rounded border bg-gray-100 hover:bg-gray-200 text-gray-700"
            onClick={onClose}
            disabled={saving}
          >
            Cancel
          </button>
          <button
            className="text-xs px-3 py-1 rounded border bg-active-blue text-white hover:bg-blue-700"
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
  );

  return typeof window !== 'undefined'
    ? ReactDOM.createPortal(modalContent, document.body)
    : null;
}

ReferenceEditModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  highlight: PropTypes.shape({
    text: PropTypes.string,
    startOffset: PropTypes.number,
    endOffset: PropTypes.number,
    nodeId: PropTypes.string,
  }),
  reference: PropTypes.object,
  onSave: PropTypes.func,
  onDelete: PropTypes.func,
};

// Helper to get parent section nodeId by stripping from first '(' (inclusive)
function getParentSectionNodeId(nodeId) {
  if (!nodeId) return '';
  const idx = nodeId.indexOf('(');
  return idx === -1 ? nodeId : nodeId.slice(0, idx).trim();
}

// Helper to extract the code (2-3 letter) from nodeId (e.g., /collection/tx/code/al/...)
function getCodeFromNodeId(nodeId) {
  if (!nodeId) return '';
  const match = nodeId.match(/\/code\/([a-zA-Z]{2,3})\//);
  return match ? match[1] : '';
}

// Smart replacement for reference search text
function smartReferenceText(selectedText, reference) {
  if (!selectedText) return selectedText;
  let result = selectedText;

  // Rule: External code reference, e.g. 'Section 25.11(a), Penal Code'
  // Matches 'Section(s)? <number>(subdivisions), <Code Name>' or 'Article(s)? <number>(subdivisions), <Code Name>'
  const externalCodeMatch = result.match(/^(Sections?|Articles?) ([0-9]+[A-Z]?(?:\.[0-9]+)?(\([^)]+\))*)\s*,\s*([^,]+)$/i);
  if (externalCodeMatch) {
    const type = /^Section/i.test(externalCodeMatch[1]) ? 'section' : 'article';
    const idWithSubdivisions = externalCodeMatch[2];
    const codeName = externalCodeMatch[4].trim().toLowerCase();
    // Find code in CODE_MAP (case-insensitive)
    let code = null;
    for (const [name, short] of Object.entries(CODE_MAP)) {
      if (name.toLowerCase() === codeName) {
        code = short;
        break;
      }
    }
    if (code) {
      return `code/${code} ${type}/${idWithSubdivisions}`;
    }
  }

  // Rule 0: If selectedText is a subsection (e.g., (a), (a-1), or (9)), handle as subdivision
  if (reference?.nodeId) {
    // Handle letter-based subsections: (a), (a-1)
    if (/^\([a-z]\)$/i.test(result) || /^\([a-z]-\d+\)$/i.test(result)) {
      const parentSectionNodeId = getParentSectionNodeId(reference.nodeId);
      return parentSectionNodeId + result;
    }
    // Handle number-based subdivisions: (1), (9), etc.
    if (/^\(\d+\)$/.test(result)) {
      const subdivisionId = result.slice(1, -1); // Remove parentheses
      // Remove the last parenthetical part from the nodeId and append the new subdivision
      const nodeIdWithoutLastParen = reference.nodeId.replace(/\([^)]*\)$/, '');
      return `${nodeIdWithoutLastParen}(${subdivisionId})`;
    }
  }

  // Rule 1: If result matches 'Chapters? <number>', replace with 'code/<code> chapter/<number>'
  const chapterMatch = result.match(/Chapters? (\d+)/i);
  if (chapterMatch && reference?.nodeId) {
    const code = getCodeFromNodeId(reference.nodeId);
    const chapterNum = chapterMatch[1];
    result = result.replace(/Chapters? (\d+)/i, `code/${code} chapter/${chapterNum}`);
  }

  // Rule 1.5: If result matches 'Sections? <number>(<subdivisions>)' or 'Articles? <number>(<subdivisions>)', return 'code/<code> section/<matched id>' or 'code/<code> article/<matched id>'
  // This regex captures the main number and any number of trailing parenthetical subdivisions
  const sectionOrArticleMatch = result.match(/^(Sections?|Articles?) ([0-9]+[A-Z]?(?:\.[0-9]+)?(\([^)]+\))*)/i);
  if (sectionOrArticleMatch && reference?.nodeId) {
    const code = getCodeFromNodeId(reference.nodeId);
    const type = /^Section/i.test(sectionOrArticleMatch[1]) ? 'section' : 'article';
    const idWithSubdivisions = sectionOrArticleMatch[2];
    return `code/${code} ${type}/${idWithSubdivisions}`;
  }

  // Rule 1.6: If the entire selected text matches /^\d+[A-Z]?\.\d+(<subdivisions>)*$/, treat as section
  if (/^\d+[A-Z]?\.\d+(\([^)]+\))*$/.test(result) && reference?.nodeId) {
    const code = getCodeFromNodeId(reference.nodeId);
    return `code/${code} section/${result}`;
  }

  // Rule 1.7: If result matches 'Article <section>, Code of Criminal Procedure', transform to 'code/cr section/<section>'
  const articleCriminalMatch = result.match(/Articles? ([^,]+), Code of Criminal Procedure/i);
  if (articleCriminalMatch) {
    const sectionId = articleCriminalMatch[1];
    return `code/cr section/${sectionId}`;
  }

  // Rule 1.8: If result matches 'Subdivision (<identifier>)', replace last parenthetical in current nodeId with the new subdivision
  const subdivisionMatch = result.match(/Subdivisions? \(([^)]+)\)/i);
  if (subdivisionMatch && reference?.nodeId) {
    const subdivisionId = subdivisionMatch[1];
    // Remove the last parenthetical part from the nodeId and append the new subdivision
    const nodeIdWithoutLastParen = reference.nodeId.replace(/\([^)]*\)$/, '');
    return `${nodeIdWithoutLastParen}(${subdivisionId})`;
  }

  // Rule 2: Replace first occurrence of 'Subsection ' or 'Subsections ' (case-insensitive) with parent section nodeId
  if (reference?.nodeId) {
    const parentSectionNodeId = getParentSectionNodeId(reference.nodeId);
    result = result.replace(/Subsections? /i, parentSectionNodeId);
  }

  return result;
}
