import { Reference } from '../models/Reference';

const API_VERSION = 'v1';
const BASE_URL = `/api/${API_VERSION}/references`;
const ENDPOINTS = {
  COLLECTION: BASE_URL,
  BY_ID: (id) => `${BASE_URL}/${id}`,
  BY_NODE: (nodeId) => `${BASE_URL}?node=${nodeId}`
};

/**
 * Service for interacting with the references API.
 * All methods are static and return Promises that resolve to Reference instances.
 */
export class ReferenceService {
  /**
   * Fetches references, optionally filtered by nodeId
   * @param {string} [nodeId] - Optional nodeId to filter references
   * @returns {Promise<Reference[]>} Array of Reference instances
   * @throws {Error} If the API request fails
   */
  static async getReferences(nodeId = null) {
    const url = nodeId ? ENDPOINTS.BY_NODE(nodeId) : ENDPOINTS.COLLECTION;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch references: ${response.statusText}`);
    }

    const data = await response.json();
    return ReferenceService._transformReferences(data);
  }

  /**
   * Fetches a single reference by ID
   * @param {string} referenceId - The ID of the reference to fetch
   * @returns {Promise<Reference|null>} The reference, or null if not found
   * @throws {Error} If the API request fails
   */
  static async getReferenceById(referenceId) {
    const response = await fetch(ENDPOINTS.BY_ID(referenceId));

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch reference: ${response.statusText}`);
    }

    const data = await response.json();
    return Reference.fromJSON(data);
  }

  /**
   * Adds a new reference
   * @param {Object} reference - The reference data to add
   * @returns {Promise<Reference>} The added reference
   * @throws {Error} If the API request fails
   */
  static async addReference(reference) {
    const response = await fetch(ENDPOINTS.COLLECTION, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(reference)
    });

    if (!response.ok) {
      throw new Error(`Failed to add reference: ${response.statusText}`);
    }

    const data = await response.json();
    return Reference.fromJSON(data);
  }

  /**
   * Removes a reference by object (expects at least id/_id and nodeId)
   * @param {Object} reference - The reference object to delete
   * @returns {Promise<{success: boolean, nodeId: string, id: string}>} The nodeId and id of the deleted reference
   * @throws {Error} If the API request fails
   */
  static async removeReference(reference) {
    const id = reference.id || reference._id;
    const response = await fetch(ENDPOINTS.BY_ID(id), {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete reference: ${response.statusText}`);
    }
    // Expect only nodeId and id in the response
    return await response.json();
  }

  /**
   * Updates a reference
   * @param {Reference} updatedReference - The updated reference data
   * @returns {Promise<Reference>} The updated reference
   * @throws {Error} If the API request fails
   */
  static async updateReference(updatedReference) {
    try {
      const jsonData = updatedReference.toJSON();
      const url = `${BASE_URL}/${updatedReference.id}`;
      const response = await fetch(url, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jsonData)
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update reference');
      }
      const data = await response.json();
      return Reference.fromJSON(data);
    } catch (error) {
      console.error('Error updating reference:', error);
      throw error;
    }
  }

  /**
   * Transforms raw API response data into Reference instances
   * @private
   * @param {Array|Object} data - The data to transform
   * @returns {Reference[]} Array of Reference instances
   */
  static _transformReferences(data) {
    if (!data) return [];
    const items = Array.isArray(data) ? data : [data];
    return items.map(ref => Reference.fromJSON(ref));
  }
}
