import React, { createContext, useContext, useState, useCallback } from 'react';

// Context for managing which statute reference popover is open
const ReferencePopoverContext = createContext({
  openPopoverId: null,
  openPopover: () => {},
  closePopover: () => {},
});

export function ReferencePopoverProvider({ children }) {
  const [openPopoverId, setOpenPopoverId] = useState(null);

  const openPopover = useCallback((id) => {
    setOpenPopoverId(id);
  }, []);

  const closePopover = useCallback(() => {
    setOpenPopoverId(null);
  }, []);

  return (
    <ReferencePopoverContext.Provider value={{ openPopoverId, openPopover, closePopover }}>
      {children}
    </ReferencePopoverContext.Provider>
  );
}

export function useReferencePopover() {
  return useContext(ReferencePopoverContext);
}
