'use client';

import React, { createContext } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ReferenceService } from '../services/ReferenceService';

// Create context
const ReferenceContext = createContext(null);

// Reference query keys
const QUERY_KEYS = {
  references: 'references',
  referencesByNode: (nodeId) => ['references', 'node', nodeId],
};

/**
 * Provider component for references
 */
export function ReferenceProvider({ children }) {
  const queryClient = useQueryClient();

  // Mutation for adding a reference
  const addReferenceMutation = useMutation({
    mutationFn: (reference) => ReferenceService.addReference(reference),
    onSuccess: (newReference) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.references] });
      if (newReference.nodeId) {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.referencesByNode(newReference.nodeId) });
        queryClient.invalidateQueries({ queryKey: ['statute-content', newReference.nodeId] });
        queryClient.invalidateQueries({ queryKey: ['highlights', newReference.nodeId] });
      }
    }
  });

  // Mutation for updating a reference
  const updateReferenceMutation = useMutation({
    mutationFn: (reference) => ReferenceService.updateReference(reference),
    onSuccess: (updatedReference) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.references] });
      if (updatedReference.nodeId) {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.referencesByNode(updatedReference.nodeId) });
        queryClient.invalidateQueries({ queryKey: ['statute-content', updatedReference.nodeId] });
        queryClient.invalidateQueries({ queryKey: ['highlights', updatedReference.nodeId] });
      }
    }
  });

  // Mutation for removing a reference
  const removeReferenceMutation = useMutation({
    mutationFn: (reference) => ReferenceService.removeReference(reference),
    onSuccess: (deletedInfo) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.references] });
      if (deletedInfo?.nodeId) {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.referencesByNode(deletedInfo.nodeId) });
        queryClient.invalidateQueries({ queryKey: ['statute-content', deletedInfo.nodeId] });
        queryClient.invalidateQueries({ queryKey: ['highlights', deletedInfo.nodeId] });
      }
    }
  });

  // Create context value
  const contextValue = {
    getReferences: (nodeId) => ReferenceService.getReferences(nodeId),
    addReference: addReferenceMutation.mutateAsync,
    updateReference: updateReferenceMutation.mutateAsync,
    removeReference: removeReferenceMutation.mutateAsync,
    QUERY_KEYS
  };

  return (
    <ReferenceContext.Provider value={contextValue}>
      {children}
    </ReferenceContext.Provider>
  );
}

export { ReferenceContext };
