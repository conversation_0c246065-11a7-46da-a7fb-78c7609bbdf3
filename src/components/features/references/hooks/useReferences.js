import { useContext } from 'react';
import { useQuery } from '@tanstack/react-query';
import { ReferenceContext } from '@/components/features/references/context/ReferenceContext';

export const useReferences = (nodeId) => {
  const context = useContext(ReferenceContext);

  if (!context) {
    throw new Error('useReferences must be used within a ReferenceProvider');
  }

  const { getReferences, addReference, updateReference, removeReference, QUERY_KEYS } = context;

  // Get references for this specific node
  const { data: references = [], isLoading, error } = useQuery({
    queryKey: QUERY_KEYS.referencesByNode(nodeId),
    queryFn: () => getReferences(nodeId),
    enabled: !!nodeId,
    staleTime: 0,
    gcTime: 2 * 60 * 1000
  });

  // Return nodeReferences to match the pattern in HighlightContent
  const nodeReferences = references;

  return {
    nodeReferences,
    isLoading,
    error,
    addReference,
    updateReference,
    removeReference
  };
};
