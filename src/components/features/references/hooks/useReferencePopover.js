import { useCallback } from 'react';

/**
 * useReferencePopover
 * Handles hover, long-press, and keyboard events for reference popovers.
 *
 * @param {Object} options
 *   - popoverId: string (unique id for this popover)
 *   - isOpen: boolean (whether this popover is open)
 *   - onOpen: function (called to open popover)
 *   - onClose: function (called to close popover)
 * @returns {Object} { eventProps, isActive }
 */
export function useReferencePopover({ popoverId, isOpen, onOpen, onClose }) {
  // Mouse enter: open immediately
  const onMouseEnter = useCallback(() => {
    onOpen(popoverId);
  }, [onOpen, popoverId]);

  // Mouse leave: close
  const onMouseLeave = useCallback(() => {
    if (isOpen) onClose();
  }, [isOpen, onClose]);

  // Touch start: open immediately
  const onTouchStart = useCallback(() => {
    onOpen(popoverId);
  }, [onOpen, popoverId]);

  // Touch end/cancel: close
  const onTouchEnd = useCallback(() => {
    if (isOpen) onClose();
  }, [isOpen, onClose]);

  // Keyboard: spacebar toggles popover
  const onKeyDown = useCallback((e) => {
    if (e.code === 'Space' || e.key === ' ') {
      e.preventDefault();
      if (isOpen) {
        onClose();
      } else {
        onOpen(popoverId);
      }
    }
  }, [isOpen, onOpen, onClose, popoverId]);

  // Props to spread onto the <a> element
  const eventProps = {
    onMouseEnter,
    onMouseLeave,
    onTouchStart,
    onTouchEnd,
    onTouchCancel: onTouchEnd,
    onKeyDown,
    tabIndex: 0, // ensure focusable
    'aria-haspopup': 'dialog',
    'aria-expanded': isOpen,
    'aria-controls': isOpen ? popoverId : undefined,
  };

  return { eventProps, isActive: isOpen };
}
