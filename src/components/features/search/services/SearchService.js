import { SearchResult } from '../models/SearchResult';
import { SearchMatch } from '../models/SearchMatch';
import { Node } from '@/components/features/statutes/models/Node';

export class SearchService {
  static async searchNodes(query, codes = [], searchField = 'text') {
    try {
      const params = new URLSearchParams({
        q: query,
        ...(codes.length > 0 ? { codes: codes.join(',') } : {}),
        searchField
      });
      const response = await fetch(`/api/v1/search?${params.toString()}`);
      const data = await response.json();

      if (searchField === 'nodeId') {
        // For nodeId search, just return the raw results (array of { nodeId })
        return data;
      }

      // Transform MongoDB results into SearchResult instances
      const transformedResults = data.map(result => {
        try {
          // Get parent ID from nodeId
          const parts = result.nodeId.split('/');
          const parentId = parts.slice(0, -1).join('/') || '/';

          // Create Node instance from the result itself since the node data is at the top level
          const node = new Node({
            nodeId: result.nodeId,
            id: parts[parts.length - 1],
            type: result.type,
            text: result.text,
            code: result.code,
            collection: result.collection,
            parentId,
            hierarchy: result.hierarchy || []
          });

          // Use the server's match data if available, otherwise create our own
          const matches = result.matches?.map(match => {
            return new SearchMatch({
              text: match.text || result.text,
              index: match.index || 0,
              length: match.length || query.length,
              score: match.score || 0
            });
          }) || [
            // Fallback to creating our own match if server didn't provide any
            new SearchMatch({
              text: result.text,
              index: result.text.toLowerCase().indexOf(query.toLowerCase()),
              length: query.length,
              score: result.score || 0
            })
          ];

          // Create and return a SearchResult instance
          const searchResult = new SearchResult({
            node,
            matches,
            score: result.score || 0
          });

          return searchResult;
        } catch (error) {
          console.error('Error processing search result:', error, result);
          return null;
        }
      }).filter(Boolean); // Remove any null results from errors

      return transformedResults;
    } catch (error) {
      console.error('SearchService error:', error);
      throw error;
    }
  }
}
