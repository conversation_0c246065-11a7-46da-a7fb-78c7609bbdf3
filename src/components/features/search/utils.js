import { SearchResult, SearchMatch } from '@/components/features/search';

export function searchNodes(query, rootNode, selectedCodes = []) {
  if (!query) return [];
  if (typeof query !== 'string' || query.length === 0 || !rootNode) return [];
  if (selectedCodes.length === 0) return [];

  const results = [];
  const searchTerm = query.toLowerCase();
  const sectionMatches = new Map(); // Track matches by section nodeId

  // Scoring constants
  const SCORES = {
    TITLE: {
      EXACT: 100,    // Exact match in title
      PARTIAL: 50    // Partial match in title
    },
    CONTENT: {
      EXACT: 80,     // Exact match in content
      PARTIAL: 30    // Partial match in content
    }
  };

  function isNodeInSelectedCodes(node) {
    if (!node) return false;
    
    // Allow root and collection nodes to pass through so we can search their children
    if (node.type === 'root' || node.type === 'collection') return true;
    
    // Get the code from the node's hierarchy
    const hierarchy = node.getHierarchy();
    const codeNode = hierarchy.find(n => n.type === 'code');
    
    if (!codeNode) {
      return false;
    }
    
    const isIncluded = selectedCodes.includes(codeNode.nodeId);
    
    return isIncluded;
  }

  function searchSingleNode(node, searchTerm, selectedCodes) {
    // Skip if node or its ancestors aren't in selected codes
    if (!isNodeInSelectedCodes(node)) {
      return;
    }

    const nodeText = (node?.text || '').toLowerCase();
    const titleLevelTypes = ['title', 'subtitle', 'code', 'chapter', 'subchapter', 'section'];
    const isTitleText = titleLevelTypes.includes(node?.type);

    // For title-level nodes, create direct search results
    if (isTitleText && nodeText.includes(searchTerm)) {
      results.push(new SearchResult({
        node: node,
        matches: [new SearchMatch({
          text: node.text,
          index: nodeText.indexOf(searchTerm),
          length: searchTerm.length,
          score: nodeText === searchTerm ? SCORES.TITLE.EXACT : SCORES.TITLE.PARTIAL
        })],
        score: nodeText === searchTerm ? SCORES.TITLE.EXACT : SCORES.TITLE.PARTIAL,
        matchType: 'title'
      }));
    }
    // For subsections, collect matches under parent section
    else if (node?.type === 'subsection' && nodeText.includes(searchTerm)) {
      const parentSection = node.getHierarchy().find(n => n.type === 'section');
      if (parentSection) {
        const match = new SearchMatch({
          text: node.text,
          index: nodeText.indexOf(searchTerm),
          length: searchTerm.length,
          score: SCORES.CONTENT.PARTIAL
        });

        if (!sectionMatches.has(parentSection.nodeId)) {
          sectionMatches.set(parentSection.nodeId, {
            section: parentSection,
            matches: []
          });
        }
        sectionMatches.get(parentSection.nodeId).matches.push(match);
      }
    }

    // Recursively search children
    if (node?.children?.length > 0) {
      node.children.forEach(child => searchSingleNode(child, searchTerm, selectedCodes));
    }
  }

  searchSingleNode(rootNode, searchTerm, selectedCodes);

  // Convert section matches to search results
  for (const [, data] of sectionMatches) {
    results.push(new SearchResult({
      node: data.section,
      matches: data.matches,
      score: Math.max(...data.matches.map(m => m.score)),
      matchType: 'content'
    }));
  }

  return results.sort((a, b) => b.score - a.score);
} 