import { Model } from '@/lib/models/Model';

export class SearchResult extends Model {
  constructor({
    node,
    section = null,
    matches = [],
    score = 0,
    matchType = 'content'
  }) {
    super();
    this.node = node;
    this.section = section;
    this.matches = matches;
    this.score = score;
    this.matchType = matchType;
  }

  getFormattedPath() {
    const path = this.node.getHierarchy()
      .filter(n => n.text)
      .filter(n => !n.isSubsection())
      .map(n => n.text.toUpperCase())
      .join(' > ');
    return path;
  }

  // Simplify to use node's built-in method
  getNodeForSelection() {
    return this.node;
  }

  // For title matches, return the full node name
  getTitleMatch() {
    if (this.matchType !== 'title' || !this.matches.length) return null;
    return this.matches[0].text;
  }

  // For content matches, return formatted matches with highlight positions
  getContentMatches() {
    if (this.matchType !== 'content') return [];
    return this.matches.map(match => match.getFormattedMatch());
  }
}
