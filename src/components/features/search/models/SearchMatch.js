import { Model } from '@/lib/models/Model';

export class SearchMatch extends Model {
  constructor({
    text,
    index,
    length,
    score = 0
  }) {
    super();
    this.text = text;
    this.index = index;
    this.length = length;
    this.score = score;
  }

  // Get the formatted match with context
  getFormattedMatch(contextWords = 5) {
    if (!this.text || typeof this.index !== 'number' || !this.length) {
      console.warn('SearchMatch missing required data:', {
        text: !!this.text,
        index: this.index,
        length: this.length
      });
      return this.text || '';
    }

    const before = this.text.slice(0, this.index);
    const match = this.text.slice(this.index, this.index + this.length);
    const after = this.text.slice(this.index + this.length);

    // Get words before and after with preserved whitespace
    const beforeWords = before.match(/\S+\s*/g)?.slice(-contextWords).join('') || '';
    const afterWords = after.match(/\s*\S+\s*/g)?.slice(0, contextWords).join('') || '';

    const hasMoreBefore = (before.match(/\S+\s*/g) || []).length > contextWords;
    const hasMoreAfter = (after.match(/\s*\S+\s*/g) || []).length > contextWords;

    return `${hasMoreBefore ? '... ' : ''}${beforeWords}${match}${afterWords}${hasMoreAfter ? ' ...' : ''}`;
  }

  // Static method to find matches in text
  static findMatches(text, searchTerm) {
    if (!text || !searchTerm) return [];

    const matches = [];
    const searchTermLower = searchTerm.toLowerCase();
    let lastIndex = 0;

    while (true) {
      const index = text.toLowerCase().indexOf(searchTermLower, lastIndex);
      if (index === -1) break;

      matches.push(new SearchMatch({
        text,
        index,
        length: searchTerm.length,
        score: 1
      }));

      lastIndex = index + searchTerm.length;
    }

    return matches;
  }
}
