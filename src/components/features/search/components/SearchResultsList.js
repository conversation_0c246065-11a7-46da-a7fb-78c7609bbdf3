import React, { useCallback, forwardRef } from 'react';
import { Loader2 as Loader2Icon } from 'lucide-react';
import { useSearch } from '@/components/features/search';
import { SearchResultCell } from './SearchResultCell';
import { SearchInput } from './SearchInput';
import { SearchFilter } from './SearchFilter';
import { useTreeState } from '@/components/features/navtree';

export const SearchResultsList = forwardRef(({ onSelect, searchQuery: externalSearchQuery, isCompact = false }, ref) => {
  const {
    searchQuery: internalSearchQuery,
    searchResults,
    setSearchQuery,
    isSearching,
    selectedCodes,
    availableCodes,
    error
  } = useSearch();

  // Use external search query if provided, otherwise use internal
  const activeSearchQuery = externalSearchQuery !== undefined ? externalSearchQuery : internalSearchQuery;

  const { selectedNodeId } = useTreeState();

  const handleSearchChange = useCallback((value) => {
    setSearchQuery(value);
    localStorage.setItem('searchQuery', value);
  }, [setSearchQuery]);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    localStorage.removeItem('searchQuery');
  }, [setSearchQuery]);

  const handleResultSelect = useCallback((result) => {
    if (!result?.node) return;
    onSelect(result.node);
  }, [onSelect]);

  const renderSearchResults = useCallback(() => {
    if (!activeSearchQuery || activeSearchQuery.length < 3 || !selectedCodes.length) {
      return null;
    }

    if (isSearching) {
      return (
        <div className="search-info p-4 text-center text-gray-500">
          <Loader2Icon className="animate-spin h-5 w-5 inline-block mr-2" />
          Searching...
        </div>
      );
    }

    if (error) {
      return (
        <div className="search-info p-4 text-center text-red-500">
          Error performing search: {error.message}
        </div>
      );
    }

    if (!searchResults?.length) {
      return (
        <div className="search-info p-4 text-center text-gray-500">
          No results found
        </div>
      );
    }

    return (
      <div className="divide-y">
        {searchResults.map((result, index) => {
          return (
            <SearchResultCell
              key={`${result.node.nodeId}-${index}`}
              result={result}
              searchQuery={activeSearchQuery}
              onSelect={handleResultSelect}
              isSelected={result.node.nodeId === selectedNodeId}
            />
          );
        })}
      </div>
    );
  }, [activeSearchQuery, selectedCodes, isSearching, searchResults, handleResultSelect, error, selectedNodeId]);

  const renderResultsBar = useCallback(() => {
    const searchActive = activeSearchQuery && activeSearchQuery.length >= 3 && selectedCodes.length > 0 && searchResults.length > 0;
    if (!searchActive) return null;

    if (error) {
      return (
        <div className="search-results-bar flex items-center px-4 py-2 bg-gray-50 border-b text-red-500 text-sm">
          Error performing search: {error.message}
        </div>
      );
    }

    return (
      <div className="search-results-bar flex items-center px-4 py-2 bg-gray-50 border-b text-gray-700 text-sm">
        {searchResults.length} results found
      </div>
    );
  }, [activeSearchQuery, selectedCodes, searchResults, error]);

  // Compact mode for header search
  if (isCompact) {
    return (
      <div className="flex flex-col h-full">
        {renderSearchResults()}
      </div>
    );
  }

  // Full mode for sidebar search
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b sticky top-0 bg-white">
        <div className="flex gap-2">
          <div className="flex-[4]">
            <SearchInput
              ref={ref}
              value={activeSearchQuery}
              onChange={handleSearchChange}
              onClear={clearSearch}
            />
          </div>
          <div className="flex-1">
            <SearchFilter availableCodes={availableCodes} />
          </div>
        </div>
      </div>
      {renderResultsBar()}
      <div className="search-results-list flex-1 overflow-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
        {renderSearchResults()}
      </div>
    </div>
  );
});

SearchResultsList.displayName = 'SearchResultsList';
