import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SearchResultsList } from './SearchResultsList';
import { SearchProvider } from '@/components/features/search/context/SearchContext';
import { useSearch } from '@/components/features/search';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';
import { Node } from '@/components/features/statutes/models/Node';

// Mock the StatuteService
jest.mock('@/components/features/statutes/services/StatuteService', () => ({
  StatuteService: {
    getAvailableCodes: jest.fn().mockResolvedValue([
      { code: 'CODE1', text: 'Code One' },
      { code: 'CODE2', text: 'Code Two' }
    ])
  }
}));

// Mock the useSearch hook
jest.mock('@/components/features/search', () => ({
  useSearch: jest.fn()
}));

// Mock the useTreeState hook
jest.mock('@/components/features/navtree', () => ({
  useTreeState: jest.fn().mockReturnValue({
    selectedNodeId: 'test-node',
    setSelectedNodeId: jest.fn()
  })
}));

// Create a new QueryClient for each test
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      // Turn off retries and background updates for tests
      retry: false,
      // Prevent refetching during tests
      staleTime: Infinity,
      // Prevent garbage collection during tests
      cacheTime: Infinity,
      // Don't refetch on window focus
      refetchOnWindowFocus: false,
    },
  },
  // Silence React Query errors in console during tests
  logger: {
    log: console.log,
    warn: console.warn,
    error: () => {},
  }
});

// Create a wrapper component that provides both QueryClient and SearchContext
const renderWithProviders = (ui, mockSearchValue = {}) => {
  const testQueryClient = createTestQueryClient();
  const defaultSearchValue = {
    searchQuery: '',
    searchResults: [],
    setSearchQuery: jest.fn(),
    isSearching: false,
    selectedCodes: ['CODE1'],
    availableCodes: [{ code: 'CODE1', text: 'Code One' }],
    ...mockSearchValue
  };

  useSearch.mockImplementation(() => defaultSearchValue);

  return render(
    <QueryClientProvider client={testQueryClient}>
      <SearchProvider>
        {ui}
      </SearchProvider>
    </QueryClientProvider>
  );
};

describe('SearchResultsList', () => {
  const mockOnSelect = jest.fn();

  const mockSearchResult = {
    node: new Node({
      nodeId: '/collection/tx/code/ag/title/1/section/1.001',
      id: '1.001',
      type: 'section',
      text: 'Test Node',
      code: 'CODE1',
      hierarchy: [
        {
          nodeId: '/collection/tx',
          type: 'collection',
          text: 'Texas Statutes'
        },
        {
          nodeId: '/collection/tx/code/ag',
          type: 'code',
          text: 'Agriculture Code'
        },
        {
          nodeId: '/collection/tx/code/ag/title/1',
          type: 'title',
          text: 'Title 1'
        }
      ]
    }),
    matchCount: 1,
    matches: [
      {
        getFormattedMatch: () => 'Test Match'
      }
    ]
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Add a test to verify the StatuteService mock
  test('loads available codes correctly', async () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />
    );

    // Verify that getAvailableCodes was called
    expect(StatuteService.getAvailableCodes).toHaveBeenCalled();
  });

  test('renders search input and filter', () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />
    );

    expect(screen.getByPlaceholderText('Search statutes')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('shows no results when search query is too short', () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'ab',
        searchResults: [],
        isSearching: false
      }
    );
    expect(screen.queryByText('No results found')).not.toBeInTheDocument();
  });

  test('shows loading state while searching', () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'test',
        isSearching: true
      }
    );
    expect(screen.getByText('Searching...')).toBeInTheDocument();
  });

  test('shows no results message when search returns empty', () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'test',
        searchResults: [],
        isSearching: false
      }
    );
    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  test('renders search results when available', () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'test',
        searchResults: [mockSearchResult],
        isSearching: false
      }
    );

    // Look for the text in the specific element that contains the match
    const titleElement = screen.getByText((content, element) => {
      return element.className === 'text-md text-gray-900' && element.textContent.includes('Test Match');
    });
    expect(titleElement).toBeInTheDocument();
  });

  test('handles search input changes', async () => {
    const mockSetSearchQuery = jest.fn();
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        setSearchQuery: mockSetSearchQuery
      }
    );

    const user = userEvent.setup();
    const input = screen.getByPlaceholderText('Search statutes');
    await user.type(input, 'test');

    expect(mockSetSearchQuery).toHaveBeenCalledWith('t');
    expect(mockSetSearchQuery).toHaveBeenCalledWith('e');
    expect(mockSetSearchQuery).toHaveBeenCalledWith('s');
    expect(mockSetSearchQuery).toHaveBeenCalledWith('t');
  });

  test('clears search when clear button is clicked', async () => {
    const mockSetSearchQuery = jest.fn();
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'test',
        setSearchQuery: mockSetSearchQuery
      }
    );

    const user = userEvent.setup();
    const clearButton = screen.getByRole('button', {
      name: 'Clear search'
    });
    await user.click(clearButton);

    expect(mockSetSearchQuery).toHaveBeenCalledWith('');
  });

  test('calls onResultSelect when a result is clicked', async () => {
    const { container } = renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'test',
        searchResults: [mockSearchResult],
        isSearching: false
      }
    );

    const user = userEvent.setup();
    const resultContainer = container.querySelector('.search-result');
    await user.click(resultContainer);

    expect(mockOnSelect).toHaveBeenCalledWith(mockSearchResult.node);
  });

  test('does not show results when no codes are selected', () => {
    renderWithProviders(
      <SearchResultsList onSelect={mockOnSelect} />,
      {
        searchQuery: 'test',
        searchResults: [mockSearchResult],
        selectedCodes: []
      }
    );
    expect(screen.queryByText('Test Node')).not.toBeInTheDocument();
  });
});
