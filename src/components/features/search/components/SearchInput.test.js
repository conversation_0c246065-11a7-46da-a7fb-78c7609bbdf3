import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchInput } from './SearchInput';

describe('SearchInput', () => {
  const mockOnChange = jest.fn();
  const mockOnClear = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders with placeholder text', () => {
    render(<SearchInput value="" onChange={mockOnChange} onClear={mockOnClear} />);
    expect(screen.getByPlaceholderText('Search statutes')).toBeInTheDocument();
  });

  test('displays provided value', () => {
    render(
      <SearchInput
        value="test search"
        onChange={mockOnChange}
        onClear={mockOnClear}
      />
    );
    expect(screen.getByDisplayValue('test search')).toBeInTheDocument();
  });

  test('calls onChange with input value when user types', async () => {
    const user = userEvent.setup();
    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClear={mockOnClear}
      />
    );

    const input = screen.getByPlaceholderText('Search statutes');
    await user.type(input, 'test');

    // Check that onChange was called for each character typed
    expect(mockOnChange).toHaveBeenCalledTimes(4);
    expect(mockOnChange.mock.calls).toEqual([
      ['t'],    // First keystroke
      ['e'],    // Second keystroke
      ['s'],    // Third keystroke
      ['t']     // Fourth keystroke
    ]);
  });

  test('calls onChange with complete value when input changes', async () => {
    const user = userEvent.setup();
    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClear={mockOnClear}
      />
    );

    const input = screen.getByPlaceholderText('Search statutes');
    await user.clear(input);
    await user.paste('test search');

    // Should be called once with the complete value
    expect(mockOnChange).toHaveBeenCalledTimes(1);
    expect(mockOnChange).toHaveBeenCalledWith('test search');
  });

  test('clear button is not shown when value is empty', () => {
    render(
      <SearchInput
        value=""
        onChange={mockOnChange}
        onClear={mockOnClear}
      />
    );

    const clearButton = screen.queryByRole('button');
    expect(clearButton).not.toBeInTheDocument();
  });

  test('clear button is shown when value is not empty', () => {
    render(
      <SearchInput
        value="test"
        onChange={mockOnChange}
        onClear={mockOnClear}
      />
    );

    const clearButton = screen.getByRole('button');
    expect(clearButton).toBeInTheDocument();
  });

  test('calls onClear when clear button is clicked', async () => {
    const user = userEvent.setup();
    render(
      <SearchInput
        value="test"
        onChange={mockOnChange}
        onClear={mockOnClear}
      />
    );

    const clearButton = screen.getByRole('button');
    await user.click(clearButton);

    expect(mockOnClear).toHaveBeenCalledTimes(1);
  });
});
