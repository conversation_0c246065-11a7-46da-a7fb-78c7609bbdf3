import React, { forwardRef } from 'react';
import { Search as SearchIcon, X as XIcon } from 'lucide-react';

export const SearchInput = forwardRef(({ value, onChange, onClear }, ref) => {
  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <SearchIcon size={16} className="text-gray-400" />
      </div>
      <input
        ref={ref}
        type="text"
        className="block w-full pl-10 pr-10 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500"
        placeholder="Search statutes"
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      {value && (
        <button
          className="absolute inset-y-0 right-0 pr-3 flex items-center"
          onClick={onClear}
          aria-label="Clear search"
        >
          <XIcon size={16} className="text-gray-400 hover:text-gray-600" />
        </button>
      )}
    </div>
  );
});

SearchInput.displayName = 'SearchInput';
