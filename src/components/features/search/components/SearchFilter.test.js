import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchFilter } from './SearchFilter';
import { useSearch } from '../hooks/useSearch';

// Mock createPortal to render content directly in the test environment
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (element) => element,
}));

// Mock the useSearch hook
jest.mock('../hooks/useSearch');

describe('SearchFilter', () => {
  const mockSetSelectedCodes = jest.fn();
  const mockAvailableCodes = [
    { nodeId: '1', code: 'CODE1', text: 'Code One', collection: 'Collection A', id: '1' },
    { nodeId: '2', code: 'CODE2', text: 'Code Two', collection: 'Collection A', id: '2' },
    { nodeId: '3', code: 'CODE3', text: 'Code Three', collection: 'Collection B', id: '3' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    useSearch.mockImplementation(() => ({
      selectedCodes: [],
      setSelectedCodes: mockSetSelectedCodes,
      searchResults: [],
      availableCodes: mockAvailableCodes,
    }));
  });

  test('renders with no codes selected', () => {
    render(<SearchFilter />);
    // The default label should be 'Filter'
    expect(screen.getByText('Filter')).toBeInTheDocument();
    // Check that the FilterIcon has the text-red-400 class when no codes are selected
    const filterButton = screen.getByRole('button');
    const filterIcon = filterButton.querySelector('svg');
    expect(filterIcon).toHaveClass('text-red-400');
  });

  test('shows loading state when no available codes', () => {
    useSearch.mockImplementation(() => ({
      selectedCodes: [],
      setSelectedCodes: mockSetSelectedCodes,
      searchResults: [],
      availableCodes: [],
    }));

    render(<SearchFilter />);
    // The loading label should be 'Loading...'
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  describe('toggles individual code selection', () => {
    test('when code is not selected, selects it', async () => {
      const user = userEvent.setup();
      // Start with explicitly no codes selected
      useSearch.mockImplementation(() => ({
        selectedCodes: [],
        setSelectedCodes: mockSetSelectedCodes,
        searchResults: [],
        availableCodes: mockAvailableCodes,
      }));

      render(<SearchFilter />);
      await user.click(screen.getByRole('button'));
      await user.click(screen.getByText('Code One'));

      expect(mockSetSelectedCodes).toHaveBeenCalledWith(['CODE1']);
    });

    test('when code is selected, unselects it', async () => {
      const user = userEvent.setup();
      // Start with CODE1 already selected
      useSearch.mockImplementation(() => ({
        selectedCodes: ['CODE1'],
        setSelectedCodes: mockSetSelectedCodes,
        searchResults: [],
        availableCodes: mockAvailableCodes,
      }));

      render(<SearchFilter />);
      await user.click(screen.getByRole('button'));
      await user.click(screen.getByText('Code One'));

      expect(mockSetSelectedCodes).toHaveBeenCalledWith([]);
    });
  });

  test('shows search result counts', async () => {
    const user = userEvent.setup();
    useSearch.mockImplementation(() => ({
      selectedCodes: ['CODE1'],
      setSelectedCodes: mockSetSelectedCodes,
      searchResults: [
        { node: { code: 'CODE1' } },
        { node: { code: 'CODE1' } },
      ],
      availableCodes: mockAvailableCodes,
    }));

    render(<SearchFilter />);
    await user.click(screen.getByRole('button'));

    const matchCount = screen.getByText('2');
    expect(matchCount).toBeInTheDocument();
  });

  describe('toggleSelectAll', () => {
    test('when no codes are selected, selects all codes', async () => {
      const user = userEvent.setup();
      // Start with no codes selected
      useSearch.mockImplementation(() => ({
        selectedCodes: [],
        setSelectedCodes: mockSetSelectedCodes,
        searchResults: [],
        availableCodes: mockAvailableCodes,
      }));

      render(<SearchFilter />);
      await user.click(screen.getByRole('button'));
      await user.click(screen.getByText('Select All'));

      // Should set all codes array to indicate all selected
      expect(mockSetSelectedCodes).toHaveBeenCalledWith(['CODE1', 'CODE2', 'CODE3']);
    });

    test('when some codes are selected, selects all codes', async () => {
      const user = userEvent.setup();
      // Start with some codes selected
      useSearch.mockImplementation(() => ({
        selectedCodes: ['CODE1', 'CODE2'],
        setSelectedCodes: mockSetSelectedCodes,
        searchResults: [],
        availableCodes: mockAvailableCodes,
      }));

      render(<SearchFilter />);
      await user.click(screen.getByRole('button'));
      await user.click(screen.getByText('Select All'));

      // Should set all codes array to indicate all selected
      expect(mockSetSelectedCodes).toHaveBeenCalledWith(['CODE1', 'CODE2', 'CODE3']);
    });

    test('when all codes are selected, unselects all codes', async () => {
      const user = userEvent.setup();
      // Start with all codes selected
      useSearch.mockImplementation(() => ({
        selectedCodes: ['CODE1', 'CODE2', 'CODE3'],
        setSelectedCodes: mockSetSelectedCodes,
        searchResults: [],
        availableCodes: mockAvailableCodes,
      }));

      render(<SearchFilter />);
      await user.click(screen.getByRole('button'));
      await user.click(screen.getByText('Select All'));

      // Should set empty array to indicate none selected
      expect(mockSetSelectedCodes).toHaveBeenCalledWith([]);
    });
  });
});
