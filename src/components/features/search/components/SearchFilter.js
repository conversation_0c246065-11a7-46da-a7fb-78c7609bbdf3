import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Filter as FilterIcon, ChevronDown as ChevronDownIcon, Check as CheckIcon } from 'lucide-react';
import { createPortal } from 'react-dom';
import { useSearch } from '../hooks/useSearch';

export const SearchFilter = () => {
  const buttonRef = useRef(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [dropdownRect, setDropdownRect] = useState(null);
  const {
    selectedCodes = [],
    setSelectedCodes,
    searchResults = [],
    availableCodes = []
  } = useSearch();

  // Group codes by collection with better error handling
  const groupedCodes = useMemo(() => {
    if (!Array.isArray(availableCodes) || availableCodes.length === 0) {
      return {};
    }

    return availableCodes.reduce((acc, code) => {
      if (!code) return acc;

      const collection = code.collection || 'uncategorized';
      if (!acc[collection]) {
        acc[collection] = [];
      }

      const codeData = {
        nodeId: code.nodeId,
        code: code.code,
        text: code.text,
        collection: code.collection,
        id: code.id
      };

      acc[collection].push(codeData);
      return acc;
    }, {});
  }, [availableCodes]);

  const isAllSelected = useMemo(() => {
    const allCodes = Object.values(groupedCodes)
      .flatMap(codes => codes.map(code => code.code));

    // Only true if we have codes and every code is selected
    return allCodes.length > 0 && allCodes.every(code => selectedCodes.includes(code));
  }, [groupedCodes, selectedCodes]);

  useEffect(() => {
    // Close the filter dropdown when clicking outside of it
    const handleClickOutside = (event) => {
      if (isFilterOpen) {
        const isButtonClick = buttonRef.current?.contains(event.target);
        const dropdownElement = document.querySelector('.filter-dropdown-portal');
        const isDropdownClick = dropdownElement?.contains(event.target);

        if (!isButtonClick && !isDropdownClick) {
          setIsFilterOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isFilterOpen]);

  useEffect(() => {
    // Update the dropdown position when the filter is open
    if (isFilterOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownRect(rect);
    }
  }, [isFilterOpen]);

  const toggleCode = (code) => {
    setSelectedCodes(selectedCodes.includes(code)
        ? selectedCodes.filter(c => c !== code)  // Remove code if already selected
        : [...selectedCodes, code]              // Add code if not selected
    );
  };

  const toggleSelectAll = () => {
    const allCodes = Object.values(groupedCodes)
      .flatMap(codes => codes.map(code => code.code));

    // If all are selected, clear the selection
    // Otherwise, select all codes
    setSelectedCodes(isAllSelected ? [] : allCodes);
  };

  const getMatchesForCode = useCallback((code) => {
    return searchResults.filter(result => result.node.code === code).length;
  }, [searchResults]);

  return (
    <div className="relative filter-dropdown">
      <button
        ref={buttonRef}
        onClick={() => setIsFilterOpen(!isFilterOpen)}
        className="w-full flex items-center justify-between px-3 py-2 border rounded-md hover:bg-gray-50 text-base"
        style={{ height: '44px' }}
      >
        <div className="flex items-center gap-2 min-w-0">
          <FilterIcon
            size={16}
            className={selectedCodes.length === 0 ? 'text-red-400' : 'active-blue'}
          />
          <span className="text-gray-700 truncate">
            {!availableCodes?.length ? 'Loading...' :
              isAllSelected
                ? `All (${availableCodes.length})`
                : selectedCodes.length > 0
                  ? `${selectedCodes.length}/${availableCodes.length}`
                  : 'Filter'
            }
          </span>
        </div>
        <ChevronDownIcon size={16} className="text-gray-400 flex-shrink-0" />
      </button>

      {isFilterOpen && createPortal(
        <div
          className="fixed z-[9999] bg-white border rounded-md shadow-lg min-w-max filter-dropdown-portal"
          style={{
            top: `${dropdownRect?.bottom + 4}px`,
            right: `${window.innerWidth - (dropdownRect?.right || 0)}px`,
            minWidth: '280px',
            maxWidth: '350px',
            width: 'max-content'
          }}
        >
          <div className="sticky top-0 bg-white border-b z-10">
            <div
              className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer border-b"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                toggleSelectAll();
              }}
            >
              <div className="flex items-center">
                <div className={`w-4 h-4 border rounded mr-2 flex items-center justify-center
                  ${isAllSelected ? 'bg-blue-500 border-blue-500' : 'border-gray-300'}`}
                >
                  {isAllSelected && <CheckIcon size={12} className="text-white" />}
                </div>
                <span className="text-sm text-gray-700">Select All</span>
              </div>
            </div>
          </div>

          <div className="max-h-[calc(100vh-200px)] overflow-auto">

            {Object.entries(groupedCodes).length === 0 ? (
              <div className="px-3 py-2 text-sm text-gray-500">
                No codes available
              </div>
            ) : (
              Object.entries(groupedCodes).map(([collection, codes]) => (
                <div key={collection} className="border-b last:border-b-0">
                  <div className="px-3 py-2 bg-gray-50 font-medium text-sm text-gray-700">
                    {collection.toUpperCase()}
                  </div>
                  {codes.map(node => {
                    const matchCount = getMatchesForCode(node.code);

                    return (
                      <div
                        key={node.nodeId}
                        className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer"
                        onClick={() => toggleCode(node.code)}
                      >
                        <div className="flex items-center gap-2">
                          <div className={`w-4 h-4 border rounded flex items-center justify-center
                            ${selectedCodes.includes(node.code) ? 'bg-blue-500 border-blue-500' : 'border-gray-300'}`}
                          >
                            {selectedCodes.includes(node.code) && <CheckIcon size={12} className="text-white" />}
                          </div>
                          <span className="text-sm text-gray-700">{node.text}</span>
                        </div>
                        {matchCount > 0 && (
                          <span className="ml-2 px-2 py-0.5 text-xs text-white bg-gray-400 rounded-full">
                            {matchCount}
                          </span>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};
