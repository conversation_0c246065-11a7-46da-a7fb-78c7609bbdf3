import React, { useCallback, useRef, useEffect } from 'react';
import { Search as SearchIcon, X as XIcon } from 'lucide-react';
import { SearchResultsList } from '@/components/features/search';
import { useTreeState } from '@/components/features/navtree';
import { useLayout } from '@/app/LayoutContext';

export function SearchPanel({ isOpen, onClose }) {
  const { selectNode } = useTreeState();
  const { isMobile } = useLayout();
  const searchInputRef = useRef(null);

  // Auto-focus search input when panel opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      // Small delay to allow panel animation to complete
      const timeoutId = setTimeout(() => {
        searchInputRef.current?.focus();
      }, 350); // Slightly longer than the 300ms panel animation

      return () => clearTimeout(timeoutId);
    }
  }, [isOpen]);

  const handleSearchResultSelect = useCallback((node) => {
    if (!node) return;
    selectNode(node.nodeId);
    if (isMobile) {
      onClose();
    }
  }, [selectNode, onClose, isMobile]);

  return (
    <div className={`fixed top-[60px] bg-white shadow-lg z-[9998] transition-transform duration-300 ease-in-out ${
      isMobile
        ? 'left-0 w-full h-[calc(100vh-60px)] border-r-0'
        : 'left-0 w-[400px] h-[calc(100vh-60px)] border-r border-gray-300'
    } ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
      {/* Panel Header */}
      <div className="border-b bg-gray-50 flex items-center justify-between px-4 py-3">
        <div className="w-6"></div> {/* Spacer for centering */}
        <div className="flex items-center gap-2">
          <SearchIcon size={16} className="text-gray-600" />
          <h3 className="font-medium text-gray-700">Search</h3>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
          aria-label="Close search"
        >
          <XIcon size={16} className="text-gray-600" />
        </button>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto h-[calc(100%-48px)]">
        <SearchResultsList ref={searchInputRef} onSelect={handleSearchResultSelect} />
      </div>
    </div>
  );
}
