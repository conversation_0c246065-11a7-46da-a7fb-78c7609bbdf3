import React, { memo } from 'react';
import { ChevronRight as ChevronRightIcon } from 'lucide-react';

export const SearchResultCell = memo(({ result, searchQuery, onSelect, isSelected }) => {
  const highlightMatch = (text) => {
    if (!searchQuery || searchQuery.length < 3 || typeof text !== 'string') return text;
    try {
      const escapedQuery = searchQuery.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
      const regex = new RegExp(`(${escapedQuery})`, 'gi');
      const parts = text.split(regex);
      return (
        <span>
          {parts.map((part, i) => {
            const isMatch = part.toLowerCase() === searchQuery.toLowerCase();
            const key = `${isMatch ? 'match' : 'text'}-${part}-${i}`;
            return isMatch ? (
              <span key={key} className="bg-yellow-100 font-medium">{part}</span>
            ) : (
              <span key={key}>{part}</span>
            );
          })}
        </span>
      );
    } catch (error) {
      console.warn('Highlighting failed:', error);
      return text;
    }
  };

  return (
    <div
      className={`search-result p-4 hover:bg-gray-100 cursor-pointer group
        ${isSelected ? 'bg-blue-50' : ''}`}
      onClick={() => onSelect(result)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="text-xs text-gray-500 mb-1">
            {result.node.getHierarchy()
              .filter(n => n.text)
              .map(n => n.text.toUpperCase())
              .join(' > ')}
          </div>
          {result.matches?.map((match, index) => {
            const formattedMatch = match.getFormattedMatch();
            return (
              <div key={index} className="text-md text-gray-900">
                {highlightMatch(formattedMatch)}
              </div>
            );
          })}
        </div>
        <ChevronRightIcon
          size={16}
          className={`mt-1 ${isSelected ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-600'}`}
        />
      </div>
    </div>
  );
});

SearchResultCell.displayName = 'SearchResultCell';
