import React, { createContext, useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';
import { SearchService } from '@/components/features/search/services/SearchService';

export const SearchContext = createContext();

export const SearchProvider = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCodes, setSelectedCodes] = useState([]);

  // Fetch available codes
  const {
    data: availableCodes = [],
    isLoading: isLoadingCodes,
  } = useQuery({
    queryKey: ['available-codes'],
    queryFn: () => StatuteService.getAvailableCodes(),
    staleTime: 0,
    gcTime: 30 * 1000,
  });

  // Search query using SearchService
  const {
    data: searchResults = [],
    isLoading: isSearching,
    error,
    isError
  } = useQuery({
    queryKey: ['statute-search', searchQuery, selectedCodes],
    queryFn: async () => {
      const results = await SearchService.searchNodes(searchQuery, selectedCodes);
      return results;
    },
    enabled: searchQuery.length >= 3 && selectedCodes.length > 0,
    staleTime: 0,
    gcTime: 30 * 1000,
    onError: (error) => {
      console.error('SearchContext: Search error:', {
        error,
        query: searchQuery,
        selectedCodes,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Initialize selected codes when available codes are loaded (only on first load)
  useEffect(() => {
    if (availableCodes.length > 0 && selectedCodes.length === 0) {
      // Only auto-select if this is the initial load (no user interaction yet)
      const hasUserInteracted = localStorage.getItem('search-codes-initialized');
      if (!hasUserInteracted) {
        setSelectedCodes(availableCodes.map(code => code.code));
        localStorage.setItem('search-codes-initialized', 'true');
      }
    }
  }, [availableCodes, selectedCodes.length, setSelectedCodes]);

  const value = {
    searchQuery,
    setSearchQuery,
    searchResults,
    selectedCodes,
    setSelectedCodes,
    availableCodes,
    isSearching: isSearching || isLoadingCodes,
    error: isError ? error : null
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};
