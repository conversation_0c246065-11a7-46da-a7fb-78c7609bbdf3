export class OrderStore {
  static COLLECTION_ORDER_KEY = 'collection_order';
  static CODE_ORDER_KEY = 'code_order';

  constructor() {
    this.collectionOrder = this.loadCollectionOrder();
    this.codeOrder = this.loadCodeOrder();

  }

  loadCollectionOrder() {
    if (typeof window === 'undefined') return {};
    const stored = localStorage.getItem(OrderStore.COLLECTION_ORDER_KEY);
    if (!stored) return {};
    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing collection order from localStorage:', error);
      return {};
    }
  }

  loadCodeOrder() {
    if (typeof window === 'undefined') return {};
    const stored = localStorage.getItem(OrderStore.CODE_ORDER_KEY);
    if (!stored) return {};
    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing code order from localStorage:', error);
      return {};
    }
  }

  saveCollectionOrder(order) {
    if (typeof window === 'undefined') return;
    localStorage.setItem(OrderStore.COLLECTION_ORDER_KEY, JSON.stringify(order));
    this.collectionOrder = order;
  }

  saveCodeOrder(order) {
    if (typeof window === 'undefined') return;
    localStorage.setItem(OrderStore.CODE_ORDER_KEY, JSON.stringify(order));
    this.codeOrder = order;
  }

  getOrderForCollection(collectionId) {
    const order = this.codeOrder[collectionId] || [];
    return order;
  }
}
