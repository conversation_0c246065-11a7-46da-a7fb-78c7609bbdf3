import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { TreeStateContext } from '@/components/features/navtree';
import { addNodeIds, removeNodeIds, toggleNodeId, collectDescendantNodeIds } from '@/components/features/navtree';
import { getParentNodeIds } from '@/components/features/navtree/utils';

export function TreeStateProvider({ children }) {
  const router = useRouter();
  const [selectedNodeId, setSelectedNodeId] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedNodeId = localStorage.getItem('selectedNode');
      if (savedNodeId) return savedNodeId;
    }
    return null;
  });
  const [expandedNodeIds, setExpandedNodeIds] = useState(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedIds = localStorage.getItem('expandedNodeIds');
        if (savedIds) {
          return new Set(JSON.parse(savedIds));
        }
      } catch (error) {
        console.error('Error loading expanded nodes from localStorage:', error);
      }
    }
    return new Set();
  });
  const [loadingNodeIds, setLoadingNodeIds] = useState(new Set());

  const updateExpandedNodes = useCallback((nodeIds) => {
    if (!nodeIds) return;
    setExpandedNodeIds(prev => addNodeIds(prev, nodeIds));
  }, []);

  // Helper to expand all parent nodes of a nodeId
  const expandParentsToNode = useCallback((nodeId) => {
    if (!nodeId) return;
    const parentIds = getParentNodeIds(nodeId);
    updateExpandedNodes(parentIds);
  }, [updateExpandedNodes]);

  // Core selection function - manages tree state and URL
  const selectNode = useCallback((nodeId) => {
    if (!nodeId) return;

    // Update selected node
    setSelectedNodeId(nodeId);
    localStorage.setItem('selectedNode', nodeId);

    // Update URL without causing a page reload
    router.replace(nodeId, { scroll: false });

    // Expand all parent nodes
    expandParentsToNode(nodeId);
  }, [router, expandParentsToNode]);

  // Scroll to node function - handles just the scrolling
  const scrollToNode = useCallback((nodeId) => {
    if (!nodeId) return;

    // Find the element first
    const element = document.querySelector(`[data-node-id="${nodeId}"]`);
    if (!element) {
      return;
    }

    // Just use scrollIntoView - the simplest approach
    element.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center' 
    });
  }, []);

  // Handle tree node expansion
  const toggleNode = useCallback((nodeId) => {
    setExpandedNodeIds(prev => toggleNodeId(prev, nodeId));
  }, []);

  // Then update both expandAllBelow and collapseAllBelow
  const expandAllBelow = useCallback(async (node) => {
    if (!node?.nodeId) return;

    setLoadingNodeIds(prev => addNodeIds(prev, `expand-${node.nodeId}`));

    try {
      await new Promise(resolve => setTimeout(resolve, 0)); // Let UI update
      const nodesToExpand = collectDescendantNodeIds(node);
      setExpandedNodeIds(prev => addNodeIds(prev, nodesToExpand));
    } finally {
      setLoadingNodeIds(prev => removeNodeIds(prev, `expand-${node.nodeId}`));
    }
  }, []);

  const collapseAllBelow = useCallback(async (node) => {
    if (!node?.nodeId) return;

    setLoadingNodeIds(prev => addNodeIds(prev, `collapse-${node.nodeId}`));

    try {
      await new Promise(resolve => setTimeout(resolve, 0)); // Let UI update
      const nodesToCollapse = collectDescendantNodeIds(node);
      setExpandedNodeIds(prev => removeNodeIds(prev, nodesToCollapse));
    } finally {
      setLoadingNodeIds(prev => removeNodeIds(prev, `collapse-${node.nodeId}`));
    }
  }, []);

  const isNodeExpanded = useCallback((nodeId) => {
    return expandedNodeIds.has(nodeId);
  }, [expandedNodeIds]);

  // Save to localStorage whenever expandedNodeIds changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const serializedIds = Array.from(expandedNodeIds);
      localStorage.setItem('expandedNodeIds', JSON.stringify(serializedIds));
    }
  }, [expandedNodeIds]);

  const value = {
    selectedNodeId,
    setSelectedNodeId,
    expandedNodeIds,
    setExpandedNodeIds,
    loadingNodes: loadingNodeIds,
    setLoadingNodeIds,
    selectNode,
    scrollToNode,
    toggleNode,
    expandAllBelow,
    collapseAllBelow,
    updateExpandedNodes,
    isNodeExpanded,
    expandParentsToNode
  };

  return (
    <TreeStateContext.Provider value={value}>
      {children}
    </TreeStateContext.Provider>
  );
}
