import { createContext, useContext, useState, useCallback, useMemo } from 'react';
import { OrderStore } from '@/components/features/navtree';

const OrderStateContext = createContext(null);

export function OrderStateProvider({ children }) {
  const [orderStore] = useState(() => new OrderStore());
  const [codeOrder, setCodeOrder] = useState(orderStore.codeOrder);
  const [collectionOrder] = useState(orderStore.collectionOrder);

  const updateCodeOrder = useCallback((collectionId, orderedIds) => {

    const newCodeOrder = {
      ...codeOrder,
      [collectionId]: orderedIds
    };

    setCodeOrder(newCodeOrder);
    orderStore.saveCodeOrder(newCodeOrder);
  }, [orderStore, codeOrder]);

  const value = useMemo(() => ({
    codeOrder,
    collectionOrder,
    updateCodeOrder
  }), [codeOrder, collectionOrder, updateCodeOrder]);

  return (
    <OrderStateContext.Provider value={value}>
      {children}
    </OrderStateContext.Provider>
  );
}

export const useOrderState = () => {
  const context = useContext(OrderStateContext);
  if (!context) {
    throw new Error('useOrderState must be used within OrderStateProvider');
  }
  return context;
};
