'use client';

import { useQuery } from '@tanstack/react-query';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';

export function useNavTree() {
  const {
    data: navData,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['nav-tree'],
    queryFn: () => StatuteService.getNavTree(),
    staleTime: 5 * 60 * 1000, // 5 minutes - prevent frequent refetching
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in memory
    retry: false, // Disable retry to prevent loops
    refetchOnWindowFocus: false, // Prevent refetch on focus
    refetchOnMount: false, // Prevent refetch on mount if cached
    onError: (error) => {
      console.error('Navigation tree query error:', error);
    }
  });

  return {
    nodes: navData,
    loading: isLoading,
    error: isError ? error : null
  };
}
