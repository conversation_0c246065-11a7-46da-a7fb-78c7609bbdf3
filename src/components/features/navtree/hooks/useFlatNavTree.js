'use client';

import { useQuery } from '@tanstack/react-query';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';

/**
 * Hook for fetching filtered navigation tree optimized for navigation operations.
 * Returns pre-sorted array of navigation nodes filtered by the current node's code and type.
 * 
 * @param {string} nodeId - Current node ID to determine filtering context
 * @returns {Object} Query result with filtered nodes array, loading state, and error
 */
export function useFlatNavTree(nodeId = null) {
  // Normalize the nodeId for stable cache keys
  const normalizedNodeId = nodeId?.trim() || null;
  
  // Skip API call for root node - it doesn't need navigation context
  const shouldFetch = Boolean(normalizedNodeId && normalizedNodeId !== '/');
  
  const {
    data: flatNodes,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['flat-nav-tree', normalizedNodeId],
    queryFn: () => StatuteService.getFlatNavTree(normalizedNodeId),
    staleTime: 5 * 60 * 1000, // 5 minutes - prevent frequent refetching
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in memory
    enabled: shouldFetch, // Only enable for non-root nodes
    retry: false, // Disable retry to prevent loops
    refetchOnWindowFocus: false, // Prevent refetch on focus
    refetchOnMount: false, // Prevent refetch on mount if cached
    onError: (error) => {
      console.error('Flat nav tree query error:', error);
    }
  });

  return {
    flatNodes: flatNodes || [],
    loading: isLoading,
    error: isError ? error : null
  };
}