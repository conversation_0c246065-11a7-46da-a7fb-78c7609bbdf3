import { memo } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { BookmarkButton } from '@/components/features/bookmarks';

const NodeControls = memo(({ node, isExpanded, onToggle }) => {
  const hasChildren = node.hasChildren();
  const showExpandControls = hasChildren && !node.isSection() && !node.isSubsection();

  return (
    <>
      <div className="flex items-center gap-1 flex-shrink-0" data-ignore-drag="true" style={{ marginTop: '-8px' }}>
        {showExpandControls && (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onToggle(node.nodeId);
            }}
            className="p-1.5 rounded text-gray-500 min-w-[36px] min-h-[36px] flex items-center justify-center flex-shrink-0"
            title={isExpanded ? 'Collapse' : 'Expand'}
          >
            {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
        )}
        <BookmarkButton node={node} />
      </div>
      <span className="text-sm leading-tight">{node.text}</span>
    </>
  );
});

NodeControls.displayName = 'NodeControls';

export { NodeControls };
