'use client';

import { memo, useRef } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToParentElement
} from '@dnd-kit/modifiers';
import { SortableNavTreeNode } from './SortableNavTreeNode';
import { useOrderState } from '../context/OrderStateProvider';
import { useTreeState } from '../context/TreeStateContext';
import { useNavTree } from '../hooks/useNavTree';
import { arrayMove } from '@dnd-kit/sortable';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// Helper function to find a node by ID in the tree
const findNodeById = (nodes, id) => {
  if (!nodes) return null;

  for (const node of nodes) {
    if (node.nodeId === id) return node;
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// Helper function to find the parent node and its children array
const findParentWithChildren = (nodes, childId) => {
  if (!nodes) return null;

  for (const node of nodes) {
    // Check if this node has the child we're looking for
    if (node.children?.some(child => child.nodeId === childId)) {
      return {
        parent: node,
        children: node.children
      };
    }
    // If not, recursively check this node's children
    if (node.children) {
      const found = findParentWithChildren(node.children, childId);
      if (found) return found;
    }
  }
  return null;
};

export const SortableNavTreeList = memo(({ onSelect }) => {
  const { nodes, loading, error } = useNavTree();
  const { updateCodeOrder } = useOrderState();
  const { toggleNode, isNodeExpanded } = useTreeState();
  const draggedNodeRef = useRef(null);
  const wasExpandedRef = useRef(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  if (loading) {
    return <LoadingSpinner message="Loading Table of Contents..." spinnerPosition="left" />;
  }
  if (error) {
    return <div>Error: {error.message || 'Failed to load Table of Contents'}</div>;
  }
  if (!nodes || !nodes[0]) {
    return <div>No content available</div>;
  }

  const rootNode = nodes[0];

  const handleDragStart = (event) => {
    const { id } = event.active;

    const node = findNodeById([rootNode], id);

    if (!node) {
      console.error('Could not find node for drag start:', id);
      return;
    }

    draggedNodeRef.current = node;
    wasExpandedRef.current = isNodeExpanded(node.nodeId);

    if (wasExpandedRef.current) {
      toggleNode(node.nodeId);
    }
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (wasExpandedRef.current && draggedNodeRef.current) {
      toggleNode(draggedNodeRef.current.nodeId);
    }

    if (!over || active.id === over.id) {
      return;
    }

    // Find the parent node and its children array for the dragged node
    const sourceParent = findParentWithChildren([rootNode], active.id);
    const targetParent = findParentWithChildren([rootNode], over.id);

    // Only allow reordering within the same parent
    if (!sourceParent || !targetParent || sourceParent.parent.nodeId !== targetParent.parent.nodeId) {
      return;
    }

    const { parent, children } = sourceParent;
    const oldIndex = children.findIndex(node => node.nodeId === active.id);
    const newIndex = children.findIndex(node => node.nodeId === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      console.error('Could not find indices for drag end:', { active, over });
      return;
    }

    const reorderedNodes = arrayMove(children, oldIndex, newIndex);
    const orderedIds = reorderedNodes.map(node => node.nodeId);

    updateCodeOrder(parent.nodeId, orderedIds);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <div className="sortable-tree-node-list" style={{ position: 'relative' }}>
        <SortableContext
          items={[rootNode.nodeId]}
          strategy={verticalListSortingStrategy}
        >
          <SortableNavTreeNode
            key={rootNode.nodeId}
            node={rootNode}
            level={0}
            onSelect={onSelect}
          />
        </SortableContext>
      </div>
    </DndContext>
  );
});

SortableNavTreeList.displayName = 'SortableNavTreeList';
