/* Base styles and typography */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: hsl(204, 69.9%, 53.1%); /*#3498db*/
  --selected-color: hsl(204, 69.9%, 53.1%); /* Stronger blue for better contrast */
  --hover-color: hsl(204, 69.9%, 83.1%); /* Even lighter blue for hover */
  --background-color: #ffffff;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --section-spacing: 2rem;
  --subsection-spacing: 1rem;
}

.selected-index-item {
  background-color: var(--selected-color) !important;
  color: var(--background-color) !important;
  font-weight: 600;
  border-left: 4px solid hsl(204, 69.9%, 43.1%);
  margin-left: -4px;
}

.selected-index-item:hover {
  background-color: hsl(204, 69.9%, 48.1%) !important;
  color: var(--background-color) !important;
}

.highlighted-section {
  background-color: var(--hover-color);
  transition: background-color 0.5s ease;
}

.statute-nav-container {
  scroll-behavior: smooth;
}

.tree-node {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.tree-node-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  /* gap: 0.25rem; */
}

/* Align NodeControl items to top */
.relative.flex.items-center {
  align-items: flex-start;
}

.tree-node:hover {
  background-color: #f3f4f6;
}

/* ============================== */
/* Sortable Tree Node */
/* ============================== */

.sortable-tree-node {
  position: relative;
  transition: transform 200ms cubic-bezier(0.2, 0, 0, 1);
}

.sortable-tree-node.is-dragging {
  z-index: 1;
  background-color: var(--background-color);
  box-shadow: 0 0 0 1px var(--border-color),
              0 2px 4px rgba(0,0,0,0.1),
              0 4px 8px rgba(0,0,0,0.1);
  border-radius: 4px;
}

.sortable-tree-node-list {
  position: relative;
}

.sortable-tree-node-list::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--accent-color);
  opacity: 0;
  transition: opacity 200ms ease;
}

.sortable-tree-node-list.is-over::before {
  opacity: 1;
}
