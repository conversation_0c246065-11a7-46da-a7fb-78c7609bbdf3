import React, { useEffect, useState, useCallback } from 'react';
import { Locate as LocateIcon, X as XIcon } from 'lucide-react';
import { useTreeState } from '../context/TreeStateContext';
import { useNavTree } from '../hooks/useNavTree';
import '@/components/StatuteBrowser.css';
import './FindSelectedSectionButton.css';

// Helper to check if an element is fully visible in a container
function isElementFullyVisible(element, container) {
  if (!element || !container) return true;
  const elementRect = element.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  return (
    elementRect.top >= containerRect.top &&
    elementRect.bottom <= containerRect.bottom
  );
}

// Helper to find a node by ID in the tree (recursive)
function findNodeById(nodes, id) {
  if (!nodes) return null;
  for (const node of nodes) {
    if (node.nodeId === id) return node;
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
}

// Helper to capitalize the first letter
function capitalize(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

const FindSelectedSectionButton = () => {
  const { selectedNodeId, scrollToNode, expandParentsToNode, expandedNodeIds } = useTreeState();
  const { nodes, loading } = useNavTree();
  const [showButton, setShowButton] = useState(false);
  const [manuallyClosed, setManuallyClosed] = useState(false);

  // Find the selected node object
  const selectedNode = selectedNodeId && nodes ? findNodeById(nodes, selectedNodeId) : null;
  const nodeTypeLabel = selectedNode ? capitalize(selectedNode.type) : 'Section';
  const buttonText = `Find selected ${nodeTypeLabel}`;

  // Centralized visibility check function
  const checkVisibility = useCallback(() => {
    if (loading || !selectedNodeId || selectedNodeId === '/') {
      setShowButton(false);
      return null;
    }

    // Look specifically for the node in the TOC navigation tree
    const tocElement = document.querySelector('.left-panel-content');
    const nodeElement = tocElement?.querySelector(`[data-node-id="${selectedNodeId}"]`);

    if (!nodeElement || !tocElement) {
      setShowButton(true);
      return null;
    }

    // Check visibility and update button state
    const isVisible = isElementFullyVisible(nodeElement, tocElement);
    setShowButton(!isVisible);

    return { nodeElement, tocElement };
  }, [loading, selectedNodeId]);

  // Watch for selected node visibility, but only after navtree is loaded
  useEffect(() => {
    const elements = checkVisibility();
    if (!elements) return;

    const { nodeElement, tocElement } = elements;

    // Set up observer to auto-hide/show button as user scrolls/collapses/expands
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        // Check if element is fully visible, not just intersecting
        const isFullyVisible = entry.isIntersecting && entry.intersectionRatio >= 0.99;
        setShowButton(!isFullyVisible);
      },
      { root: tocElement, threshold: [0, 0.1, 0.5, 0.9, 1.0] }
    );
    observer.observe(nodeElement);

    // Add scroll listener as backup to catch visibility changes
    const handleScroll = () => {
      const isVisible = isElementFullyVisible(nodeElement, tocElement);
      setShowButton(!isVisible);
    };

    // Listen on the document with capture to catch all scroll events
    const scrollHandler = (e) => {
      // If this is our target element, run the visibility check
      if (e.target?.classList?.contains('left-panel-content')) {
        handleScroll();
      }
    };

    document.addEventListener('scroll', scrollHandler, true);

    return () => {
      observer.disconnect();
      document.removeEventListener('scroll', scrollHandler, true);
    };
  }, [selectedNodeId, loading, nodes, expandedNodeIds, checkVisibility]);

  // Reset manuallyClosed when the selected node changes
  useEffect(() => {
    setManuallyClosed(false);
  }, [selectedNodeId]);

  const handleClick = useCallback((e) => {
    e.preventDefault();

    if (selectedNodeId && expandParentsToNode && scrollToNode) {
      // First expand parents
      expandParentsToNode(selectedNodeId);

      // Wait for expansion, then scroll
      setTimeout(() => {
        scrollToNode(selectedNodeId);

        // Wait for scroll to complete, then re-check visibility
        setTimeout(() => {
          checkVisibility();
        }, 500); // Allow time for smooth scroll to complete
      }, 300); // Give time for expansion
    }
  }, [selectedNodeId, expandParentsToNode, scrollToNode, checkVisibility]);

  if (!showButton || manuallyClosed) return null;

  return (
    <div className="find-selected-section-btn-container" aria-live="polite">
      <button
        className={`find-selected-section-btn visible`}
        onClick={handleClick}
        tabIndex={0}
        aria-label={buttonText}
        type="button"
        style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
      >
        <LocateIcon size={18} className="find-selected-section-icon" aria-hidden="true" />
        <span className="find-selected-section-text">{buttonText}</span>
        <span
          className="find-selected-section-close ml-2 flex items-center rounded hover:bg-gray-200 cursor-pointer"
          title="Hide this button"
          tabIndex={0}
          role="button"
          aria-label="Close"
          onClick={e => {
            e.stopPropagation();
            setManuallyClosed(true);
          }}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.stopPropagation();
              setManuallyClosed(true);
            }
          }}
        >
          <XIcon size={16} className="text-gray-600" />
        </span>
      </button>
    </div>
  );
};

export default FindSelectedSectionButton;
