.find-selected-section-btn-container {
  position: absolute;
  top: 72px;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: center;
  pointer-events: none;
}

.find-selected-section-btn {
  pointer-events: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  color: var(--accent-color);
  font-weight: 500;
  border-radius: 9999px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12), 0 1.5px 4px rgba(0,0,0,0.08);
  padding: 0.5rem 1.25rem;
  border: none;
  cursor: pointer;
  opacity: 0;
  transform: translateY(-24px) scale(0.98);
  transition: opacity 0.3s cubic-bezier(.4,0,.2,1), transform 0.3s cubic-bezier(.4,0,.2,1);
  outline: none;
}

.find-selected-section-btn.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.find-selected-section-btn:focus {
  box-shadow: 0 0 0 3px #93c5fd, 0 4px 16px rgba(0,0,0,0.12);
}

.find-selected-section-icon {
  color: var(--accent-color);
}

@media (max-width: 767px) {
  .find-selected-section-btn {
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }
}
