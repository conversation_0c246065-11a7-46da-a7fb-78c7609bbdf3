'use client';

import { useCallback, memo } from 'react';
import { NodeControls } from '@/components/features/navtree';
import { useTreeState } from '@/components/features/navtree/context/TreeStateContext';
import './NavTree.css';

export const NavTreeNode = memo(({ node, level = 0, onSelect }) => {
  const { selectedNodeId, toggleNode, isNodeExpanded } = useTreeState();

  const handleNodeClick = useCallback(() => {
    if (onSelect) {
      onSelect(node);
    }
  }, [node, onSelect]);

  // For non-root nodes, use existing expanded state check
  const isExpanded = isNodeExpanded(node.nodeId) || node.isRoot();
  const isSelected = selectedNodeId === node.nodeId;
  const hasChildren = node.children.length > 0;

  return (
    <div className="w-full">
      <div
        className={`group hover:bg-blue-50 text-gray-800 min-h-[44px] cursor-pointer pr-4
          ${isSelected ? 'selected-index-item' : ''}
          ${hasChildren ? 'font-medium' : 'font-normal'}`}
        data-node-id={node.nodeId}
        onClick={handleNodeClick}
      >
        <div
          className="flex gap-2 items-start py-2"
          style={{
            paddingLeft: node.isSection()
              ? `${(2*level)+2}rem`
              : `${(2*level)}rem`
          }}
        >
          <NodeControls
            node={node}
            isExpanded={isExpanded}
            onToggle={toggleNode}
          />
        </div>
      </div>
    </div>
  );
});

NavTreeNode.displayName = 'NavTreeNode';
