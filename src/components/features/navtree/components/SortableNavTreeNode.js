import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical } from 'lucide-react';
import { NavTreeNode } from './NavTreeNode';
import { useTreeState } from '../context/TreeStateContext';
import { useOrderState } from '../context/OrderStateProvider';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import React, { memo } from 'react';

const sortNodesByOrder = (nodes, orderedIds) => {
  // If no specific order is defined, sort by the node's order property
  if (!orderedIds?.length) {
    return [...nodes].sort((a, b) => {
      const orderA = a.order || 0;
      const orderB = b.order || 0;
      return orderA - orderB;
    });
  }

  const nodeMap = new Map(nodes.map(node => [node.nodeId, node]));
  const sortedNodes = [];

  // First add all nodes that have an explicit order in orderedIds
  // This is used for manually reordered collections and codes
  orderedIds.forEach(id => {
    if (nodeMap.has(id)) {
      sortedNodes.push(nodeMap.get(id));
      nodeMap.delete(id);
    }
  });

  // Then add any remaining nodes (not in the order) sorted by their order property
  const remainingNodes = Array.from(nodeMap.values()).sort((a, b) => {
    const orderA = a.order || 0;
    const orderB = b.order || 0;
    // If orders are the same, sort by nodeId for consistency
    return orderA - orderB || String(a.nodeId).localeCompare(String(b.nodeId));
  });

  return [...sortedNodes, ...remainingNodes];
};

const SortableNavTreeNodeComponent = ({ node, level, onSelect }) => {
  const { isNodeExpanded } = useTreeState();
  const { codeOrder } = useOrderState();

  // Only allow dragging for collection and code nodes
  const isDraggable = node?.type === 'collection' || node?.type === 'code';

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: node?.nodeId || 'invalid-node',
    data: {
      type: node?.type,
      nodeId: node?.nodeId,
      parentId: node?.parent?.nodeId
    },
    disabled: !isDraggable // Disable drag for non-draggable nodes
  });

  if (!node?.nodeId) {
    console.error('Invalid node passed to SortableNavTreeNode:', node);
    return null;
  }

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const modifiedListeners = isDraggable ? {
    ...listeners,
    onPointerDown: (e) => {
      const target = e.target;
      if (target.closest('[data-ignore-drag="true"]')) {
        return;
      }
      listeners.onPointerDown(e);
    }
  } : {};

  // Get children if node is expanded
  const isExpanded = isNodeExpanded(node.nodeId);
  const children = isExpanded ? node.children || [] : [];

  // Sort children according to stored order
  const orderedChildren = sortNodesByOrder(children, codeOrder[node.nodeId]);
  const childIds = orderedChildren.map(child => child.nodeId);

  return (
    <>
      <div
        ref={setNodeRef}
        style={style}
        className={`sortable-tree-node group ${isDragging ? 'is-dragging' : ''}`}
        data-node-id={node.nodeId}
        data-parent-id={node.parent?.nodeId}
        {...(isDraggable ? attributes : {})}
        {...modifiedListeners}
      >
        <div className="flex items-start">
          {isDraggable && (
            <div
              className="opacity-0 group-hover:opacity-100 transition-opacity px-1 mt-2.5"
              title="Drag to reorder"
            >
              <GripVertical size={14} className="text-gray-400" />
            </div>
          )}
          <div className={`flex-1 ${!isDraggable ? 'pl-6' : ''}`}>
            <NavTreeNode
              node={node}
              level={level}
              onSelect={onSelect}
            />
          </div>
        </div>
      </div>
      {/* Render children if expanded */}
      {orderedChildren.length > 0 && (
        <SortableContext items={childIds} strategy={verticalListSortingStrategy}>
          {orderedChildren.map(child => (
            <SortableNavTreeNode
              key={child.nodeId}
              node={child}
              level={level + 1}
              onSelect={onSelect}
            />
          ))}
        </SortableContext>
      )}
    </>
  );
};

export const SortableNavTreeNode = memo(SortableNavTreeNodeComponent);
