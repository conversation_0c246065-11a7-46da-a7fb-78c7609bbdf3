
// Get parent nodeIds from a nodeId path
export const getParentNodeIds = (nodeId) => {
  if (!nodeId) return [];

  const tokens = nodeId.split('/').filter(Boolean);

  let results = [];
  while (tokens.length > 0) {
    results.push('/'+tokens.join('/'));
    tokens.pop();
    tokens.pop();
  }

  return results;
};

// Pure utility functions for set operations on nodeIds
export const addNodeIds = (prevIds, newIds) => {
  const existingIds = new Set(prevIds);
  const idsToAdd = Array.isArray(newIds) ? newIds : [newIds];
  return new Set([...existingIds, ...idsToAdd]);
};

export const removeNodeIds = (prevIds, idsToRemove) => {
  const newIds = new Set(prevIds);
  const idsToDelete = Array.isArray(idsToRemove) ? idsToRemove : [idsToRemove];
  idsToDelete.forEach(id => newIds.delete(id));
  return newIds;
};

export const toggleNodeId = (prevIds, nodeId) => {
  if (prevIds.has(nodeId)) {
    return removeNodeIds(prevIds, nodeId);
  } else {
    return addNodeIds(prevIds, nodeId);
  }
};

export const collectDescendantNodeIds = (node) => {
  const nodeIds = new Set();
  if (!node?.nodeId) return [];

  // Don't add the root node itself, only its descendants that are not subsections or sections
  node.children?.forEach(child => {
    if (child.isSubsection() || child.isSection()) return;
    nodeIds.add(child.nodeId);
    const childIds = collectDescendantNodeIds(child);
    childIds.forEach(id => nodeIds.add(id));
  });

  return Array.from(nodeIds);
};

// Add these utility functions
export const sortNodesByOrder = (nodes, orderedIds) => {
  if (!orderedIds?.length) return nodes;

  const nodeMap = new Map(nodes.map(node => [node.nodeId, node]));
  const sortedNodes = [];

  // First add all nodes that have an order
  orderedIds.forEach(id => {
    if (nodeMap.has(id)) {
      sortedNodes.push(nodeMap.get(id));
      nodeMap.delete(id);
    }
  });

  // Then add any remaining nodes (not in the order)
  nodeMap.forEach(node => sortedNodes.push(node));

  return sortedNodes;
};
