'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

// Hook now accepts contentRef parameter for dynamic scrolling
export function useBreadcrumbScroll({ content, contentRef }) {
  const [visibleNodes, setVisibleNodes] = useState([]);

  // Static breadcrumb implementation (only when no contentRef is provided)
  useEffect(() => {
    // Only use static implementation if we don't have a contentRef for dynamic scrolling
    if (contentRef?.current || !content) return;

    // Extract hierarchy from the content node
    let hierarchyNodes = [];

    if (content.getHierarchy && typeof content.getHierarchy === 'function') {
      // If content has a getHierarchy method, use it
      hierarchyNodes = content.getHierarchy();
    } else if (Array.isArray(content._hierarchy)) {
      // If content has a _hierarchy array, use it
      hierarchyNodes = content._hierarchy;
    } else if (content.hierarchy && Array.isArray(content.hierarchy)) {
      // Some nodes might have a direct hierarchy property
      hierarchyNodes = content.hierarchy;
    }

    // Sort directly by nodeId string comparison since they're hierarchical
    const sortedNodes = [...hierarchyNodes].sort((a, b) => {
      return a.nodeId.localeCompare(b.nodeId);
    });

    setVisibleNodes(sortedNodes);
  }, [content, contentRef]);

  // Helper function to find a node in the content by nodeId
  const findNodeInContent = useCallback((nodeId) => {
    if (!content) return null;
    
    const searchInNode = (node) => {
      if (node.nodeId === nodeId) return node;
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          const found = searchInNode(child);
          if (found) return found;
        }
      }
      return null;
    };
    
    return searchInNode(content);
  }, [content]);

  // Dynamic scrolling implementation
  // Note: Monitor performance during heavy scrolling - may need throttling if issues arise
  const handleScroll = useCallback(() => {
    if (!contentRef?.current || !content) return;
    
    const elements = contentRef.current.querySelectorAll('*[data-node-id]');
    const containerTop = contentRef.current.getBoundingClientRect().top;
    
    // Filter to only allowed content types for breadcrumb (exclude section and content types)
    const allowedTypes = ['collection', 'code', 'title', 'subtitle', 'chapter', 'subchapter', 'article', 'subarticle'];
    
    // Get the base hierarchy from the selected node
    let baseHierarchy = [];
    if (content.getHierarchy && typeof content.getHierarchy === 'function') {
      baseHierarchy = content.getHierarchy();
    } else if (Array.isArray(content._hierarchy)) {
      baseHierarchy = content._hierarchy;
    } else if (content.hierarchy && Array.isArray(content.hierarchy)) {
      baseHierarchy = content.hierarchy;
    }
    
    // Filter base hierarchy to allowed types and create a map by type
    const hierarchyByType = new Map();
    baseHierarchy
      .filter(node => allowedTypes.includes(node.type?.toLowerCase()))
      .forEach(node => {
        hierarchyByType.set(node.type.toLowerCase(), node);
      });
    
    // Find all nodes that have scrolled off the screen (top edge above viewport)
    const scrolledOffByType = new Map();
    
    for (const element of elements) {
      const rect = element.getBoundingClientRect();
      const nodeId = element.getAttribute('data-node-id');
      const node = findNodeInContent(nodeId);
      
      if (!node || !allowedTypes.includes(node.type?.toLowerCase())) continue;
      
      // If the top edge is above the container top, this node has scrolled off
      if (rect.top < containerTop) {
        // Keep the most recently scrolled off node of each type
        scrolledOffByType.set(node.type.toLowerCase(), node);
      }
    }
    
    // Build final hierarchy: start with base, then add/replace with scrolled off nodes
    const finalHierarchyByType = new Map(hierarchyByType);
    
    // Add or replace nodes that have scrolled off
    scrolledOffByType.forEach((node, type) => {
      finalHierarchyByType.set(type, node);
    });
    
    // Convert back to array and sort by nodeId for proper hierarchical order
    const finalHierarchy = Array.from(finalHierarchyByType.values());
    const sortedNodes = finalHierarchy.sort((a, b) => a.nodeId.localeCompare(b.nodeId));
    
    setVisibleNodes(sortedNodes);
  }, [content, contentRef, findNodeInContent]);

  // Use ref to avoid handleScroll dependency issues
  const handleScrollRef = useRef(handleScroll);
  handleScrollRef.current = handleScroll;

  useEffect(() => {
    const container = contentRef?.current;
    if (!container || !content) return;

    const scrollHandler = (event) => handleScrollRef.current(event);
    container.addEventListener('scroll', scrollHandler, { passive: true });
    handleScrollRef.current(); // Initial call

    return () => {
      container.removeEventListener('scroll', scrollHandler);
    };
  }, [content, contentRef]); // Remove handleScroll dependency

  return { visibleNodes };
}