import React, { useState, useEffect, useCallback } from 'react';
import { useTreeState } from '@/components/features/navtree';
import './LocateNodeButton.css';

export const LocateNodeButton = ({
  node,
  icon: Icon,
  text,
  className = ''
}) => {
  const { selectNode, scrollToNode } = useTreeState();
  const [isHovered, setIsHovered] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  const handleClick = useCallback(async (e) => {
    e.stopPropagation();
    try {
      // Always scroll to the node
      scrollToNode(node.nodeId);
      // Only select if not already selected
      selectNode(node.nodeId);
    } catch (err) {
      console.error('LocateNodeButton: Error caught:', err);
      setError("Sorry, we can't find the section in the index.");
    }
  }, [node.nodeId, selectNode, scrollToNode]);

  if (!node || (!Icon && !text)) {
    console.error('LocateNodeButton must have either an icon or text');
    return null;
  }

  const nodeType = node.type;
  const buttonTitle = nodeType ? `Select this ${nodeType}` : 'Select this';

  return (
    <div className={'relative'}>
      <button
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className={`locate-button transition-colors flex items-center gap-2 breadcrumb-text ${
          isHovered ? 'active-blue' : ''
        } ${className}`}
        title={buttonTitle}
      >
        {Icon && <Icon size={18} className={`transition-colors ${isHovered ? 'active-blue' : ''}`} />}
        {text && <span className={`transition-colors breadcrumb-text ${isHovered ? 'active-blue' : ''}`}>{text}</span>}
      </button>

      {error && (
        <div className="absolute right-0 top-0 -translate-y-full whitespace-nowrap bg-red-500 text-white text-xs py-1 px-2 rounded shadow-lg mb-2 z-50 animate-fade-in">
          {error}
          <div className="absolute -bottom-1 right-2 w-2 h-2 bg-red-500 rotate-45" />
        </div>
      )}
    </div>
  );
};
