/* Breadcrumb Bar Container */
.breadcrumb-bar {
  height: 50px;
  border-bottom: 1px solid #d1d5db; /* border-gray-300 */
  background-color: #f9fafb; /* bg-gray-50 */
  display: flex;
  align-items: center;
  position: relative;
  z-index: 40;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* TOC <PERSON>ton in Breadcrumb Bar */
.breadcrumb-toc-button {
  height: 100%;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  border-right: 1px solid #d1d5db;
  color: #6b7280; /* text-gray-500 */
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
  z-index: 50;
}

.breadcrumb-toc-button:hover {
  background-color: #e5e7eb; /* bg-gray-200 */
  color: #3b82f6; /* active-blue */
}

.breadcrumb-toc-button--hidden {
  display: none;
}

/* BreadcrumbPath styling */
.statute-breadcrumb {
  background: transparent; /* Use parent background */
  padding: 0;
  flex: 1;
  min-width: 0; /* Prevent flex item from overflowing */
  height: 100%;
  display: flex;
  align-items: center;
  position: relative; /* For absolute positioned gradients */
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap; /* force single line */
  gap: 0.25rem;
  width: 100%;
  overflow-x: auto !important;
  overflow-y: hidden;
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  pointer-events: auto !important;
  touch-action: pan-x !important;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.breadcrumb-container::-webkit-scrollbar {
  display: none;
}

.breadcrumb-container > *:first-child {
  margin-left: 10px;
}

/* Gradient overlays for scroll hint - Absolute positioned over breadcrumb */
.breadcrumb-gradient-left,
.breadcrumb-gradient-right {
  position: absolute;
  top: 0;
  width: 15px;
  height: 100%;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
  z-index: 45;
}
.breadcrumb-gradient-left {
  left: 0;
  background: linear-gradient(to right, rgba(0,0,0,0.15) 0%, rgba(0,0,0,0) 100%);
}
.breadcrumb-gradient-right {
  right: 0;
  background: linear-gradient(to left, rgba(0,0,0,0.25) 0%, rgba(0,0,0,0) 100%);
}

.breadcrumb-chevron {
  flex-shrink: 0;
  margin: 0 0.25rem;
}

/* Add padding for mobile */
@media (max-width: 640px) {
  .statute-breadcrumb {
    padding: 0.5rem 0;
  }
}

.breadcrumb-text {
  color: var(--text-color, #333333);
}
