@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-in;
}

/* Locate button styles */
.locate-button svg {
  color: #9ca3af; /* text-gray-300 to match bookmark icon */
}

.locate-button svg,
.locate-button .breadcrumb-text {
  color: #9ca3af; /* text-gray-300 to match bookmark icon */
  transition: color 0.2s ease;
}

/* Hover states */
.locate-button:hover svg,
.locate-button:hover .breadcrumb-text,
.locate-button button:hover svg,
.locate-button button:hover .breadcrumb-text {
  color: var(--accent-color); /* active-blue on hover */
}

/* Visibility rules */
.collection .locate-button,
.code .locate-button,
.title .locate-button,
.subtitle .locate-button,
.chapter .locate-button,
.subchapter .locate-button,
.section .locate-button {
  opacity: 0;
  transition: all 0.2s ease;
}

.collection:hover .locate-button,
.code:hover .locate-button,
.title:hover .locate-button,
.subtitle:hover .locate-button,
.chapter:hover .locate-button,
.subchapter:hover .locate-button,
.section:hover .locate-button {
  opacity: 1;
}
