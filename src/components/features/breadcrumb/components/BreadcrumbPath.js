import React, { useRef, useEffect, useState, useCallback } from 'react';
import { ChevronRight as ChevronRightIcon, List } from 'lucide-react';
import { BreadcrumbSegment } from './BreadcrumbSegment';
import { useBreadcrumbScroll } from '../hooks/useBreadcrumbScroll';
import { useLayout } from '@/app/LayoutContext';
import './BreadcrumbPath.css';

export const BreadcrumbPath = ({ content, contentRef }) => {
  const { visibleNodes } = useBreadcrumbScroll({ content, contentRef });
  const { isMobile, showTOCPanel, setShowTOCPanel } = useLayout();
  const containerRef = useRef(null);
  const [showLeftGradient, setShowLeftGradient] = useState(false);
  const [showRightGradient, setShowRightGradient] = useState(false);
  const [isContentLoaded, setIsContentLoaded] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);


  // Step 1: Load breadcrumb path items and auto-scroll when new items added
  useEffect(() => {
    if (visibleNodes?.length > 0) {
      setIsContentLoaded(true);
      setIsScrolled(false); // Reset scroll state to trigger auto-scroll
    }
  }, [visibleNodes]);

  // Step 2: Scroll all the way to the right (only after content is loaded)
  useEffect(() => {
    if (isContentLoaded && !isScrolled) {
      const el = containerRef.current;
      if (el) {
        // Wait for layout, then scroll to show most recent context
        setTimeout(() => {
          const maxScroll = el.scrollWidth - el.clientWidth;
          if (maxScroll > 0) {
            el.scrollLeft = maxScroll;
          }
          setIsScrolled(true);
        }, 10);
      }
    }
  }, [isContentLoaded, isScrolled]);


  // Gradient update function
  const updateGradients = useCallback(() => {
    const el = containerRef.current;
    if (!el) return;
    
    const threshold = 1;
    const scrollLeft = el.scrollLeft;
    const scrollWidth = el.scrollWidth;
    const clientWidth = el.clientWidth;
    const maxScroll = Math.max(0, scrollWidth - clientWidth);
    
    if (maxScroll <= threshold) {
      setShowLeftGradient(false);
      setShowRightGradient(false);
      return;
    }
    
    const showLeft = scrollLeft > threshold;
    const showRight = scrollLeft < maxScroll - threshold;
    
    setShowLeftGradient(showLeft);
    setShowRightGradient(showRight);
  }, []);

  // Set up scroll event listeners (after content is loaded and scrolled)
  useEffect(() => {
    if (!isScrolled) return;
    
    const el = containerRef.current;
    if (!el) return;
    
    // Initial gradient update
    updateGradients();
    
    // Add event listeners
    el.addEventListener('scroll', updateGradients, { passive: true });
    window.addEventListener('resize', updateGradients);
    
    return () => {
      el.removeEventListener('scroll', updateGradients);
      window.removeEventListener('resize', updateGradients);
    };
  }, [isScrolled, updateGradients]);

  return (
    <div id="breadcrumb-bar" className="breadcrumb-bar">
      {/* Desktop TOC Button - Flex item */}
      {!isMobile && (
        <button
          onClick={() => setShowTOCPanel?.(!showTOCPanel)}
          className="breadcrumb-toc-button"
          title="Table of Contents"
          aria-label="Toggle table of contents"
        >
          <List size={16} />
        </button>
      )}

      {/* Breadcrumb navigation - Flex item that grows */}
      {visibleNodes?.length > 0 && (
        <nav className="statute-breadcrumb" aria-label="Breadcrumb">
          <div className="breadcrumb-container" ref={containerRef} >
            {visibleNodes.map((node, index) => (
              <React.Fragment key={node.nodeId || `${node.code}-${node.type}-${node.id}`}>
                {index > 0 && (
                  <ChevronRightIcon size={14} className="breadcrumb-chevron text-gray-400 z-20 relative" />
                )}
                <BreadcrumbSegment node={node} />
              </React.Fragment>
            ))}
            {/* NOTE: Ghost element to force right margin space in flex container at end of breadcrumb bar */}
            <div style={{ width: 10, flex: 'none', pointerEvents: 'none' }} aria-hidden="true" >&nbsp;</div>
          </div>

          {/* Gradients positioned absolutely over the breadcrumb content */}
          <div className="breadcrumb-gradient-left" style={{ opacity: showLeftGradient ? 1 : 0 }}/>
          <div className="breadcrumb-gradient-right" style={{ opacity: showRightGradient ? 1 : 0 }}/>
        </nav>
      )}
    </div>
  );
};
