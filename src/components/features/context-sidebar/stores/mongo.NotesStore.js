export const runtime = "nodejs";

import { mongoClient } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { Note } from '@/components/features/context-sidebar/models/Note';

export class NotesStore {
  static NOTES_COLLECTION = 'notes';

  static async notesCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.NOTES_COLLECTION);
  }

  /**
   * Get all notes for a specific user and node
   */
  static async getNotesForNode(nodeId, userId, type = null) {
    try {
      const notesCollection = await this.notesCollection();
      const query = {
        nodeId: nodeId,
        userId: new ObjectId(userId)
      };

      if (type) {
        query.type = type;
      }

      const notes = await notesCollection
        .find(query)
        .sort({ createdAt: -1 })
        .toArray();

      return notes.map(note => Note.fromJSON(note));
    } catch (error) {
      console.error('Error getting notes for node:', error);
      throw error;
    }
  }

  /**
   * Get all notes for a specific user
   */
  static async getNotesForUser(userId) {
    try {
      const notesCollection = await this.notesCollection();
      const notes = await notesCollection
        .find({ userId: new ObjectId(userId) })
        .sort({ createdAt: -1 })
        .toArray();

      return notes.map(note => Note.fromJSON(note));
    } catch (error) {
      console.error('Error getting notes for user:', error);
      throw error;
    }
  }

  /**
   * Create a new note
   */
  static async createNote(noteData, userId) {
    try {
      const note = new Note({
        ...noteData,
        userId: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      note.validate();

      const notesCollection = await this.notesCollection();
      const result = await notesCollection.insertOne({
        nodeId: note.nodeId,
        type: note.type,
        text: note.text,
        userId: new ObjectId(note.userId),
        createdAt: note.createdAt,
        updatedAt: note.updatedAt
      });

      return Note.fromJSON({
        _id: result.insertedId,
        ...noteData,
        userId: userId,
        createdAt: note.createdAt,
        updatedAt: note.updatedAt
      });
    } catch (error) {
      console.error('Error creating note:', error);
      throw error;
    }
  }

  /**
   * Update an existing note
   */
  static async updateNote(noteId, updateData, userId) {
    try {
      const notesCollection = await this.notesCollection();

      // Verify the note belongs to the user
      const existingNote = await notesCollection.findOne({
        _id: new ObjectId(noteId),
        userId: new ObjectId(userId)
      });

      if (!existingNote) {
        throw new Error('Note not found or access denied');
      }

      const updatedData = {
        ...updateData,
        updatedAt: new Date()
      };

      const result = await notesCollection.updateOne(
        { _id: new ObjectId(noteId) },
        { $set: updatedData }
      );

      if (result.matchedCount === 0) {
        throw new Error('Note not found');
      }

      // Return the updated note
      const updatedNote = await notesCollection.findOne({
        _id: new ObjectId(noteId)
      });

      return Note.fromJSON(updatedNote);
    } catch (error) {
      console.error('Error updating note:', error);
      throw error;
    }
  }

  /**
   * Delete a note
   */
  static async deleteNote(noteId, userId) {
    try {
      const notesCollection = await this.notesCollection();

      const result = await notesCollection.deleteOne({
        _id: new ObjectId(noteId),
        userId: new ObjectId(userId)
      });

      if (result.deletedCount === 0) {
        throw new Error('Note not found or access denied');
      }

      return true;
    } catch (error) {
      console.error('Error deleting note:', error);
      throw error;
    }
  }

  /**
   * Get note by ID (with user verification)
   */
  static async getNoteById(noteId, userId) {
    try {
      const notesCollection = await this.notesCollection();
      const note = await notesCollection.findOne({
        _id: new ObjectId(noteId),
        userId: new ObjectId(userId)
      });

      if (!note) {
        return null;
      }

      return Note.fromJSON(note);
    } catch (error) {
      console.error('Error getting note by ID:', error);
      throw error;
    }
  }

  /**
   * Count notes for a user
   */
  static async countNotesForUser(userId) {
    try {
      const notesCollection = await this.notesCollection();
      return await notesCollection.countDocuments({
        userId: new ObjectId(userId)
      });
    } catch (error) {
      console.error('Error counting notes for user:', error);
      throw error;
    }
  }

  /**
   * Get nodes with notes for a user (for showing indicators)
   */
  static async getNodesWithNotes(userId) {
    try {
      const notesCollection = await this.notesCollection();
      const result = await notesCollection.distinct('nodeId', {
        userId: new ObjectId(userId)
      });

      return result;
    } catch (error) {
      console.error('Error getting nodes with notes:', error);
      throw error;
    }
  }
}
