'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { useContextSidebar } from '../hooks/useContextSidebar';
import { SummarySection } from './SummarySection';
import { CaseLawSection } from './CaseLawSection';
import { NotesSection } from './NotesSection';

export const ContextSidebar = () => {
  const {
    isOpen,
    currentNode,
    currentType,
    closeSidebar
  } = useContextSidebar();

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        closeSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, closeSidebar]);

  if (!isOpen || !currentNode) {
    return null;
  }

  return (
    <div className={`flex-shrink-0 bg-white border-l border-gray-200 shadow-lg transition-all duration-300 ease-in-out relative ${
      isOpen ? 'w-96' : 'w-0'
    }`}>
      {/* Chat bubble caret */}
      {isOpen && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-2 z-10">
          <div className="w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white"></div>
        </div>
      )}

      <div className={`h-full flex flex-col overflow-hidden ${isOpen ? 'opacity-100' : 'opacity-0'}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 truncate">
              Context
            </h2>
            <p className="text-sm text-gray-600 truncate">
              {currentNode.text || currentNode.nodeId}
            </p>
          </div>
          <button
            onClick={closeSidebar}
            className="ml-2 p-1 rounded-md hover:bg-gray-200 transition-colors"
            aria-label="Close sidebar"
          >
            <X size={20} />
          </button>
        </div>

        {/* Collapsible Sections */}
        <div className="flex-1 overflow-auto">
          <div className="border-b border-gray-200">
            <SummarySection node={currentNode} type={currentType} />
          </div>
          <div className="border-b border-gray-200">
            <CaseLawSection node={currentNode} type={currentType} />
          </div>
          <div>
            <NotesSection node={currentNode} type={currentType} />
          </div>
        </div>
      </div>
    </div>
  );
};
