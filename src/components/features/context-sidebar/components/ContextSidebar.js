'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';
import { useContextSidebar } from '../hooks/useContextSidebar';
import { SummarySection } from './SummarySection';
import { CaseLawSection } from './CaseLawSection';
import { NotesSection } from './NotesSection';

export const ContextSidebar = () => {
  const {
    isOpen,
    currentNode,
    currentType,
    activeSection,
    setActiveSection,
    closeSidebar,
    sidebarWidth,
    setSidebarWidth
  } = useContextSidebar();

  const [isResizing, setIsResizing] = useState(false);
  const sidebarRef = useRef(null);
  const minWidth = 350;
  const maxWidth = 800;

  // Handle resize functionality
  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing) return;

      const newWidth = window.innerWidth - e.clientX;
      const clampedWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
      setSidebarWidth(clampedWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing, setSidebarWidth]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        closeSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, closeSidebar]);

  if (!isOpen || !currentNode) {
    return null;
  }

  const sections = [
    { id: 'summary', label: 'Summary', component: SummarySection },
    { id: 'caselaw', label: 'Case Law', component: CaseLawSection },
    { id: 'notes', label: 'Notes', component: NotesSection }
  ];

  const ActiveComponent = sections.find(s => s.id === activeSection)?.component || SummarySection;

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black bg-opacity-20 z-40 transition-opacity duration-300 ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={closeSidebar}
      />

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={`
          fixed top-0 right-0 h-full bg-white shadow-2xl z-50
          transform transition-transform duration-300 ease-in-out
          border-l border-gray-200
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
        style={{ width: `${sidebarWidth}px` }}
      >
        {/* Resize handle */}
        <div
          className="absolute left-0 top-0 w-1 h-full cursor-col-resize bg-gray-200 hover:bg-blue-300 transition-colors"
          onMouseDown={handleMouseDown}
        />

        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-semibold text-gray-900 truncate">
              Context
            </h2>
            <p className="text-sm text-gray-600 truncate">
              {currentNode.text || currentNode.nodeId}
            </p>
          </div>
          <button
            onClick={closeSidebar}
            className="ml-2 p-1 rounded-md hover:bg-gray-200 transition-colors"
            aria-label="Close sidebar"
          >
            <X size={20} />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 bg-white">
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`
                flex-1 px-4 py-3 text-sm font-medium transition-colors
                ${activeSection === section.id
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }
              `}
            >
              {section.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          <ActiveComponent node={currentNode} type={currentType} />
        </div>
      </div>
    </>
  );
};
