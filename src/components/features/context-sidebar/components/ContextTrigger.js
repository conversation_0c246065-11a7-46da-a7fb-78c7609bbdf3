'use client';

import React from 'react';
import { Info } from 'lucide-react';
import { useContextSidebar } from '../hooks/useContextSidebar';

export const ContextTrigger = ({ 
  node, 
  type = 'node', 
  className = '',
  size = 16,
  children 
}) => {
  const { openSidebar } = useContextSidebar();

  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    openSidebar(node, type);
  };

  return (
    <button
      onClick={handleClick}
      className={`
        context-trigger
        inline-flex items-center justify-center
        w-6 h-6 rounded-full
        bg-blue-50 hover:bg-blue-100
        border border-blue-200 hover:border-blue-300
        text-blue-600 hover:text-blue-700
        transition-all duration-200
        opacity-70 group-hover:opacity-100
        focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
        ${className}
      `}
      title="Open context sidebar"
      aria-label="Open context sidebar for this item"
    >
      {children || <Info size={size} />}
    </button>
  );
};
