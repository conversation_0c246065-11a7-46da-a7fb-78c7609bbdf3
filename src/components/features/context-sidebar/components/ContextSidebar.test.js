import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ContextSidebar } from './ContextSidebar';
import { ContextSidebarProvider } from '../context/ContextSidebarContext';
import { useContextSidebar } from '../hooks/useContextSidebar';

// Mock the child components
jest.mock('./SummarySection', () => ({
  SummarySection: ({ node }) => <div data-testid="summary">Summary for {node.nodeId}</div>
}));

jest.mock('./CaseLawSection', () => ({
  CaseLawSection: ({ node }) => <div data-testid="case-law">Case Law for {node.nodeId}</div>
}));

jest.mock('./NotesSection', () => ({
  NotesSection: ({ node }) => <div data-testid="notes">Notes for {node.nodeId}</div>
}));

const mockNode = {
  nodeId: '/test/node/1',
  text: 'Test Node Text',
  type: 'section'
};

const TestWrapper = ({ children, initialState = {} }) => {
  return (
    <ContextSidebarProvider>
      {children}
    </ContextSidebarProvider>
  );
};

describe('ContextSidebar', () => {
  beforeEach(() => {
    // Reset any mocks
    jest.clearAllMocks();
  });

  test('renders nothing when sidebar is closed', () => {
    render(
      <TestWrapper>
        <ContextSidebar />
      </TestWrapper>
    );

    expect(screen.queryByText('Context')).not.toBeInTheDocument();
  });

  test('renders sidebar when open with node data', async () => {
    const TestComponent = () => {
      const [isOpen, setIsOpen] = React.useState(false);

      React.useEffect(() => {
        // Simulate opening the sidebar
        setIsOpen(true);
      }, []);

      return (
        <ContextSidebarProvider>
          <button onClick={() => setIsOpen(true)}>Open Sidebar</button>
          <ContextSidebar />
        </ContextSidebarProvider>
      );
    };

    render(<TestComponent />);

    fireEvent.click(screen.getByText('Open Sidebar'));

    await waitFor(() => {
      expect(screen.getByText('Context')).toBeInTheDocument();
    });
  });

  test('displays correct tab navigation', async () => {
    const TestComponent = () => {
      const { openSidebar } = useContextSidebar();

      React.useEffect(() => {
        openSidebar(mockNode, 'node');
      }, [openSidebar]);

      return <ContextSidebar />;
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Summary')).toBeInTheDocument();
      expect(screen.getByText('Case Law')).toBeInTheDocument();
      expect(screen.getByText('Notes')).toBeInTheDocument();
    });
  });

  test('switches between tabs correctly', async () => {
    const TestComponent = () => {
      const { openSidebar } = useContextSidebar();

      React.useEffect(() => {
        openSidebar(mockNode, 'node');
      }, [openSidebar]);

      return <ContextSidebar />;
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('ai-summary')).toBeInTheDocument();
    });

    // Click on Case Law tab
    fireEvent.click(screen.getByText('Case Law'));

    await waitFor(() => {
      expect(screen.getByTestId('case-law')).toBeInTheDocument();
    });

    // Click on Notes tab
    fireEvent.click(screen.getByText('Notes'));

    await waitFor(() => {
      expect(screen.getByTestId('notes')).toBeInTheDocument();
    });
  });

  test('closes sidebar when close button is clicked', async () => {
    const TestComponent = () => {
      const { openSidebar, isOpen } = useContextSidebar();

      React.useEffect(() => {
        openSidebar(mockNode, 'node');
      }, [openSidebar]);

      return (
        <div>
          <div data-testid="sidebar-state">{isOpen ? 'open' : 'closed'}</div>
          <ContextSidebar />
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('sidebar-state')).toHaveTextContent('open');
    });

    // Click close button
    const closeButton = screen.getByLabelText('Close sidebar');
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.getByTestId('sidebar-state')).toHaveTextContent('closed');
    });
  });

  test('closes sidebar when backdrop is clicked', async () => {
    const TestComponent = () => {
      const { openSidebar, isOpen } = useContextSidebar();

      React.useEffect(() => {
        openSidebar(mockNode, 'node');
      }, [openSidebar]);

      return (
        <div>
          <div data-testid="sidebar-state">{isOpen ? 'open' : 'closed'}</div>
          <ContextSidebar />
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('sidebar-state')).toHaveTextContent('open');
    });

    // Click backdrop (the overlay div)
    const backdrop = document.querySelector('.bg-black.bg-opacity-20');
    fireEvent.click(backdrop);

    await waitFor(() => {
      expect(screen.getByTestId('sidebar-state')).toHaveTextContent('closed');
    });
  });

  test('closes sidebar when Escape key is pressed', async () => {
    const TestComponent = () => {
      const { openSidebar, isOpen } = useContextSidebar();

      React.useEffect(() => {
        openSidebar(mockNode, 'node');
      }, [openSidebar]);

      return (
        <div>
          <div data-testid="sidebar-state">{isOpen ? 'open' : 'closed'}</div>
          <ContextSidebar />
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('sidebar-state')).toHaveTextContent('open');
    });

    // Press Escape key
    fireEvent.keyDown(document, { key: 'Escape' });

    await waitFor(() => {
      expect(screen.getByTestId('sidebar-state')).toHaveTextContent('closed');
    });
  });
});
