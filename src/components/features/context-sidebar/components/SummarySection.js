'use client';

import React, { useState, useEffect } from 'react';
import { RefreshCw, AlertCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export const SummarySection = ({ node, type }) => {
  const [summary, setSummary] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isExpanded, setIsExpanded] = useState(true);

  // Fetch AI summary for the node
  useEffect(() => {
    if (!node?.nodeId) return;

    const fetchSummary = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/v1/ai-summary/${encodeURIComponent(node.nodeId)}`);

        if (!response.ok) {
          throw new Error('Failed to fetch summary');
        }

        const data = await response.json();
        setSummary(data.summary);
      } catch (err) {
        console.error('Error fetching AI summary:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSummary();
  }, [node?.nodeId]);

  const handleRefresh = () => {
    if (node?.nodeId) {
      setSummary(null);
      setError(null);
      // Re-trigger the effect by updating a dependency
      const fetchSummary = async () => {
        setIsLoading(true);
        try {
          const response = await fetch(`/api/v1/ai-summary/${encodeURIComponent(node.nodeId)}?refresh=true`);
          if (!response.ok) throw new Error('Failed to refresh summary');
          const data = await response.json();
          setSummary(data.summary);
        } catch (err) {
          setError(err.message);
        } finally {
          setIsLoading(false);
        }
      };
      fetchSummary();
    }
  };

  return (
    <div className="p-4">
      {/* Section Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 text-lg font-semibold text-gray-900 hover:text-gray-700"
        >
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          AI Summary
        </button>
        <button
          onClick={handleRefresh}
          disabled={isLoading}
          className="p-1 rounded-md hover:bg-gray-100 disabled:opacity-50 transition-colors"
          title="Refresh summary"
        >
          <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="space-y-4">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <LoadingSpinner loading={true} />
                <p className="text-sm text-gray-600 mt-2">Generating summary...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle size={20} className="text-red-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800">Error loading summary</p>
                <p className="text-sm text-red-600 mt-1">{error}</p>
                <button
                  onClick={handleRefresh}
                  className="text-sm text-red-700 hover:text-red-800 underline mt-2"
                >
                  Try again
                </button>
              </div>
            </div>
          )}

          {summary && !isLoading && (
            <div className="prose prose-sm max-w-none">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {summary}
                </div>
              </div>
            </div>
          )}

          {!summary && !isLoading && !error && (
            <div className="text-center py-8">
              <p className="text-gray-500 text-sm">No summary available for this section.</p>
              <button
                onClick={handleRefresh}
                className="text-blue-600 hover:text-blue-700 text-sm underline mt-2"
              >
                Generate summary
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
