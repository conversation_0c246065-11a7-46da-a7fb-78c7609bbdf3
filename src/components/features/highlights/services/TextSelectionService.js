export class TextSelectionService {
  isPickerClick(element) {
    // Check if click is on picker or its children
    return element.closest('.statute-content-picker-container, .highlight-picker') !== null;
  }

  createSelection(selection) {
    if (!this._isValidSelection(selection)) {
      return null;
    }

    const range = selection.getRangeAt(0);
    const text = range.toString().trim();

    const boundaries = this._getSelectionBoundaries(range);
    if (!boundaries) {
      return null;
    }

    let matches;
    if (boundaries.isSingleNode) {
      matches = this._createMatchForNode(range, boundaries.startNode, text);
    } else {
      matches = this._getMultiSubsectionMatches(range, boundaries);
    }

    if (!matches?.length) {
      return null;
    }

    // Use the first rect from getClientRects() for multi-line selections
    let rect;
    const rects = range.getClientRects();
    if (rects.length > 0) {
      rect = rects[0]; // Use the first rect (start of selection)
    } else {
      rect = range.getBoundingClientRect(); // Fallback
    }

    // Check if rect has valid dimensions before returning
    if (!rect || rect.width === 0 || rect.height === 0) {
      console.warn('Selection rect has invalid dimensions:', rect);
      return {
        text,
        matches
        // Omit rect to prevent errors
      };
    }

    return {
      text,
      matches,
      rect
    };
  }

  _isValidSelection(selection) {
    if (!selection || selection.isCollapsed) {
      return false;
    }

    const range = selection.getRangeAt(0);
    const text = range.toString().trim();
    if (!text) {
      console.warn('No text selected');
      return false;
    }

    // Get the actual nodes containing the selection
    const startNode = this._findClosestNode(range.startContainer);
    const endNode = this._findClosestNode(range.endContainer);

    if (!startNode || !endNode) {
      console.warn('Selection not within valid nodes');
      return false;
    }

    return true;
  }

  _getSelectionBoundaries(range) {
    const startNode = this._findClosestNode(range.startContainer);
    const endNode = this._findClosestNode(range.endContainer);

    // Return null for invalid selections
    if (!startNode || !endNode) {
      console.error('Selection boundaries not found');
      return null;
    }

    // Return both nodes, letting the caller determine how to handle them
    return {
      startNode,
      endNode,
      isSingleNode: startNode === endNode
    };
  }

  _createMatchForNode(range, node, trimmedText = null) {
    if (!range || !node) return [];

    const nodeRange = document.createRange();
    nodeRange.selectNodeContents(node);

    // Get the full node text to find the trimmed text within it
    const nodeText = node.textContent || '';
    const rawSelectedText = range.toString();
    const selectedText = trimmedText || rawSelectedText.trim();

    // Calculate offset relative to the node's full content
    const precedingRange = document.createRange();
    precedingRange.setStart(node, 0);
    precedingRange.setEnd(range.startContainer, range.startOffset);
    const precedingText = precedingRange.toString();
    
    // Find where the trimmed text actually starts in the node
    let actualStartOffset;
    if (rawSelectedText !== selectedText) {
      // Text was trimmed, so we need to find the actual start of the trimmed text
      const rawStartOffset = precedingText.length;
      const textAfterStart = nodeText.substring(rawStartOffset);
      const trimmedTextIndex = textAfterStart.indexOf(selectedText);
      actualStartOffset = rawStartOffset + (trimmedTextIndex >= 0 ? trimmedTextIndex : 0);
    } else {
      // No trimming needed
      actualStartOffset = precedingText.length;
    }

    return [{
      nodeId: node.getAttribute('data-node-id'),
      startOffset: actualStartOffset,
      endOffset: actualStartOffset + selectedText.length
    }];
  }

  _getMultiSubsectionMatches(range, boundaries) {
    const matches = [];
    const subsections = this._getNodesBetween(boundaries.startNode, boundaries.endNode);

    subsections.forEach((subsection, index) => {
      const isFirst = index === 0;
      const isLast = index === subsections.length - 1;

      // Create a range for this subsection
      const subsectionRange = document.createRange();
      subsectionRange.selectNodeContents(subsection);

      // Adjust range boundaries for first and last subsections
      if (isFirst) {
        subsectionRange.setStart(range.startContainer, range.startOffset);
      }
      if (isLast) {
        subsectionRange.setEnd(range.endContainer, range.endOffset);
      }

      // Use _createMatchForNode to generate the match
      const subsectionMatches = this._createMatchForNode(subsectionRange, subsection);
      matches.push(...subsectionMatches);
    });

    return matches;
  }

  _getNodesBetween(start, end) {
    // If start and end are the same, just return that node
    if (start === end) {
      return [start];
    }

    const nodes = [];
    let current = start;

    // Get the common parent element
    const commonAncestor = this._findCommonAncestor(start, end);
    if (!commonAncestor) return [start];

    // Only collect nodes that are actually part of the selection
    while (current && current !== end.nextElementSibling) {
      if (current.hasAttribute('data-node-id') &&
          commonAncestor.contains(current) &&
          this._isNodeInSelection(current, start, end)) {
        nodes.push(current);
      }
      current = current.nextElementSibling;
    }

    return nodes;
  }

  _findCommonAncestor(node1, node2) {
    const path1 = this._getNodePath(node1);
    const path2 = this._getNodePath(node2);

    for (let i = 0; i < path1.length && i < path2.length; i++) {
      if (path1[i] !== path2[i]) {
        return path1[i - 1];
      }
    }
    return path1[Math.min(path1.length, path2.length) - 1];
  }

  _getNodePath(node) {
    const path = [];
    let current = node;
    while (current) {
      path.unshift(current);
      current = current.parentElement;
    }
    return path;
  }

  _isNodeInSelection(node, startNode, endNode) {
    const range = document.createRange();
    range.setStartBefore(startNode);
    range.setEndAfter(endNode);
    return range.intersectsNode(node);
  }

  _findClosestNode(node) {
    // Handle text nodes
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentElement;
    }

    while (node && !node.hasAttribute('data-node-id')) {
      node = node.parentElement;
    }
    return node;
  }
}
