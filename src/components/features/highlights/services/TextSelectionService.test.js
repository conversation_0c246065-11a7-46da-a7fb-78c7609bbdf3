import { TextSelectionService } from './TextSelectionService';

// Mock DOM methods
global.document = {
  createRange: () => ({
    toString: jest.fn(),
    getClientRects: jest.fn(() => [{ width: 100, height: 20, top: 10, left: 10 }]),
    getBoundingClientRect: jest.fn(() => ({ width: 100, height: 20, top: 10, left: 10 })),
    setStart: jest.fn(),
    setEnd: jest.fn(),
    selectNodeContents: jest.fn(),
    startContainer: null,
    endContainer: null,
    startOffset: 0,
    endOffset: 0
  }),
  createTreeWalker: jest.fn()
};

global.Node = {
  TEXT_NODE: 3
};

describe('TextSelectionService', () => {
  let service;
  let mockSelection;
  let mockRange;
  let mockNode;

  beforeEach(() => {
    service = new TextSelectionService();
    
    mockNode = {
      nodeType: 1, // Element node
      hasAttribute: jest.fn(() => true),
      getAttribute: jest.fn(() => 'test-node-id'),
      textContent: '(a) This temporary provision applies to all cases.',
      parentElement: null
    };

    mockRange = {
      toString: jest.fn(),
      getClientRects: jest.fn(() => [{ width: 100, height: 20, top: 10, left: 10, bottom: 30, right: 110 }]),
      getBoundingClientRect: jest.fn(() => ({ width: 100, height: 20, top: 10, left: 10, bottom: 30, right: 110 })),
      startContainer: { nodeType: 3, parentElement: mockNode }, // Text node
      endContainer: { nodeType: 3, parentElement: mockNode },
      startOffset: 4, // Position right before "This"
      endOffset: 8,   // Position right after "This"
      commonAncestorContainer: mockNode
    };

    mockSelection = {
      isCollapsed: false,
      getRangeAt: jest.fn(() => mockRange)
    };

    // Mock document.createRange for the service
    global.document.createRange = jest.fn(() => ({
      toString: jest.fn(() => ''),
      setStart: jest.fn(),
      setEnd: jest.fn(),
      selectNodeContents: jest.fn()
    }));
  });

  describe('createSelection with punctuation boundary issue', () => {
    test('should handle selection of first word after punctuation', () => {
      // Mock the selection of "This" (with leading space in raw text)
      mockRange.toString.mockReturnValue(' This'); // Raw selection includes leading space
      
      // Mock the range objects that will be created
      const mockNodeRange = {
        toString: jest.fn(() => ''),
        setStart: jest.fn(),
        setEnd: jest.fn(),
        selectNodeContents: jest.fn()
      };
      
      const mockPrecedingRange = {
        toString: jest.fn(() => '(a)'), // Text before the selection start
        setStart: jest.fn(),
        setEnd: jest.fn(),
        selectNodeContents: jest.fn()
      };
      
      global.document.createRange
        .mockReturnValueOnce(mockNodeRange) // For nodeRange
        .mockReturnValueOnce(mockPrecedingRange); // For precedingRange

      const result = service.createSelection(mockSelection);

      expect(result).not.toBeNull();
      expect(result.text).toBe('This'); // Should be trimmed
      expect(result.matches).toHaveLength(1);
      expect(result.matches[0]).toMatchObject({
        nodeId: 'test-node-id',
        startOffset: 4, // Should point to start of "This", not the space
        endOffset: 8    // Should point to end of "This"
      });
      expect(result.rect).toBeDefined();
    });

    test('should handle selection without leading whitespace', () => {
      // Mock the selection of "temporary" (no leading space)
      mockRange.toString.mockReturnValue('temporary');
      mockRange.startOffset = 9;
      mockRange.endOffset = 18;
      
      // Mock the range objects that will be created
      const mockNodeRange = {
        toString: jest.fn(() => ''),
        setStart: jest.fn(),
        setEnd: jest.fn(),
        selectNodeContents: jest.fn()
      };
      
      const mockPrecedingRange = {
        toString: jest.fn(() => '(a) This '), // Text before "temporary"
        setStart: jest.fn(),
        setEnd: jest.fn(),
        selectNodeContents: jest.fn()
      };
      
      global.document.createRange
        .mockReturnValueOnce(mockNodeRange) // For nodeRange
        .mockReturnValueOnce(mockPrecedingRange); // For precedingRange

      const result = service.createSelection(mockSelection);

      expect(result).not.toBeNull();
      expect(result.text).toBe('temporary');
      expect(result.matches).toHaveLength(1);
      expect(result.matches[0]).toMatchObject({
        nodeId: 'test-node-id',
        startOffset: 9,
        endOffset: 18
      });
    });
  });
});