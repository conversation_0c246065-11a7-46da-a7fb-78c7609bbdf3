import { ReferenceLink } from '@/components/features/references';

export class ContentRenderer {
  // Static style objects to prevent re-renders
  static styles = {
    highlight: { position: 'relative', zIndex: 1 },
    reference: { position: 'relative', zIndex: 0 },
    search: { position: 'relative', zIndex: 2 }
  };

  constructor(eventHandlers = {}) {
    this.eventHandlers = eventHandlers;
  }

  highlight(segment) {
    const { text, data, overlappingSegments = [] } = segment;

    // Sort overlapping segments by z-index (search > highlight)
    const sortedSegments = [...overlappingSegments].sort((a, b) => {
      if (a.type === 'search') return -1;
      if (b.type === 'search') return 1;
      return 0;
    });

    // Apply segments from innermost to outermost
    const content = sortedSegments.reduce((acc, overlapping) => {
      return this[overlapping.type]({
        ...overlapping,
        text: acc
      });
    }, text);

    const element = (
      <span
        key={`highlight-${data.id}-${segment.startOffset}-${segment.endOffset}`}
        className={`highlight-span ${data.color}`}
        data-highlight-id={data.id}
        style={ContentRenderer.styles.highlight}
        {...this.eventHandlers.highlight}
      >
        {content}
      </span>
    );

    return element;
  }

  reference(segment) {
    const { text, data, preserveHandlers = {} } = segment;

    // If href is undefined but status is not "unresolved", something is wrong
    if (!data.href && !data.status) {
      console.warn('Reference has no href but is not marked as unresolved:', {
        text,
        data
      });
    }

    return (
      <ReferenceLink
        key={`reference-${data.href}-${segment.startOffset}-${segment.endOffset}`}
        href={data.href}
        className={`statute-reference ${data.status ? data.status : ''}`}
        style={ContentRenderer.styles.reference}
        reference={segment.reference}
        {...preserveHandlers}
      >
        {text}
      </ReferenceLink>
    );
  }

  search(segment) {
    return (
      <span
        key={`search-${segment.startOffset}`}
        className="search-highlight"
        style={ContentRenderer.styles.search}
        {...this.eventHandlers.search}
      >
        {segment.text}
      </span>
    );
  }
}
