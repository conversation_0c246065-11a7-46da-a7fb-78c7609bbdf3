import { SEARCH_CONSTANTS } from '@/components/features/search/constants';
import { Node } from '@/components/features/statutes/models/Node';

/**
 * Service for processing content with references, highlights, and search matches.
 *
 * @param {Object} node - The node object with collection and code properties
 * @param {Object} renderer - The renderer object used to render different segment types
 * @param {Array} highlights - Array of highlight objects
 * @param {Array} references - Array of reference objects
 */
export class ContentProcessingService {
  constructor(node, renderer, highlights = [], references = []) {
    this.node = node;
    this.renderer = renderer;
    this.highlights = highlights;
    this.references = references;
  }

  _getAllBoundaryPoints(references, highlights, searchMatches = []) {
    const points = new Set();
    [...references, ...highlights, ...searchMatches].forEach(segment => {
      points.add(segment.startOffset);
      points.add(segment.endOffset);
    });

    const sortedPoints = Array.from(points).sort((a, b) => a - b);

    return sortedPoints;
  }

  /**
   * Process content with references, highlights, and search matches.
   *
   * @param {string} content - The text content to process
   * @param {string} searchQuery - Optional search query to highlight in the content
   * @returns {Array} Processed content segments
   */
  async processContent(content, searchQuery) {
    const references = this.references || [];
    const highlights = this.highlights || [];

    // Process all three match types concurrently and await their results
    const [referenceMatches, highlightMatches, searchMatches] = await Promise.all([
      this._processReferences(content, references),
      this._processHighlights(content, highlights),
      this._processSearchMatches(content, searchQuery)
    ]);

    const sortedPoints = this._getAllBoundaryPoints(referenceMatches, highlightMatches, searchMatches);

    const result = [];
    let lastIndex = 0;

    for (let i = 0; i < sortedPoints.length - 1; i++) {
      const start = sortedPoints[i];
      const end = sortedPoints[i + 1];

      if (start > lastIndex) {
        result.push(content.slice(lastIndex, start));
      }

      const segmentText = content.slice(start, end);
      const activeSegments = [
        ...referenceMatches.filter(r => r.startOffset <= start && r.endOffset >= end),
        ...highlightMatches.filter(h => h.startOffset <= start && h.endOffset >= end),
        ...searchMatches.filter(s => s.startOffset <= start && s.endOffset >= end)
      ].sort((a, b) => {
        if (a.type === 'search') return -1;
        if (b.type === 'search') return 1;
        if (a.type === 'highlight') return -1;
        if (b.type === 'highlight') return 1;
        return 0;
      });

      if (activeSegments.length > 0) {
        let rendered = segmentText;

        for (const segment of activeSegments) {
          rendered = this.renderer[segment.type]({
            ...segment,
            text: rendered,
            startOffset: start,
            endOffset: end
          });
        }
        result.push(rendered);
      } else {
        result.push(segmentText);
      }

      lastIndex = end;
    }

    if (lastIndex < content.length) {
      result.push(content.slice(lastIndex));
    }

    return result;
  }

  /**
   * Process references into segments for rendering.
   *
   * @param {string} content - The text content
   * @param {Array} references - Array of reference objects with matches
   * @returns {Array} Processed reference segments
   */
  async _processReferences(content, references) {
    if (!content || !references || !Array.isArray(references) || references.length === 0) {
      return [];
    }

    // Skip reference processing if node is not a content type
    if (!this.node || !this.node.type || !Node.CONTENT_TYPES.includes(this.node.type)) {
      return [];
    }

    // Flatten references and their matches into individual segments
    const segments = [];
    references.forEach(ref => {
      ref.matches.forEach(match => {
        segments.push({
          type: 'reference',
          startOffset: match.startOffset,
          endOffset: match.endOffset,
          text: match.text,
          data: {
            nodeId: match.nodeId,
            href: match.target,
            status: match.status
          },
          reference: ref
        });
      });
    });

    return segments;
  }

  async _processHighlights(content, highlights) {
    if (!highlights || !Array.isArray(highlights)) {
      return [];
    }

    const segments = [];
    // Sort highlights by ID (newer first) before processing
    [...highlights]
      .sort((a, b) => {
        const idA = a.data?.id || a.id;
        const idB = b.data?.id || b.id;
        return (idB || '').localeCompare(idA || '');
      })
      .forEach(highlight => {
        // Only process matches for this exact nodeId
        const currentNodeMatches = highlight.matches.filter(match =>
          match.nodeId === this.node.nodeId
        );

        currentNodeMatches.forEach(match => {
          segments.push({
            type: 'highlight',
            startOffset: match.startOffset,
            endOffset: match.endOffset,
            text: content.slice(match.startOffset, match.endOffset),
            data: {
              id: highlight.id,
              color: highlight.color
            }
          });
        });
      });

    return segments;
  }

  async _processSearchMatches(content, searchQuery) {
    if (!content || !searchQuery || searchQuery.length < SEARCH_CONSTANTS.MIN_QUERY_LENGTH) return [];

    const lowerText = content.toLowerCase();
    const lowerQuery = searchQuery.toLowerCase();
    const matches = [];
    let index = 0;

    while ((index = lowerText.indexOf(lowerQuery, index)) !== -1) {
      matches.push({
        type: 'search',
        startOffset: index,
        endOffset: index + searchQuery.length,
        text: content.slice(index, index + searchQuery.length)
      });
      index += searchQuery.length;
    }

    return matches;
  }

  _applyLayer(content, segments, renderer) {
    if (!segments.length) return content;

    const points = new Set();
    segments.forEach(segment => {
      points.add(segment.startOffset);
      points.add(segment.endOffset);
    });

    const sortedPoints = Array.from(points).sort((a, b) => a - b);
    const result = [];
    let lastIndex = 0;
    const textContent = Array.isArray(content) ? content[0] : content;

    for (let i = 0; i < sortedPoints.length - 1; i++) {
      const start = sortedPoints[i];
      const end = sortedPoints[i + 1];

      if (start > lastIndex) {
        result.push(textContent.slice(lastIndex, start));
      }

      const containingSegments = segments.filter(
        segment => segment.startOffset <= start && segment.endOffset >= end
      ).sort((a, b) => {
        // Ensure highlights are processed last to preserve their event handlers
        if (a.type === 'highlight') return 1;
        if (b.type === 'highlight') return -1;
        return 0;
      });

      if (containingSegments.length > 0) {
        let rendered = textContent.slice(start, end);
        let eventHandlers = {};

        // Process segments in order, preserving highlight event handlers
        for (const segment of containingSegments) {
          rendered = renderer({
            ...segment,
            text: rendered,
            startOffset: start,
            endOffset: end,
            preserveHandlers: eventHandlers
          });

          // Capture highlight event handlers
          if (segment.type === 'highlight' && rendered.props) {
            eventHandlers = Object.entries(rendered.props)
              .filter(([key]) => key.startsWith('on'))
              .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
          }
        }

        result.push(rendered);
      }

      lastIndex = end;
    }

    return result;
  }
}
