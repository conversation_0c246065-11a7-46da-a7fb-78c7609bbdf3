import { Highlight } from '@/components/features/highlights';

const API_VERSION = 'v1';
const API_BASE = `/api/${API_VERSION}`;
const BASE_URL = `${API_BASE}/highlights`;
const ENDPOINTS = {
  COLLECTION: BASE_URL,
  BY_ID: (id) => `${BASE_URL}/${id}`,
  BY_NODE: (nodeId) => `${BASE_URL}?node=${nodeId}`
};

/**
 * Service for interacting with the highlights API.
 * All methods are static and return Promises that resolve to Highlight instances.
 */
export class HighlightService {
  /**
   * Fetches highlights, optionally filtered by nodeId
   * @param {string} [nodeId] - Optional nodeId to filter highlights
   * @returns {Promise<Highlight[]>} Array of Highlight instances
   * @throws {Error} If the API request fails
   */
  static async getHighlights(nodeId = null) {
    try {
      const url = nodeId ? ENDPOINTS.BY_NODE(nodeId) : ENDPOINTS.COLLECTION;
      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to fetch highlights: ${response.statusText}`
        );
      }

      const data = await response.json();
      if (!Array.isArray(data)) {
        return [];
      }

      // Filter out any invalid highlights
      return data
        .map(h => {
          try {
            return h ? new Highlight(h) : null;
          } catch (error) {
            console.error('Invalid highlight data:', error);
            return null;
          }
        })
        .filter(Boolean);
    } catch (error) {
      console.error('Error in getHighlights:', error);
      throw error;
    }
  }

  /**
   * Adds a new highlight
   * @param {Object|Highlight} highlight - The highlight to add (can be plain object or Highlight instance)
   * @returns {Promise<Highlight>} The added highlight
   * @throws {Error} If validation or API request fails
   */
  static async addHighlight(highlight) {
    try {
      // Only create a new instance if we don't already have one
      const highlightInstance = highlight instanceof Highlight
        ? highlight
        : new Highlight(highlight);

      const response = await fetch(ENDPOINTS.COLLECTION, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(highlightInstance.toPlainObject())
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to add highlight: ${response.statusText}`
        );
      }

      const data = await response.json();
      return new Highlight(data);
    } catch (error) {
      console.error('Error in addHighlight:', error);
      throw error;
    }
  }

  /**
   * Updates an existing highlight
   * @param {Object} highlight - The highlight to update
   * @returns {Promise<Highlight>} The updated highlight
   * @throws {Error} If validation or API request fails
   */
  static async updateHighlight(highlight) {
    try {
      // Only create a new instance if we don't already have one
      const highlightToUpdate = highlight instanceof Highlight
        ? highlight
        : new Highlight(highlight);

      if (!highlightToUpdate.id) {
        throw new Error('Cannot update highlight: missing ID');
      }

      const response = await fetch(ENDPOINTS.BY_ID(highlightToUpdate.id), {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(highlightToUpdate.toPlainObject())
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to update highlight: ${response.statusText}`
        );
      }

      const data = await response.json();
      return new Highlight(data);
    } catch (error) {
      console.error('Error in updateHighlight:', error);
      throw error;
    }
  }

  /**
   * Removes a highlight by ID
   * @param {string} highlightId - ID of the highlight to remove
   * @returns {Promise<boolean>} True if successful
   * @throws {Error} If the API request fails
   */
  static async removeHighlight(highlightId) {
    try {
      if (!highlightId) {
        throw new Error('Cannot remove highlight: missing ID');
      }

      const response = await fetch(ENDPOINTS.BY_ID(highlightId), {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to remove highlight: ${response.statusText}`
        );
      }

      return true;
    } catch (error) {
      console.error('Error in removeHighlight:', error);
      throw error;
    }
  }

  /**
   * Gets all highlights for the current user
   * @returns {Promise<Highlight[]>} Array of highlights
   * @throws {Error} If the API request fails
   */
  static async getAllHighlights() {
    try {
      const response = await fetch(ENDPOINTS.COLLECTION);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to get highlights: ${response.statusText}`
        );
      }

      const data = await response.json();
      return Array.isArray(data)
        ? data.map(h => {
            try {
              return new Highlight(h);
            } catch (error) {
              console.error('Invalid highlight data:', error);
              return null;
            }
          }).filter(Boolean)
        : [];
    } catch (error) {
      console.error('Error in getAllHighlights:', error);
      throw error;
    }
  }
}
