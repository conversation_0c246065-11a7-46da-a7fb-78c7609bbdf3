import { ContentProcessingService } from './ContentProcessingService';
import { Node } from '@/components/features/statutes/models/Node';
import { Highlight } from '@/components/features/highlights/models/Highlight';
import { Reference } from '@/components/features/references/models/Reference';

describe('ContentProcessingService', () => {
  const mockNode = new Node({
    id: 'test-id',
    nodeId: 'test/node/id',
    type: 'subsection',
    text: 'Test content'
  });

  const mockRenderer = {
    reference: jest.fn(props => `<ref>${props.text}</ref>`),
    highlight: jest.fn(props => `<highlight>${props.text}</highlight>`),
    search: jest.fn(props => `<search>${props.text}</search>`)
  };

  const mockReference = new Reference({
    id: 'ref-1',
    nodeId: mockNode.nodeId,
    text: 'reference',
    matches: [{
      startOffset: 5,
      endOffset: 14,
      nodeId: mockNode.nodeId,
      text: 'reference',
      target: '/test/target',
      status: 'resolved'
    }]
  });

  const mockHighlight = new Highlight({
    id: 'highlight-1',
    color: 'yellow',
    matches: [{
      startOffset: 0,
      endOffset: 4,
      nodeId: mockNode.nodeId,
      text: 'Test'
    }]
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockNode.references = [];
    mockNode.highlights = [];
  });

  it('processes content without any matches', async () => {
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test content');
    expect(result).toEqual(['Test content']);
  });

  it('processes content with references', async () => {
    mockNode.references = [mockReference];
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test reference here');
    expect(result).toEqual(['Test reference here']);
  });

  it('processes content with highlights', async () => {
    mockNode.highlights = [mockHighlight];
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test content');
    expect(result).toEqual(['Test content']);
  });

  it('processes content with search matches', async () => {
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test content', 'test');
    expect(result).toEqual(['<search>Test</search>', ' content']);
    expect(mockRenderer.search).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'search',
        text: 'Test'
      })
    );
  });

  it('processes content with overlapping matches', async () => {
    mockNode.references = [mockReference];
    mockNode.highlights = [mockHighlight];
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test reference', 'test');
    expect(result).toEqual(['<search>Test</search>', ' reference']);
  });

  it('handles empty content', async () => {
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('');
    expect(result).toEqual([]);
  });

  it('handles null content', async () => {
    const service = new ContentProcessingService(mockNode, mockRenderer);
    await expect(service.processContent(null)).rejects.toThrow();
  });

  it('handles content without a node', async () => {
    const service = new ContentProcessingService(null, mockRenderer);
    const result = await service.processContent('Test content');
    expect(result).toEqual(['Test content']);
  });

  it('handles invalid search query', async () => {
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test content', '');
    expect(result).toEqual(['Test content']);
  });

  it('processes content with multiple references', async () => {
    const multipleReferences = [
      new Reference({
        id: 'ref-1',
        nodeId: mockNode.nodeId,
        matches: [{
          startOffset: 0,
          endOffset: 4,
          nodeId: mockNode.nodeId,
          text: 'Test',
          target: '/test/1',
          status: 'resolved'
        }]
      }),
      new Reference({
        id: 'ref-2',
        nodeId: mockNode.nodeId,
        matches: [{
          startOffset: 13,
          endOffset: 17,
          nodeId: mockNode.nodeId,
          text: 'more',
          target: '/test/2',
          status: 'resolved'
        }]
      })
    ];

    mockNode.references = multipleReferences;
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test content more text');
    expect(result).toEqual(['Test content more text']);
  });

  it('processes content with multiple highlights', async () => {
    const multipleHighlights = [
      new Highlight({
        id: 'highlight-1',
        color: 'yellow',
        matches: [{
          startOffset: 0,
          endOffset: 4,
          nodeId: mockNode.nodeId,
          text: 'Test'
        }]
      }),
      new Highlight({
        id: 'highlight-2',
        color: 'green',
        matches: [{
          startOffset: 13,
          endOffset: 17,
          nodeId: mockNode.nodeId,
          text: 'more'
        }]
      })
    ];

    mockNode.highlights = multipleHighlights;
    const service = new ContentProcessingService(mockNode, mockRenderer);
    const result = await service.processContent('Test content more text');
    expect(result).toEqual(['Test content more text']);
  });
});
