import { useState, useCallback, useMemo } from 'react';
import { TextSelectionService } from '@/components/features/highlights';

export function useTextSelection() {
  const [selectedText, setSelectedText] = useState(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const selectionService = useMemo(() => new TextSelectionService(), []);

  const handleMouseDown = useCallback((e) => {
    if (selectionService.isPickerClick(e.target)) return;

    // Always set these states on mousedown
    setIsSelecting(true);
    setSelectedText(null);
  }, [selectionService]);

  const handleMouseUp = useCallback((e) => {
    // Don't check isSelecting here, just check if we have a valid selection
    if (selectionService.isPickerClick(e.target)) {
      return;
    }

    const selection = window.getSelection();
    const selectionData = selectionService.createSelection(selection);

    // Important: Set isSelecting to false first, then set selectedText
    // This ensures we don't get into a state where isSelecting is false
    // but selectedText is still null during the state update
    setIsSelecting(false);

    if (selectionData?.text) {
      setSelectedText(selectionData);
    }
  }, [selectionService]);

  return {
    selectedText,
    setSelectedText,
    isSelecting,
    handleMouseDown,
    handleMouseUp
  };
}
