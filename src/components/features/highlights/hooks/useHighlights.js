import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { HighlightService } from '@/components/features/highlights';

/**
 * Hook for managing highlights state and operations
 * @param {string} nodeId - The ID of the node to manage highlights for
 * @returns {Object} Highlight state and operations
 */
export function useHighlights(nodeId) {
  const [highlights, setHighlights] = useState([]);
  const { data: session } = useSession();

  // Load highlights on mount or when nodeId changes
  useEffect(() => {
    if (session?.user && nodeId) {
      const loadHighlights = async () => {
        try {
          const loadedHighlights = await HighlightService.getHighlights(nodeId);
          setHighlights(loadedHighlights);
        } catch (error) {
          console.error('Failed to load highlights:', error);
          setHighlights([]);
        }
      };
      loadHighlights();
    }
  }, [session?.user, nodeId]);

  /**
   * Adds a new highlight
   * @param {Object} highlight - The highlight to add
   * @returns {Promise<Object>} The added highlight
   */
  const addHighlight = useCallback(async (highlight) => {
    try {
      const result = await HighlightService.addHighlight(highlight);
      setHighlights(prev => [...prev, result]);
      return result;
    } catch (error) {
      console.error('Failed to add highlight:', error);
      throw error;
    }
  }, []);

  /**
   * Updates an existing highlight
   * @param {Object} highlight - The updated highlight data
   * @returns {Promise<Object>} The updated highlight
   */
  const updateHighlight = useCallback(async (highlight) => {
    try {
      const updatedHighlight = await HighlightService.updateHighlight(highlight);
      setHighlights(prev =>
        prev.map(h => h.id === highlight.id ? updatedHighlight : h)
      );
      return updatedHighlight;
    } catch (error) {
      console.error('Failed to update highlight:', error);
      throw error;
    }
  }, []);

  /**
   * Removes a highlight by ID
   * @param {string} highlightId - The ID of the highlight to remove
   * @returns {Promise<boolean>} True if successful
   */
  const removeHighlight = useCallback(async (highlightId) => {
    try {
      await HighlightService.removeHighlight(highlightId);
      setHighlights(prev => prev.filter(h => h.id !== highlightId));
      return true;
    } catch (error) {
      console.error('Failed to remove highlight:', error);
      throw error;
    }
  }, []);

  return {
    highlights,
    addHighlight,
    updateHighlight,
    removeHighlight
  };
}
