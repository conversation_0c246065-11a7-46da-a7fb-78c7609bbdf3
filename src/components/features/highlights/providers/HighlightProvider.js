'use client';

import { createContext, useContext } from 'react';
import { useHighlights } from '@/components/features/highlights/hooks/useHighlights';

const HighlightContext = createContext(null);

export function HighlightProvider({ children }) {
  const highlightState = useHighlights();

  return (
    <HighlightContext.Provider value={highlightState}>
      {children}
    </HighlightContext.Provider>
  );
}

// Export the context hook for internal use
export const useHighlightContext = () => {
  const context = useContext(HighlightContext);
  if (!context) {
    throw new Error('useHighlightContext must be used within a HighlightProvider');
  }
  return context;
};
