// Core functionality
export { Highlight } from './models/Highlight';
export { HighlightService } from './services/HighlightService';
export { ContentProcessingService } from './services/ContentProcessingService';
export { TextSelectionService } from './services/TextSelectionService';

// Provider and hooks
export { HighlightProvider } from './providers/HighlightProvider';
export { useTextSelection } from './hooks/useTextSelection';
export { useHighlights } from './hooks/useHighlights';

// UI Components
export { HighlightPicker } from './components/HighlightPicker';
export { ColorButton } from './components/ColorButton';
export { HighlightContent } from './components/HighlightContent';

// Constants
export { PICKER_CONSTANTS, HIGHLIGHT_COLORS } from './constants';
