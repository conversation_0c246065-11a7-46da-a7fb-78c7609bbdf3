export const runtime = "nodejs";

import { mongoClient } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export class HighlightStore {
  static HIGHLIGHTS_COLLECTION = 'highlights';

  /**
   * Retrieves the MongoDB collection for highlights.
   * @returns {Promise<Collection>} A promise that resolves to the highlights collection.
   * @private
   */
  static async highlightsCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.HIGHLIGHTS_COLLECTION);
  }

  /**
   * Retrieves all highlights for a specific user.
   * @param {string} userId - The ID of the user whose highlights are to be fetched.
   * @returns {Promise<Object[]>} A promise that resolves to an array of highlight objects.
   * @throws Will throw an error if the fetch operation fails.
   */
  static async getHighlightsForUser(userId) {
    try {
      const highlightsCollection = await this.highlightsCollection();
      const highlights = await highlightsCollection
        .find({ userId: new ObjectId(userId) })
        .toArray();
      return highlights;
    }
    catch (error) {
      console.error('Error getting highlights:', error);
      throw error;
    }
  }

  /**
   * Retrieves all highlights that have matches for a specific node.
   * @param {string} nodeId - The ID of the node whose highlights are to be fetched.
   * @param {string} userId - The ID of the user to whom the highlights belong.
   * @returns {Promise<Object[]>} A promise that resolves to an array of highlight objects.
   */
  static async getHighlightsForNode(nodeId, userId) {
    try {
      const highlightsCollection = await this.highlightsCollection();
      const highlights = await highlightsCollection.find({
        'matches.nodeId': nodeId,
        userId: new ObjectId(userId)
      }).toArray();
      return highlights;
    }
    catch (error) {
      console.error('Error getting highlights for node:', error);
      throw error;
    }
  }

  /**
   * Creates a new highlight for a specific user.
   * @param {Object} highlightJSON - The highlight object to create.
   * @param {string} userId - The ID of the user to whom the highlight belongs.
   * @returns {Promise<Object>} A promise that resolves to the created highlight object.
   * @throws Will throw an error if the create operation fails.
   */
  static async addHighlight(highlightJSON, userId) {
    try {
      const highlightsCollection = await this.highlightsCollection();
      // Create a clean copy of the highlight data
      const highlightData = {
        ...highlightJSON,
        userId: new ObjectId(userId)
      };

      const result = await highlightsCollection.insertOne(highlightData);
      const insertedHighlight = await highlightsCollection.findOne({ _id: result.insertedId });
      return insertedHighlight;
    }
    catch (error) {
      console.error('Error adding highlight:', error);
      throw error;
    }
  }

  /**
   * Updates an existing highlight for a specific user.
   * @param {Object} updatedHighlightJSON - The updated highlight object.
   * @param {string} userId - The ID of the user to whom the highlight belongs.
   * @returns {Promise<Object>} A promise that resolves to the updated highlight object.
   * @throws Will throw an error if the update operation fails.
   */
  static async updateHighlight(updatedHighlightJSON, userId) {
    try {
      const highlightsCollection = await this.highlightsCollection();

      // Create a clean copy of the highlight without the _id to avoid updating it
      const { _id, ...updateData } = updatedHighlightJSON;

      const result = await highlightsCollection.updateOne(
        { _id: new ObjectId(_id), userId: new ObjectId(userId) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return null;
      }

      // Fetch and return the updated document
      const freshlyUpdatedHighlight = await highlightsCollection.findOne({
        _id: new ObjectId(_id),
        userId: new ObjectId(userId)
      });

      return freshlyUpdatedHighlight;
    }
    catch (error) {
      console.error('Error updating highlight:', error);
      throw error;
    }
  }

  /**
   * Removes a highlight for a specific user.
   * @param {string} highlightId - The ID of the highlight to remove.
   * @param {string} userId - The ID of the user to whom the highlight belongs.
   * @throws Will throw an error if the remove operation fails.
   */
  static async removeHighlight(highlightId, userId) {
    try {
      const highlightsCollection = await this.highlightsCollection();
      const result = await highlightsCollection.deleteOne({
        _id: new ObjectId(highlightId),
        userId: new ObjectId(userId)
      });
      return result;
    }
    catch (error) {
      console.error('Error removing highlight:', error);
      throw error;
    }
  }
}
