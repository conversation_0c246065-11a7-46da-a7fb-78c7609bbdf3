// Responsibilities:
// - Get all highlights from localStorage
// - Get highlights for a node
// - Create a highlight
// - Update a highlight
// - Remove a highlight
// - Private: Save highlights to localStorage

import { Highlight } from '@/components/features/highlights'

export class HighlightStore {
  constructor() {
    this.HIGHLIGHTS_KEY = 'highlights';
  }

  // Public methods
  // ------------------------------------------------------------ 

  // Get all highlights
  getHighlightsForUser() {
    if (typeof window === 'undefined') return [];
    try {
      const savedData = localStorage.getItem(this.HIGHLIGHTS_KEY);
      return savedData ? Highlight.deserialize(savedData) : [];
    } catch (error) {
      console.error('Failed to load highlights:', error);
      return [];
    }
  }

  getHighlightsForNode(nodeId) {
    const highlights = this.getHighlightsForUser();
    return highlights.filter(h => h.nodeId === nodeId);
  }

  createHighlight(highlight) {
    const highlights = this.getHighlightsForUser();
    const newHighlights = [...highlights, highlight];
    this._saveHighlightsToLocalStorage(newHighlights);
    return highlight;
  }

  updateHighlight(updatedHighlight) {
    const highlights = this.getHighlightsForUser();
    const highlight = highlights.find(h => h.id === updatedHighlight.id);
    if (!highlight) return null;
    
    const newHighlights = highlights.map(h => 
      h.id === updatedHighlight.id ? updatedHighlight : h
    );
    this._saveHighlightsToLocalStorage(newHighlights);
    return updatedHighlight;
  }

  removeHighlight(highlightId) {
    const highlights = this.getHighlightsForUser();
    const newHighlights = highlights.filter(h => h.id !== highlightId);
    this._saveHighlightsToLocalStorage(newHighlights);
  }

  // Private methods
  // ------------------------------------------------------------ 

  _saveHighlightsToLocalStorage(highlights) {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(this.HIGHLIGHTS_KEY, Highlight.serialize(highlights));
    } catch (error) {
      console.error('Failed to save highlights:', error);
    }
  }
} 