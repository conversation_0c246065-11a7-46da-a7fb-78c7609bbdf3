Highlight feature requirements:

 1. Purpose of the feature:
 - Allow users to highlight text in a document and save the highlights for later use.
 - This helps remind them of key passages in a statute.

 2. Highlight creation:
 - To create a highlight, the user should be able to click and drag over the text they want to highlight. After selection, present the highlight picker centered above the selected text. The picker will have a highlight icon and buttons for each color. Colors in the picker represent the color of the highlight. A highlight is not created until the user selects a color. Upon selecting a color, the highlight is created, saved to local storage, the text selection is removed, the picker is hidden, and the highlight is displayed on the page.
 - these highlights can appear anywhere within node text for all of the node types: Code, Title, Subtitle, Chapter, Subchapter, Section, Subsection, Paragraph, Subparagraph.
 - The highlight could be a single character, word, phrase, paragraph, portion of sentence or paragraph, or entire paragraph or section or subsection. To the user, it shouldn't matter where the highlights is in the text, it should just be a highlight.
 - There can be multiple highlights in a section. These highlights could potentially overlap, intersect, and be in different colors.
 - Highlight colors are currently hardcoded to yellow, pink, blue, orange, green, and purple. The colors should have some transparency to them so that the user can see the text underneath the highlight.

 3. Highlight editing/removal:
 - Hovering on an existing highlight should display the highlight picker centered above the highlight.
 - The current highlight color should be displayed with a selection ring around the color and an 'X' icon in the center of the color. The 'X' icon should be clickable and should remove the highlight.
 - When removing a highlight, remove from localStorage, remove the highlight display, and the highlight picker should be closed.
 - Selecting a color from the highlight picker should update the existing highlight with the new selected color and then close the picker.

 4. Highlight Picker behavior:
 - The highlight picker should be visible: 1) after the user clicks and drags over the text they want to highlight; 2) when the user hovers over an existing highlight.
 - The highlight picker should be hidden: 1) when the user clicks outside of the picker; 2) the user scrolls while the picker is open; 3) the user selects a color; or 4) the user clicks the "x" button in the picker to remove the highlight.
 - After hovering on a highlight, the user should be able to move the mouse into the picker without hiding the picker.
 - By default, the picker is centered above the selected text or the hovered highlight. If the highlight picker can't fit in the viewport above the selected text or hovered highlight, then the picker should be positioned below the selected text or hovered highlight and the caret should point up.
 - The Highlight picker contains a caret icon in the center of the picker. This caret points down when the picker is centered above the selected text and points up when the picker is centered above the hovered highlight.

5. Overlapping highlights:
 - If two highlights overlap, the highlights should be displayed on top of each other. The highlight that was created last should be displayed on top of the other highlights.
 - TODO: When hovering over an overlapping highlight, [TBD]

6. Search highlights:
 - Automatically display highlights for an active search query term.
 - These highlight aren't not removable or editable. Only when the search query is removed should the highlights disappear.

7. Highlight storage:
 - Highlights are stored in mongoDB 'highlights' collection per user.
 - Each highlight has a unique id, a nodeId, a startOffset, an endOffset, a color, and a text. Text might not be needed to be stored.

8. Future features:
 - Add a notes field to the highlight picker.
