import { Model } from '@/lib/models/Model';
import { validate, schemas } from '@/lib/models/validation';

const DEFAULT_HIGHLIGHT_COLOR = 'yellow';

export class Highlight extends Model {
  /**
   * Validates highlight data using the centralized validation schema
   * @param {Object} data - The highlight data to validate
   * @throws {Error} If the data is invalid
   */
  static validate(data) {
    if (!data) {
      throw new Error('Highlight data is required');
    }

    try {
      // Use the centralized validation schema
      validate(data, schemas.highlight);

      // Additional validation for matches array
      if (!Array.isArray(data.matches) || data.matches.length === 0) {
        throw new Error('Highlight must have at least one match');
      }

      // Validate each match
      data.matches.forEach(match => {
        if (!match || typeof match !== 'object') {
          throw new Error('Each match must be an object');
        }

        const { startOffset, endOffset, nodeId } = match;

        if (typeof startOffset !== 'number' || startOffset < 0) {
          throw new Error('startOffset must be a non-negative number');
        }

        if (typeof endOffset !== 'number' || endOffset <= startOffset) {
          throw new Error('endOffset must be greater than startOffset');
        }

        if (typeof nodeId !== 'string' || !nodeId.trim()) {
          throw new Error('nodeId must be a non-empty string');
        }
      });

      return true;
    } catch (error) {
      // Enhance the error message for better debugging
      throw new Error(`Invalid highlight data: ${error.message}`);
    }
  }

  constructor(data = {}) {
    super();

    // Extract and validate the data
    const { id, _id, color = DEFAULT_HIGHLIGHT_COLOR, matches = [], ...rest } = data;

    // The matches array allows a single highlight to span multiple nodes.
    // Each match represents a range (startOffset, endOffset) on a specific nodeId.
    // This is intentional: it enables a single highlight to cover text across multiple nodes,
    // so that editing, coloring, or deleting the highlight acts as a single logical unit.
    // Do not refactor to a flat model unless you want to lose multi-node highlight support.

    // Ensure matches is an array and has the correct structure
    const processedMatches = Array.isArray(matches)
      ? matches
          .map(match => {
            if (!match) return null;

            // Ensure all required fields are present and properly typed
            const processedMatch = {
              startOffset: Number(match.startOffset) || 0,
              endOffset: Number(match.endOffset) || 0,
              nodeId: String(match.nodeId || ''),
              color: typeof match.color === 'string' ? match.color : color
            };

            // Skip invalid matches
            try {
              if (processedMatch.startOffset >= processedMatch.endOffset) {
                throw new Error('startOffset must be less than endOffset');
              }
              if (!processedMatch.nodeId) {
                throw new Error('nodeId is required');
              }
              return processedMatch;
            } catch (error) {
              console.warn('Invalid match data:', error.message, match);
              return null;
            }
          })
          .filter(Boolean)
      : [];

    // Ensure we have at least one valid match
    if (processedMatches.length === 0) {
      throw new Error('Highlight must have at least one valid match');
    }

    // Set the properties
    this.id = id || _id || null;
    this.color = typeof color === 'string' ? color : DEFAULT_HIGHLIGHT_COLOR;
    this.matches = processedMatches;

    // Store any additional properties
    Object.assign(this, rest);

    // Validate the complete object
    this.constructor.validate(this);
  }

  /**
   * Validates the current highlight instance
   * @throws {Error} If validation fails
   */
  validate() {
    return this.constructor.validate(this);
  }

  /**
   * Converts the highlight to a plain object
   * @returns {Object} Plain object representation with _id and matches
   */
  toPlainObject() {
    return {
      _id: this._id || undefined,
      matches: this.matches.map(match => ({
        startOffset: match.startOffset,
        endOffset: match.endOffset,
        nodeId: match.nodeId,
        color: match.color
      }))
    };
  }

  static createFromSelection(selection, color, nodeId, context) {
    const { matches, text } = selection;

    if (!matches?.length) {
      throw new Error('Invalid selection data: no matches provided');
    }

    if (!text) {
      throw new Error('Invalid selection data: no text provided');
    }

    // Determine if all matches are from subsections
    const allMatchesAreSubsections = matches.every(match =>
      match.nodeId.includes('subsection') || match.nodeId.includes('(')
    );

    // Use parent section's nodeId for subsection matches, otherwise use current nodeId
    const highlightNodeId = allMatchesAreSubsections ?
      context?.parentId :
      nodeId;

    // Ensure matches have all required fields and include color
    const validMatches = matches.map(match => ({
      startOffset: match.startOffset,
      endOffset: match.endOffset,
      nodeId: match.nodeId || highlightNodeId,
      color: match.color || color // Use match color if provided, otherwise use the highlight color
    }));

    return new Highlight({
      color,
      nodeId: highlightNodeId,
      matches: validMatches
    });
  }

  update(changes) {
    return new Highlight({
      ...this,
      ...changes
    });
  }

  // Check if this highlight overlaps with another
  overlaps(other) {
    return this.matches.some(match1 =>
      other.matches.some(match2 =>
        match1.nodeId === match2.nodeId &&
        !(match1.endOffset <= match2.startOffset ||
          match1.startOffset >= match2.endOffset)
      )
    );
  }

  // Check if this highlight contains a specific text position
  containsPosition(nodeId, offset) {
    return this.matches.some(match =>
      match.nodeId === nodeId &&
      offset >= match.startOffset &&
      offset < match.endOffset
    );
  }

  // Get all unique nodeIds from matches
  getNodeIds() {
    return [...new Set(this.matches.map(m => m.nodeId))];
  }
}
