.highlight-container {
  position: relative;
}

.highlight-span {
  cursor: pointer;
  display: inline;
  position: relative;
}

.highlight-span[data-highlight-id] {
  z-index: 2;
}

.highlight-span:not([data-highlight-id]) {
  z-index: 1;
}

a.statute-reference {
  z-index: 3;
  position: relative;
}

.highlight-picker {
  position: absolute;
  pointer-events: auto;
}

.highlight-span:hover {
  filter: brightness(0.95);
}

.highlight-span.yellow {
  background-color: rgba(255, 255, 0, 0.4);
}

.highlight-span.green {
  background-color: rgba(0, 255, 0, 0.4);
}

.highlight-span.blue {
  background-color: rgba(0, 191, 255, 0.4);
}

.highlight-span.pink {
  background-color: rgba(255, 192, 203, 0.4);
}

.highlight-span.purple {
  background-color: rgba(147, 112, 219, 0.4);
}

.highlight-span.orange {
  background-color: rgba(255, 165, 0, 0.4);
}

.highlight-content {
  position: relative;
  isolation: isolate;
  /* No white-space property - completely normal text behavior */
}

/* For content that appears to be tabular - preserves all whitespace including tabs and wraps */
.highlight-content.has-tabs {
  white-space: pre-wrap;
  tab-size: 8;
  font-family: 'Courier New', 'Monaco', monospace;
  padding: 0.25rem 0;
  padding-left: 0 !important;
  border-radius: 4px;
  margin: 0.25rem 0;
  overflow-x: auto;
  text-indent: 0 !important;
}

/* Ensure parent containers don't add indentation to tabular content */
.section-text .highlight-content.has-tabs,
.subsection-level-1 .highlight-content.has-tabs,
.subsection-level-2 .highlight-content.has-tabs,
.subsection-level-3 .highlight-content.has-tabs,
.subsection-level-4 .highlight-content.has-tabs,
.subsection-level-5 .highlight-content.has-tabs {
  padding-left: 0 !important;
  text-indent: 0 !important;
  margin-left: 0 !important;
}

.tabular-content {
  white-space: pre;
  tab-size: 8;
  font-family: 'Courier New', 'Monaco', monospace;
  overflow-x: auto;
  background-color: rgba(248, 249, 250, 0.8);
  padding: 0.75rem;
  border-radius: 6px;
  margin: 0.75rem 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.9em;
  line-height: 1.4;
}

.selectable-text {
  position: relative;
  display: inline;
}

.selectable-text span {
  position: relative;
  z-index: 1;
}

.statute-content-picker-container {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 9999;
}

.highlight-color-picker {
  pointer-events: auto;
}

/* Search highlight styles */
.search-highlight {
  background-color: rgba(255, 255, 0, 0.4);
  padding: 0.1em 0;
  border-radius: 2px;
  position: relative;
  z-index: 4; /* Above references (3) */
}

.search-highlight:hover {
  background-color: rgba(255, 255, 0, 0.5);
}

/* ================================================ */
/* Statute reference styles */
/* ================================================ */
.statute-reference {
  color: var(--text-color);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0 0.1em;
  border-radius: 2px;
  border-bottom: 1px dotted var(--accent-color);
}

.statute-reference:hover {
  background-color: rgba(37, 99, 235, 0.1); /* Light blue background on hover */
}

.statute-reference.unresolved {
  color: var(--text-color);
  border-bottom: 1px dotted var(--error-color);
}

.statute-reference.unresolved:hover {
  background-color: rgba(255, 0, 0, 0.1);
}
