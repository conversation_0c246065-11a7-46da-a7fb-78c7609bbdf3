import React from 'react';
import { X as XIcon, Plus as PlusIcon } from 'lucide-react';
import PropTypes from 'prop-types';
import styles from './HighlightPicker.module.css';

export const ColorButton = ({ color, isSelected, onClick, onDelete }) => {
  return (
    <div className="relative group flex items-center justify-center h-8">
      <button
        className={`
          ${styles.colorButton}
          ${styles[color.class]}
          ${isSelected ? `${styles.selected} ${styles[color.selectedClass]}` : ''}
          relative
        `}
        onMouseDown={(e) => {
          // Prevent selection clearing
          e.preventDefault();
          e.stopPropagation();
        }}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onClick(color);
        }}
        title={`Highlight ${color.name}`}
      >
        {isSelected && onDelete && (
          <div
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className={`
              absolute inset-0 flex items-center justify-center
              opacity-100 hover:bg-gray-500/20 rounded-full
              transition-all duration-200
            `}
            title="Remove highlight"
          >
            <XIcon size={14} className="text-gray-600" />
          </div>
        )}
        {!isSelected && (
          <span className="absolute inset-0 flex items-center justify-center pointer-events-none group-hover:opacity-100 opacity-0 transition-opacity duration-150">
            <PlusIcon size={16} style={{ color: 'rgba(255,255,255,0.7)' }} />
          </span>
        )}
      </button>
    </div>
  );
};

ColorButton.propTypes = {
  color: PropTypes.shape({
    name: PropTypes.string.isRequired,
    class: PropTypes.string.isRequired,
    selectedClass: PropTypes.string.isRequired
  }).isRequired,
  isSelected: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func
};
