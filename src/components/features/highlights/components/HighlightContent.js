'use client';

import React, { useRef, useCallback, useMemo, useState, useEffect } from 'react';
import { HighlightPicker, ContentProcessingService } from '@/components/features/highlights';
import { useHighlights, useTextSelection } from '@/components/features/highlights';
import { useSearch } from '@/components/features/search';
import PropTypes from 'prop-types';
import { ContentRenderer } from '@/components/features/highlights/services/ContentRenderer';
import { Highlight } from '@/components/features/highlights';
import { ReferenceEditModal } from '@/components/features/references/components/ReferenceEditModal';
import './Highlight.css';
import { useQuery } from '@tanstack/react-query';
import { HighlightService } from '@/components/features/highlights/services/HighlightService';
import { ReferenceService } from '@/components/features/references/services/ReferenceService';
import { useStatuteContent } from '@/components/features/statutes/hooks/useStatuteContent';

export function HighlightContent({
  children,
  node,
  elementType = 'span'
}) {
  // Memoize the nodeId and type to prevent unnecessary re-renders
  const nodeId = useMemo(() => node?.nodeId, [node?.nodeId]);
  const nodeType = useMemo(() => node?.type, [node?.type]);
  const { searchQuery } = useSearch();

  // Check if content has tabs (indicating tabular data)
  const hasTabs = useMemo(() => {
    if (typeof children !== 'string') return false;
    return /\t/.test(children);
  }, [children]);
  const {
    addHighlight,
    updateHighlight,
    removeHighlight
  } = useHighlights(nodeId);

  const contentRef = useRef(null);
  const pickerRef = useRef(null);

  const {
    selectedText,
    setSelectedText,
    handleMouseDown,
    handleMouseUp
  } = useTextSelection();

  // Hover state management
  const [hoveredHighlight, setHoveredHighlight] = useState(null);

  // State to hold processed content - initialize with children to show something initially
  const [processedContent, setProcessedContent] = useState(children);

  // Reference modal state
  const [referenceModal, setReferenceModal] = useState(null);

  // Fetch highlights and references for this node - CONSERVATIVE MEMORY MANAGEMENT
  const { data: highlights = [] } = useQuery({
    queryKey: ['highlights', nodeId],
    queryFn: () => HighlightService.getHighlights(nodeId),
    enabled: !!nodeId,
    staleTime: 0, // No stale time but allow loading
    gcTime: 2 * 60 * 1000, // 2 minutes to allow proper loading
  });
  const { data: references = [] } = useQuery({
    queryKey: ['references', nodeId],
    queryFn: () => ReferenceService.getReferences(nodeId),
    enabled: !!nodeId,
    staleTime: 0, // No stale time but allow loading
    gcTime: 2 * 60 * 1000, // 2 minutes to allow proper loading
  });

  const { refetch } = useStatuteContent();

  const getHighlightRect = useCallback((highlightId) => {
    if (!highlightId) {
      console.warn('getHighlightRect called with null or undefined highlightId');
      return null;
    }

    const highlightElement = document.querySelector(`[data-highlight-id="${highlightId}"]`);
    if (!highlightElement) {
      console.warn(`No element found for highlight ID: ${highlightId}`);
      return null;
    }

    try {
      const rect = highlightElement.getBoundingClientRect();
      if (!rect || typeof rect.left !== 'number' || typeof rect.top !== 'number' ||
          rect.width === 0 || rect.height === 0) {
        console.warn('Invalid rect for highlight:', highlightId, rect);
        return null;
      }
      return rect;
    } catch (error) {
      console.error('Error getting bounding client rect for highlight:', highlightId, error);
      return null;
    }
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (hoveredHighlight) {
      const pickerElement = pickerRef.current;
      if (pickerElement) {
        const pickerRect = pickerElement.getBoundingClientRect();
        const mouseX = e.clientX;
        const mouseY = e.clientY;

        const isOverPicker =
          mouseX >= pickerRect.left &&
          mouseX <= pickerRect.right &&
          mouseY >= pickerRect.top &&
          mouseY <= pickerRect.bottom;

        const highlightElement = e.target.closest('.highlight-span');
        const isOverHighlight = highlightElement?.dataset.highlightId === hoveredHighlight.id;

        if (!isOverPicker && !isOverHighlight) {
          setHoveredHighlight(null);
        }
      }
    }
  }, [hoveredHighlight]);

  // Use ref for highlights to prevent circular dependency in event handlers
  const highlightsRef = useRef(highlights);
  highlightsRef.current = highlights;

  // Stable event handlers to prevent circular dependencies
  const eventHandlers = useMemo(() => {
    const handleHighlightEnter = (e) => {
      if (e.buttons === 1 || !nodeId) return; // Skip if selecting or no node

      const highlightId = e.currentTarget.dataset.highlightId;
      const highlight = highlightsRef.current.find(h => h.id === highlightId);
      if (highlight) {
        const rect = getHighlightRect(highlight.id);
        if (rect) {
          setHoveredHighlight(highlight);
        }
      }
    };

    return {
      highlight: {
        onMouseDown: handleMouseDown,
        onMouseMove: handleMouseMove,
        onMouseEnter: handleHighlightEnter
      }
    };
  }, [nodeId, handleMouseDown, handleMouseMove, getHighlightRect]);

  // Update processed content if children changes
  useEffect(() => {
    setProcessedContent(children);
  }, [children]);

  // Use ref for references to prevent circular dependency
  const referencesRef = useRef(references);
  referencesRef.current = references;

  // Memoize processing key to trigger reprocessing only when necessary
  // Include reference status to trigger reprocessing when references are resolved/updated
  const processingKey = useMemo(() => {
    const referenceHash = references.map(ref => 
      `${ref.id}-${ref.matches?.map(m => m.status).join(',') || ''}`
    ).join('|');
    return `${nodeId}-${highlights.length}-${references.length}-${referenceHash}`;
  }, [nodeId, highlights.length, references]);

  // Process content when dependencies change
  useEffect(() => {
    if (!children || selectedText || !nodeId) return;

    const renderer = new ContentRenderer(eventHandlers);
    const nodeForProcessor = {
      nodeId,
      type: nodeType,
      highlights,
      references
    };
    const contentProcessor = new ContentProcessingService(nodeForProcessor, renderer, highlights, references);
    let isMounted = true;

    async function processContent() {
      try {
        const result = await contentProcessor.processContent(children, searchQuery);
        if (isMounted) {
          setProcessedContent(result);
        }
      } catch (error) {
        console.error('Error processing content for node:', nodeId, error);
      }
    }

    processContent();
    return () => { isMounted = false; };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [children, nodeId, nodeType, searchQuery, selectedText, eventHandlers, processingKey]);

  // Create highlight when color is selected
  const handleCreateHighlight = useCallback(async (color) => {
    if (!selectedText || !nodeId) return;

    const highlight = Highlight.createFromSelection(
      selectedText,
      color,
      nodeId,
      { nodeId } // minimal node object
    );

    try {
      await addHighlight(highlight);
      // Note: We don't need to update node.addHighlight since we're using React Query
    } catch (error) {
      console.error('Failed to create highlight:', error);
    } finally {
      setSelectedText(null);
    }
  }, [addHighlight, nodeId, selectedText, setSelectedText]);

  const handleHighlightUpdate = useCallback(async (highlight) => {
    if (!highlight || !nodeId) return;
    try {
      await updateHighlight(highlight);
      // Note: We don't need to update node.highlights since we're using React Query
    } catch (error) {
      console.error('Failed to update highlight:', error);
    } finally {
      setHoveredHighlight(null);
    }
  }, [updateHighlight, nodeId]);

  const handleHighlightDelete = useCallback(async (highlight) => {
    if (!highlight || !nodeId) return;
    setHoveredHighlight(null);

    try {
      await removeHighlight(highlight.id);
      // Note: We don't need to update node.removeHighlight since we're using React Query
    } catch (error) {
      console.error('Failed to delete highlight:', error);
    }
  }, [removeHighlight, nodeId]);

  // Handler for creating a reference
  const handleReferenceCreate = useCallback(() => {
    if (!selectedText || !selectedText.rect || !nodeId) {
      return;
    }

    setReferenceModal({
      text: selectedText.text,
      startOffset: selectedText.matches[0].startOffset,
      endOffset: selectedText.matches[0].endOffset,
      nodeId: nodeId,
      targetRect: selectedText.rect
    });
  }, [selectedText, nodeId]);

  // Handler for reference save (refresh content)
  const handleReferenceSave = useCallback(() => {
    refetch();
    setReferenceModal(null);
  }, [refetch]);

  // Create the root element with proper event handlers
  const Element = elementType;

  return (
    <Element
      ref={contentRef}
      className={`highlight-content${hasTabs ? ' has-tabs' : ''}`}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      style={{
        contain: 'layout style', // Prevent layout impact from child elements
      }}
    >
      {processedContent}

      {selectedText && selectedText.rect && (
        <HighlightPicker
          ref={pickerRef}
          highlight={null}
          targetRect={selectedText.rect}
          onCreate={handleCreateHighlight}
          onReferenceCreate={handleReferenceCreate}
          onClose={() => setSelectedText(null)}
        />
      )}

      {hoveredHighlight && (() => {
        const rect = getHighlightRect(hoveredHighlight.id);
        return rect ? (
          <HighlightPicker
            ref={pickerRef}
            highlight={hoveredHighlight}
            targetRect={rect}
            onUpdate={handleHighlightUpdate}
            onDelete={handleHighlightDelete}
            onClose={() => setHoveredHighlight(null)}
          />
        ) : null;
      })()}

      {/* ReferenceEditModal rendered at top level */}
      {referenceModal && (
        <ReferenceEditModal
          open={!!referenceModal}
          onClose={() => setReferenceModal(null)}
          highlight={referenceModal}
          onSave={handleReferenceSave}
        />
      )}
    </Element>
  );
}

HighlightContent.propTypes = {
  children: PropTypes.node,
  node: PropTypes.object.isRequired,
  elementType: PropTypes.string
};
