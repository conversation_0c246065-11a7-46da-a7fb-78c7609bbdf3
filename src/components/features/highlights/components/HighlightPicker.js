'use client';

import React, { useRef, useEffect, useLayoutEffect, useState, useCallback } from 'react';
import {
  Highlight,
  ColorButton
} from '@/components/features/highlights'
import PropTypes from 'prop-types';
import { Highlighter as HighlighterIcon, Plus as PlusIcon, FileSymlink as ReferenceIcon } from 'lucide-react';
import styles from './HighlightPicker.module.css';
import ReactDOM from 'react-dom';
import { ModalPositioning } from '@/lib/ModalPositioning';
import { useSession } from 'next-auth/react';
import { isSystemAdmin } from '@/lib/models/roles';
import Link from 'next/link';

const COLORS = Object.freeze([
  Object.freeze({
    name: 'yellow',
    class: 'yellow',
    selectedClass: 'selectedYellow'
  }),
  Object.freeze({
    name: 'pink',
    class: 'pink',
    selectedClass: 'selectedPink'
  }),
  Object.freeze({
    name: 'blue',
    class: 'blue',
    selectedClass: 'selectedBlue'
  }),
  Object.freeze({
    name: 'orange',
    class: 'orange',
    selectedClass: 'selectedOrange'
  }),
  Object.freeze({
    name: 'green',
    class: 'green',
    selectedClass: 'selectedGreen'
  }),
  Object.freeze({
    name: 'purple',
    class: 'purple',
    selectedClass: 'selectedPurple'
  })
]);

export function HighlightPicker({ highlight, targetRect, onUpdate, onDelete, onCreate, onClose, onReferenceCreate }) {
  const pickerRef = useRef(null);
  const { data: session } = useSession();
  const isAdmin = isSystemAdmin(session);

  const [pickerPosition, setPickerPosition] = useState({ left: 0, top: 0, caretLeft: 160, shouldFlip: false });
  const [isPositioned, setIsPositioned] = useState(false);

  // Calculate dynamic width based on visible content
  const calculatePickerWidth = useCallback(() => {
    // Base components: highlighter icon (16px) + margin (8px) + 6 color buttons (6 * 32px) + padding (16px)
    let width = 16 + 8 + (6 * 32) + 16; // = 232px

    // Add reference section if admin AND authenticated: separator (16px) + reference icon (16px) + margin (8px) + reference button (32px)
    if (isAdmin && session) {
      width += 16 + 16 + 8 + 32; // = additional 72px, total 304px
    }

    return width;
  }, [isAdmin, session]);

  // Calculate position after mount/targetRect changes or admin status/session changes
  useLayoutEffect(() => {
    if (!targetRect) {
      setIsPositioned(false);
      return;
    }

    const width = calculatePickerWidth();
    const height = 63;

    // Calculate position with dynamic width using fixed positioning
    const position = ModalPositioning.calculatePosition(targetRect, {
      width,
      height,
      hasCaret: true,
      useFixedPosition: true
    });

    setPickerPosition(position);
    setIsPositioned(true);
  }, [targetRect, isAdmin, session, calculatePickerWidth]);

  const handleColorClick = (colorOption) => {
    // Authentication check is now handled by the overlay
    // This function will only be called when authenticated or editing existing highlights
    if (highlight) {
      highlight.color = colorOption.name;
      onUpdate(highlight);
    } else {
      // Creation mode
      onCreate(colorOption.name);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Don't handle clicks on the picker itself
      if (pickerRef.current && pickerRef.current.contains(event.target)) {
        return;
      }

      onClose?.();
    };

    // Use click for hover picker, mouseup for selection picker
    const eventType = highlight ? 'click' : 'mouseup';
    document.addEventListener(eventType, handleClickOutside);

    // Handle scroll
    const handleScroll = () => {
      onClose?.();
    };

    window.addEventListener('scroll', handleScroll, true);

    return () => {
      document.removeEventListener(eventType, handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [onClose, highlight]);

  return typeof window !== 'undefined'
    ? ReactDOM.createPortal(
      <div
        ref={pickerRef}
        className={`bg-white rounded-lg p-2 border highlight-picker min-w-[200px] pointer-events-auto ${styles.pickerContainer}`}
        style={{
          position: 'fixed',
          top: `${pickerPosition.top}px`,
          left: `${pickerPosition.left}px`,
          width: `${calculatePickerWidth()}px`,
          transform: 'none',
          zIndex: 1201,
          opacity: isPositioned ? 1 : 0, // Hide until positioned
          willChange: 'transform, opacity', // Hint to browser for optimization
          contain: 'layout style', // Allow paint overflow for caret visibility
        }}
      >
        <div className="flex items-center gap-1 p-1 relative h-8" style={{ alignItems: 'center' }}>
          <HighlighterIcon size={16} className="text-gray-400 mx-1 flex-shrink-0" title="Create a highlight"/>
          {COLORS.map(colorOption => (
            <ColorButton
              key={colorOption.name}
              color={colorOption}
              isSelected={highlight?.color === colorOption.name}
              onClick={() => handleColorClick(colorOption)}
              onDelete={highlight && onDelete ? () => onDelete(highlight) : undefined}
            />
          ))}
          {isAdmin && session && (
            <>
              {/* Visual separator */}
              <span className="mx-2 h-6 border-l border-gray-200 flex-shrink-0" />
              {/* Reference button */}
              <ReferenceIcon size={16} className="text-gray-400 mx-1 flex-shrink-0" />
              <button
                type="button"
                title="Add a reference"
                aria-label="Add a reference"
                className={styles.referenceButton}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  onReferenceCreate?.();
                }}
                tabIndex={0}
              >
                <PlusIcon size={16} className="text-blue-600" />
              </button>
            </>
          )}
          {/* Sign-in overlay for unauthenticated users in creation mode */}
          {!session && !highlight && (
            <div className="absolute inset-0 bg-white/60 backdrop-blur-[1px] rounded flex items-center justify-center text-sm z-10">
              <div className="text-gray-800 font-medium">
                Please <Link href="/api/auth/signin" className="text-blue-600 hover:underline">sign in</Link> to save highlights.
              </div>
            </div>
          )}
        </div>
        {isPositioned && (
          <div
            className={`absolute w-3 h-3 bg-white border-r border-b border-gray-200 transform rotate-45
              ${pickerPosition.shouldFlip ? '-top-1.5 rotate-[225deg]' : '-bottom-1.5 rotate-45'}
              ${styles.caret}`}
            style={{
              left: `${pickerPosition.caretLeft || (calculatePickerWidth() / 2)}px`,
              zIndex: 1202 // Ensure caret is above the picker
            }}
          />
        )}
      </div>,
      document.body
    )
    : null;
}

HighlightPicker.propTypes = {
  highlight: PropTypes.oneOfType([
    PropTypes.instanceOf(Highlight),
    PropTypes.shape({
      id: PropTypes.string,
      matches: PropTypes.arrayOf(PropTypes.shape({
        level: PropTypes.number.isRequired,
        startOffset: PropTypes.number.isRequired,
        endOffset: PropTypes.number.isRequired,
        text: PropTypes.string.isRequired
      })),
      color: PropTypes.string,
      text: PropTypes.string,
      node: PropTypes.shape({
        nodeId: PropTypes.string.isRequired
      }),
      position: PropTypes.shape({
        x: PropTypes.number.isRequired,
        y: PropTypes.number.isRequired,
        shouldFlip: PropTypes.bool
      })
    })
  ]),
  onUpdate: PropTypes.func,
  onCreate: PropTypes.func,
  onClose: PropTypes.func,
  onDelete: PropTypes.func,
  targetRect: PropTypes.shape({
    x: PropTypes.number.isRequired,
    y: PropTypes.number.isRequired,
    width: PropTypes.number.isRequired,
    height: PropTypes.number.isRequired,
    top: PropTypes.number.isRequired,
    bottom: PropTypes.number.isRequired,
    left: PropTypes.number.isRequired,
    right: PropTypes.number.isRequired
  }),
  onReferenceCreate: PropTypes.func
};
