.colorButton {
  @apply w-6 h-6 rounded-full transition-all relative inline-flex items-center justify-center;
  margin: 2px;
  vertical-align: middle;
}

.yellow {
  @apply bg-yellow-200 hover:bg-yellow-300;
}

.pink {
  @apply bg-pink-200 hover:bg-pink-300;
}

.blue {
  @apply bg-blue-200 hover:bg-blue-300;
}

.orange {
  @apply bg-orange-200 hover:bg-orange-300;
}

.green {
  @apply bg-green-200 hover:bg-green-300;
}

.purple {
  @apply bg-purple-200 hover:bg-purple-300;
}

.selected {
  @apply ring-2;
  margin: 0;
  border: 2px solid white;
}

.selectedYellow {
  @apply ring-yellow-400;
}

.selectedPink {
  @apply ring-pink-400;
}

.selectedBlue {
  @apply ring-blue-400;
}

.selectedOrange {
  @apply ring-orange-400;
}

.selectedGreen {
  @apply ring-green-400;
}

.selectedPurple {
  @apply ring-purple-400;
}

.pickerContainer {
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  pointer-events: auto;
  opacity: 0;
  animation: fadeIn 0.15s ease forwards;
  width: auto;
  min-width: fit-content;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.caret {
  box-shadow: 2px 2px 2px rgba(0,0,0,0.05);
  margin-bottom: -1px;
  transition: transform 0.15s ease;
}

.referenceButton {
  @apply w-6 h-6 rounded-full flex items-center justify-center transition-all relative bg-white flex-shrink-0;
  border: 2px dotted #3b82f6; /* blue-500 */
  margin: 2px;
}
.referenceButton:focus, .referenceButton:hover {
  outline: none;
  border-color: #2563eb; /* blue-600 */
  box-shadow: 0 0 0 2px #bfdbfe; /* blue-200 */
  background-color: #dbeafe; /* blue-100 */
}
