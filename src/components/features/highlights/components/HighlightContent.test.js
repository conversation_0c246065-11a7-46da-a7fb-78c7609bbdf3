import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { HighlightContent } from './HighlightContent';
import { Node } from '@/components/features/statutes/models/Node';
import { Highlight } from '@/components/features/highlights/models/Highlight';
import { useSearch } from '@/components/features/search';

// Mock the hooks individually to avoid circular dependencies
jest.mock('@/components/features/highlights/hooks/useHighlights', () => ({
  useHighlights: jest.fn()
}));

jest.mock('@/components/features/highlights/hooks/useTextSelection', () => ({
  useTextSelection: jest.fn()
}));

jest.mock('@/components/features/search', () => ({
  useSearch: jest.fn()
}));

// Mock React Query
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(() => ({
    data: [],
    isLoading: false,
    isError: false
  }))
}));

// Mock the useStatuteContent hook
jest.mock('@/components/features/statutes/hooks/useStatuteContent', () => ({
  useStatuteContent: jest.fn(() => ({
    refetch: jest.fn()
  }))
}));

// Mock the HighlightPicker component
jest.mock('@/components/features/highlights/components/HighlightPicker', () => ({
  HighlightPicker: React.forwardRef(function HighlightPicker({ onClose }, ref) {
    return (
      <div ref={ref} role="dialog" data-testid="highlight-picker">
        <button onClick={onClose}>Close</button>
      </div>
    );
  })
}));

// Mock the ReferenceEditModal component
jest.mock('@/components/features/references/components/ReferenceEditModal', () => ({
  ReferenceEditModal: ({ open, onClose }) => 
    open ? <div data-testid="reference-edit-modal"><button onClick={onClose}>Close</button></div> : null
}));

// Mock ContentProcessingService
jest.mock('@/components/features/highlights/services/ContentProcessingService', () => ({
  ContentProcessingService: jest.fn().mockImplementation(() => ({
    processContent: jest.fn().mockResolvedValue('Processed content')
  }))
}));

// Mock ContentRenderer
jest.mock('@/components/features/highlights/services/ContentRenderer', () => ({
  ContentRenderer: jest.fn().mockImplementation(() => ({}))
}));

// Import the mocked hooks
import { useHighlights } from '@/components/features/highlights/hooks/useHighlights';
import { useTextSelection } from '@/components/features/highlights/hooks/useTextSelection';

// Mock getBoundingClientRect
const mockGetBoundingClientRect = jest.fn(() => ({
  left: 100,
  top: 100,
  width: 100,
  height: 20
}));

Element.prototype.getBoundingClientRect = mockGetBoundingClientRect;

// Mock IntersectionObserver
class IntersectionObserver {
  constructor(callback) {
    this.callback = callback;
  }

  observe() {}
  unobserve() {}
  disconnect() {}
}

global.IntersectionObserver = IntersectionObserver;

// Mock window.getSelection
window.getSelection = () => ({
  toString: () => '',
  removeAllRanges: () => {},
  addRange: () => {},
  getRangeAt: () => ({
    getBoundingClientRect: () => ({
      top: 0,
      left: 0,
      width: 100,
      height: 20
    }),
    startContainer: document.createElement('div'),
    endContainer: document.createElement('div'),
    startOffset: 0,
    endOffset: 4
  })
});

// Mock ResizeObserver
class ResizeObserver {
  constructor(callback) {
    this.callback = callback;
  }

  observe() {}
  unobserve() {}
  disconnect() {}
}

global.ResizeObserver = ResizeObserver;

// Mock requestAnimationFrame
global.requestAnimationFrame = callback => setTimeout(callback, 0);
global.cancelAnimationFrame = id => clearTimeout(id);

describe('HighlightContent', () => {
  const mockNode = new Node({
    id: 'test-id',
    nodeId: 'test/node/id',
    type: 'subsection',
    text: 'Test content'
  });

  const mockHighlight = new Highlight({
    id: 'highlight-1',
    color: 'yellow',
    matches: [{
      startOffset: 0,
      endOffset: 4,
      nodeId: mockNode.nodeId
    }]
  });

  const mockHighlightHandlers = {
    addHighlight: jest.fn(),
    updateHighlight: jest.fn(),
    removeHighlight: jest.fn()
  };

  const mockTextSelection = {
    selectedText: null,
    setSelectedText: jest.fn(),
    handleMouseDown: jest.fn(),
    handleMouseUp: jest.fn()
  };

  const defaultProps = {
    children: 'Test content',
    node: mockNode
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useHighlights.mockReturnValue(mockHighlightHandlers);
    useTextSelection.mockReturnValue(mockTextSelection);
    useSearch.mockReturnValue({ searchQuery: '' });
    mockNode.highlights = [];
  });

  it('renders content correctly', () => {
    render(<HighlightContent {...defaultProps} />);
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('renders with custom element type', () => {
    render(<HighlightContent {...defaultProps} elementType="div" />);
    expect(screen.getByText('Test content').tagName).toBe('DIV');
  });

  describe('Highlight Management', () => {
    it('calls addHighlight when highlight is created', async () => {
      const mockTextSelectionWithText = {
        ...mockTextSelection,
        selectedText: {
          text: 'Test',
          matches: [{
            startOffset: 0,
            endOffset: 4,
            nodeId: mockNode.nodeId
          }],
          rect: mockGetBoundingClientRect()
        }
      };

      useTextSelection.mockReturnValue(mockTextSelectionWithText);

      render(<HighlightContent {...defaultProps} />);

      // Check that HighlightPicker is rendered when there's selected text
      expect(screen.getByTestId('highlight-picker')).toBeInTheDocument();

      // The actual addHighlight call would happen in the HighlightPicker component
      // Here we just verify the hook is called correctly
      expect(useHighlights).toHaveBeenCalledWith(mockNode.nodeId);
    });

    it('calls updateHighlight when highlight is modified', async () => {
      render(<HighlightContent {...defaultProps} />);

      await act(async () => {
        const updatedHighlight = new Highlight({
          ...mockHighlight,
          color: 'green'
        });
        await mockHighlightHandlers.updateHighlight(updatedHighlight);
      });

      expect(mockHighlightHandlers.updateHighlight).toHaveBeenCalled();
    });

    it('calls removeHighlight when highlight is deleted', async () => {
      render(<HighlightContent {...defaultProps} />);

      await act(async () => {
        await mockHighlightHandlers.removeHighlight(mockHighlight.id);
      });

      expect(mockHighlightHandlers.removeHighlight).toHaveBeenCalledWith(mockHighlight.id);
    });
  });

  describe('Mouse Interactions', () => {
    it('shows highlight picker on text selection', () => {
      const mockTextSelectionWithText = {
        ...mockTextSelection,
        selectedText: {
          text: 'Test',
          matches: [{
            startOffset: 0,
            endOffset: 4,
            nodeId: mockNode.nodeId
          }],
          rect: mockGetBoundingClientRect()
        }
      };

      useTextSelection.mockReturnValue(mockTextSelectionWithText);

      render(<HighlightContent {...defaultProps} />);

      expect(screen.getByTestId('highlight-picker')).toBeInTheDocument();
    });

    it('calls mouse event handlers', () => {
      render(<HighlightContent {...defaultProps} />);

      const contentElement = screen.getByText('Test content');
      
      fireEvent.mouseDown(contentElement);
      fireEvent.mouseUp(contentElement);

      expect(mockTextSelection.handleMouseDown).toHaveBeenCalled();
      expect(mockTextSelection.handleMouseUp).toHaveBeenCalled();
    });

    it('does not show highlight picker when no text selected', () => {
      render(<HighlightContent {...defaultProps} />);

      expect(screen.queryByTestId('highlight-picker')).not.toBeInTheDocument();
    });
  });

  describe('Search Integration', () => {
    it('uses search query in processing', () => {
      useSearch.mockReturnValue({ searchQuery: 'Test' });

      render(<HighlightContent {...defaultProps} />);

      // Verify the component renders and uses the search query
      expect(useSearch).toHaveBeenCalled();
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing node gracefully', () => {
      render(
        <HighlightContent node={null}>
          Test content
        </HighlightContent>
      );
      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    it('handles highlight creation failure', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockHighlightHandlers.addHighlight.mockRejectedValue(new Error('Failed to add'));

      render(<HighlightContent {...defaultProps} />);

      await act(async () => {
        try {
          await mockHighlightHandlers.addHighlight(mockHighlight);
        } catch {
          // Expected error
        }
      });

      expect(mockHighlightHandlers.addHighlight).toHaveBeenCalled();
      consoleError.mockRestore();
    });
  });
});
