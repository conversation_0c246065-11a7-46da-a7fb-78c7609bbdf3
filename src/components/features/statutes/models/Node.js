import { Model } from '@/lib/models/Model';
import { validate } from '@/lib/models/validation';
import { Highlight } from '@/components/features/highlights/models/Highlight';
import { Reference } from '@/components/features/references/models/Reference';

export class Node extends Model {
  static VALID_TYPES = [
    'root',
    'collection',
    'code',
    'title',
    'subtitle',
    'chapter',
    'subchapter',
    'article',
    'subarticle',
    'section',
    'subsection',
    'subdivision',
    'subparagraph',
    'subpart',
    'subitem'
  ];

  static CONTENT_TYPES = ['subsection', 'subdivision', 'subparagraph', 'subpart', 'subitem'];

  constructor({
    id,
    nodeId,
    type,
    text,
    code,
    parent = null,
    children = [],
    collection = null,
    description = '',
    parentId = null,
    amendment_history = [],
    level = 0,
    hierarchy = [],
    references = [],
    highlights = [],
    order
  }) {
    super();

    if (!Node.VALID_TYPES.includes(type)) {
      throw new Error(`Invalid node type: ${type}`);
    }

    this.id = id;
    this.nodeId = nodeId;
    this.type = type;
    this.text = text;
    this.code = code || parent?.code;
    this.parent = parent;
    this.parentId = parentId;
    this.collection = collection || parent?.collection;
    this.description = description;
    this.amendment_history = amendment_history;
    this.level = level;
    this._hierarchy = hierarchy;
    this.order = order;

    // Convert references and highlights to their respective class instances
    this._references = references.map(ref => ref instanceof Reference ? ref : Reference.fromJSON(ref));
    this._highlights = highlights.map(hl => hl instanceof Highlight ? hl : Highlight.fromJSON(hl));

    // Initialize protected children array
    this._children = [];

    // Add initial children if provided
    if (Array.isArray(children)) {
      children.forEach(child => {
        if (child instanceof Node) {
          this.addChild(child);
        }
      });
    }

    // Validate based on type
    this.validate();
  }

  validate() {
    const baseSchema = {
      id: { type: 'string', required: true },
      nodeId: { type: 'string', required: true },
      type: { type: 'string', required: true, enum: Node.VALID_TYPES },
      text: { type: 'string', required: true },
      references: { type: 'array' },
      highlights: { type: 'array' },
      order: { type: 'number' }
    };

    // Add type-specific validation
    const typeSchemas = {
      collection: {
        ...baseSchema,
        // Add collection-specific validation
      },
      code: {
        ...baseSchema,
        // Add code-specific validation
      },
      section: {
        ...baseSchema,
        amendment_history: { type: 'array' }
      },
      subsection: {
        ...baseSchema,
        level: { type: 'number' }
      }
    };

    const schema = typeSchemas[this.type] || baseSchema;
    validate(this, schema);
  }

  // Static factory methods
  static createCollection({ id, text }) {
    const collection = new Node({
      id,
      nodeId: `/collection/${id}`,
      parentId: '/',
      type: 'collection',
      text: text.toUpperCase(),
      collection: id
    });

    return collection;
  }

  static async createCode(data, parent = null) {
    const code = new Node({
      ...data,
      type: 'code',
      parent
    });

    // Build tree from statute data if available
    if (data.titles || data.articles) {
      code.children = code.buildStatuteTree(data.titles || data.articles);
    }

    return code;
  }

  // Helper methods moved from Code class
  buildStatuteTree(items) {
    if (!items || !Array.isArray(items)) return [];

    return items
      .map(item => {
        try {
          return new Node({
            ...item,
            code: this.code,
            collection: this.collection,
            parent: this
          });
        } catch (error) {
          console.error('Error creating node:', error);
          return null;
        }
      })
      .filter(Boolean);
  }

  // Children accessor methods
  get children() {
    return this._children;
  }

  set children(value) {
    this._children = [];
    if (Array.isArray(value)) {
      value.forEach(child => this.addChild(child));
      this._children.sort((a, b) => (a.order || 0) - (b.order || 0));
    }
  }

  // Helper method to properly add a child node
  addChild(child) {
    if (!(child instanceof Node)) {
      throw new Error('Child must be a Node instance');
    }

    // Validate parent-child relationship
    if (this.isRoot() && !child.isCollection()) {
      throw new Error('Root node can only have Collection children');
    }
    if (this.isCollection() && !child.isCode()) {
      throw new Error('Collection nodes can only have Code children');
    }

    // Ensure child inherits code if not set
    if (!child.code && this.code) {
      child.code = this.code;
    }

    // Set up parent relationship
    child.parent = this;

    // Inherit collection if not set
    if (!child.collection) {
      child.collection = this.collection;
    }

    // Check if a child with the same nodeId already exists
    const existingChildIndex = this._children.findIndex(existingChild =>
      existingChild.nodeId === child.nodeId
    );

    if (existingChildIndex === -1) {
      // Add new child
      this._children.push(child);
      this._children.sort((a, b) => (a.order || 0) - (b.order || 0));
    } else {
      // Update existing child
      this._children[existingChildIndex] = child;
    }
  }

  // Get collection ID, traversing up the tree if necessary
  getCollection() {
    // First try the local property
    if (this.collection) {
      return this.collection;
    }

    // If not available, traverse up the tree
    if (this.parent) {
      return this.parent.getCollection();
    }

    // If we reach here, no collection was found
    return null;
  }

  // Get code ID, traversing up the tree if necessary
  getCode() {
    // First try the local property
    if (this.code) {
      return this.code;
    }

    // If not available, traverse up the tree
    if (this.parent) {
      return this.parent.getCode();
    }

    // If we reach here, no code was found
    return null;
  }

  // Helper methods to check node type
  isRoot() { return this.type === 'root' }
  isCollection() { return this.type === 'collection'; }
  isCode() { return this.type === 'code'; }
  isTitle() { return this.type === 'title'; }
  isSubtitle() { return this.type === 'subtitle'; }
  isChapter() { return this.type === 'chapter'; }
  isSubchapter() { return this.type === 'subchapter'; }
  isSection() { return this.type === 'section'; }
  isSubsection() { return this.type === 'subsection'; }
  isArticle() { return this.type === 'article'; }
  isSubarticle() { return this.type === 'subarticle'; }
  isSubdivision() { return this.type === 'subdivision'; }
  isSubparagraph() { return this.type === 'subparagraph'; }
  isSubpart() { return this.type === 'subpart'; }

  // Check if node contains statute content
  isContentNode() { return Node.CONTENT_TYPES.includes(this.type); }

  // Tree traversal methods
  hasChildren() {
    return this._children.length > 0;
  }

  hasParent() {
    return this.parent !== null;
  }

  getRootNode() {
    if (!this.parent || this.isRoot()) return this;
    return this.parent.getRootNode();
  }

  // Get full hierarchy path for breadcrumbs (excluding root)
  getHierarchy() {
    if (!Array.isArray(this._hierarchy)) {
      console.warn('Hierarchy is not an array:', this._hierarchy);
      return [];
    }

    const validNodes = this._hierarchy.filter(node => {
      if (!node || typeof node !== 'object') {
        console.error('Invalid node in hierarchy:', node);
        return false;
      }
      if (!node.nodeId) {
        console.error('Node missing nodeId:', node);
        return false;
      }
      return true;
    });

    return validNodes
      .map(node => {
        try {
          return new Node({
            id: node.nodeId.split('/').pop(),
            nodeId: node.nodeId,
            type: node.type,
            text: node.text,
            parentId: node.nodeId.split('/').slice(0, -1).join('/') || '/'
          });
        } catch (error) {
          console.error('Error creating node in hierarchy:', error, 'Node data:', node);
          return null;
        }
      })
      .filter(Boolean);
  }

  // Add helper method to set hierarchy
  setHierarchy(hierarchy) {
    this._hierarchy = hierarchy;
  }

  // Add helper to check if we have MongoDB hierarchy
  hasHierarchy() {
    return Array.isArray(this._hierarchy) && this._hierarchy.length > 0;
  }

  // Instance method to find nodes in this node's subtree by nodeId
  findNode(nodeId) {
    if (!nodeId) return null;
    if (this.nodeId === nodeId) return this;

    for (const child of this.children) {
      const found = child.findNode(nodeId);
      if (found) return found;
    }
    return null;
  }

  findNodeByStartEndPath(startPath, endPath) {
    if (!startPath || !endPath) return null;
    if (this.nodeId.startsWith(startPath) && this.nodeId.endsWith(endPath)) return this;
    for (const child of this.children) {
      const found = child.findNodeByStartEndPath(startPath, endPath);
      if (found) return found;
    }
    return null;
  }

  getParentOfType(type) {
    function isMatchingType(node) {
      return node[`is${type.charAt(0).toUpperCase() + type.slice(1)}`]();
    }

    const rootNode = this.getRootNode();

    const findParentOfType = (nodeId) => {
      if (!nodeId) return null;

      const parent = rootNode.findNode(nodeId);
      if (!parent) return null;

      if (isMatchingType(parent)) return parent;

      return findParentOfType(parent.parentId);
    };

    return findParentOfType(this.parentId);
  }

  // Add method to get children of specific type
  getChildrenOfType(type) {
    return this.children.filter(child => child.type === type);
  }

  // Storage methods
  saveToLocalStorage(key) {
    localStorage.setItem(key, this.nodeId);
  }

  static restoreFromLocalStorage(key, rootNode) {
    try {
      const nodeId = localStorage.getItem(key);
      if (!nodeId) return null;

      return rootNode.findNode(nodeId);
    } catch (error) {
      console.warn('Error restoring node from localStorage:', error);
      localStorage.removeItem(key);
      return null;
    }
  }

  // Add convenience methods from subclasses
  get subsections() {
    return this.getChildrenOfType('subsection');
  }

  get codes() {
    return this.getChildrenOfType('code');
  }

  findCode(codeId) {
    return this.codes.find(code => code.id === codeId);
  }

  getSubsectionPath() {
    if (!this.isSubsection()) return '';

    // Get everything after the last '/' in the nodeId
    // This will give us something like "1.03(a)(2)"
    return this.nodeId.split('/').pop();
  }

  // References accessor methods
  get references() {
    return this._references || [];
  }

  set references(value) {
    this._references = Array.isArray(value) ?
      value.map(ref => ref instanceof Reference ? ref : Reference.fromJSON(ref)) :
      [];
  }

  // Highlights accessor methods
  get highlights() {
    return this._highlights || [];
  }

  set highlights(value) {
    this._highlights = Array.isArray(value) ?
      value.map(hl => hl instanceof Highlight ? hl : Highlight.fromJSON(hl)) :
      [];
  }

  // Add a single reference
  addReference(reference) {
    const ref = reference instanceof Reference ? reference : Reference.fromJSON(reference);
    if (ref) {
      this._references = [...this._references, ref];
    }
  }

  // Add a single highlight
  addHighlight(highlight) {
    const hl = highlight instanceof Highlight ? highlight : Highlight.fromJSON(highlight);
    if (hl) {
      this._highlights = [...this._highlights, hl];
    }
  }

  // Remove a reference by id
  removeReference(referenceId) {
    this._references = this._references.filter(ref => ref.id !== referenceId);
  }

  // Remove a highlight by id
  removeHighlight(highlightId) {
    this._highlights = this._highlights.filter(hl => hl.id !== highlightId);
  }

  // Helper method to check if node has references
  hasReferences() {
    return Array.isArray(this._references) && this._references.length > 0;
  }

  // Helper method to check if node has highlights
  hasHighlights() {
    return Array.isArray(this._highlights) && this._highlights.length > 0;
  }

  // Helper method to get references for this node and all descendants
  getAllReferences() {
    const references = [...this.references];
    this.children.forEach(child => {
      references.push(...child.getAllReferences());
    });
    return references;
  }

  // Helper method to get highlights for this node and all descendants
  getAllHighlights() {
    const highlights = [...this.highlights];
    this.children.forEach(child => {
      highlights.push(...child.getAllHighlights());
    });
    return highlights;
  }
}
