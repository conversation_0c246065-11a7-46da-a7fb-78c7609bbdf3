import { Node } from './Node';

// Helper function to get a clean object without circular references
const cleanNodeForTest = (node) => {
  return {
    id: node.id,
    nodeId: node.nodeId,
    type: node.type,
    text: node.text,
    code: node.code,
    collection: node.collection,
    children: node.children.map(child => child.nodeId)
  };
};

describe('Node', () => {
  // Basic construction and validation
  describe('constructor', () => {
    it('creates a valid node with required properties', () => {
      const node = new Node({
        id: '1',
        nodeId: '/collection/tx/code/ag/title/1',
        type: 'title',
        text: 'Title 1'
      });

      const cleanNode = cleanNodeForTest(node);
      expect(cleanNode).toMatchObject({
        id: '1',
        nodeId: '/collection/tx/code/ag/title/1',
        type: 'title',
        text: 'Title 1'
      });
    });

    it('throws error for invalid type', () => {
      expect(() => new Node({
        id: '1',
        nodeId: '/test',
        type: 'invalid_type',
        text: 'Test'
      })).toThrow('Invalid node type: invalid_type');
    });

    it('inherits code and collection from parent', () => {
      const parent = new Node({
        id: 'ag',
        nodeId: '/collection/tx/code/ag',
        type: 'code',
        text: 'Agriculture Code',
        code: 'ag',
        collection: 'tx'
      });

      const child = new Node({
        id: '1',
        nodeId: '/collection/tx/code/ag/title/1',
        type: 'title',
        text: 'Title 1',
        parent
      });

      expect(child.code).toBe('ag');
      expect(child.collection).toBe('tx');
    });
  });

  // Factory methods
  describe('factory methods', () => {
    it('creates a collection node', () => {
      const collection = Node.createCollection({
        id: 'tx',
        text: 'Texas Statutes'
      });

      const cleanCollection = cleanNodeForTest(collection);
      expect(cleanCollection).toMatchObject({
        type: 'collection',
        nodeId: '/collection/tx',
        collection: 'tx',
        text: 'TEXAS STATUTES'
      });
    });

    it('creates a code node with hierarchy', async () => {
      const parent = Node.createCollection({
        id: 'tx',
        text: 'Texas Statutes'
      });

      const codeData = {
        id: 'ag',
        text: 'Agriculture Code',
        collection: 'tx',
        nodeId: '/collection/tx/code/ag',
        titles: [
          {
            id: '1',
            type: 'title',
            text: 'Title 1',
            nodeId: '/collection/tx/code/ag/title/1'
          }
        ]
      };

      const code = await Node.createCode(codeData, parent);
      const cleanCode = cleanNodeForTest(code);
      expect(cleanCode.type).toBe('code');
      expect(code.children).toHaveLength(1);
      expect(code.children[0].type).toBe('title');
      expect(code.nodeId).toBe('/collection/tx/code/ag');
    });
  });

  // Tree operations
  describe('tree operations', () => {
    let root, collection, code, title, chapter;

    beforeEach(() => {
      // Set up a test tree
      root = new Node({
        id: 'root',
        nodeId: '/',
        type: 'root',
        text: 'Root'
      });

      collection = new Node({
        id: 'tx',
        nodeId: '/collection/tx',
        type: 'collection',
        text: 'Texas',
        parent: root,
        parentId: '/'
      });

      code = new Node({
        id: 'ag',
        nodeId: '/collection/tx/code/ag',
        type: 'code',
        text: 'Agriculture',
        parent: collection,
        parentId: '/collection/tx'
      });

      title = new Node({
        id: '1',
        nodeId: '/collection/tx/code/ag/title/1',
        type: 'title',
        text: 'Title 1',
        parent: code,
        parentId: '/collection/tx/code/ag'
      });

      chapter = new Node({
        id: '1',
        nodeId: '/collection/tx/code/ag/title/1/chapter/1',
        type: 'chapter',
        text: 'Chapter 1',
        parent: title,
        parentId: '/collection/tx/code/ag/title/1'
      });

      // Set up parent-child relationships
      root.addChild(collection);
      collection.addChild(code);
      code.addChild(title);
      title.addChild(chapter);
    });

    it('validates parent-child relationships', () => {
      const invalidTitle = new Node({
        id: '2',
        nodeId: '/test/title/2',
        type: 'title',
        text: 'Title 2'
      });

      expect(() => root.addChild(invalidTitle)).toThrow('Root node can only have Collection children');
      expect(() => collection.addChild(invalidTitle)).toThrow('Collection nodes can only have Code children');
    });

    it('finds node by nodeId', () => {
      const found = root.findNode('/collection/tx/code/ag/title/1');
      expect(found?.nodeId).toBe(title.nodeId);
    });

    it('finds node by start and end path', () => {
      const found = root.findNodeByStartEndPath('/collection/tx', '/title/1');
      expect(found?.nodeId).toBe(title.nodeId);
    });

    it('gets node hierarchy', () => {
      // Set up the hierarchy property that getHierarchy() uses
      chapter.setHierarchy([
        {
          nodeId: '/collection/tx',
          type: 'collection',
          text: 'Texas'
        },
        {
          nodeId: '/collection/tx/code/ag',
          type: 'code',
          text: 'Agriculture'
        },
        {
          nodeId: '/collection/tx/code/ag/title/1',
          type: 'title',
          text: 'Title 1'
        },
        {
          nodeId: '/collection/tx/code/ag/title/1/chapter/1',
          type: 'chapter',
          text: 'Chapter 1'
        }
      ]);

      const hierarchy = chapter.getHierarchy();
      const types = hierarchy.map(n => n.type);
      expect(types).toEqual(['collection', 'code', 'title', 'chapter']);
    });

    it('gets parent of specific type', () => {
      const parentTitle = chapter.getParentOfType('title');
      expect(parentTitle?.nodeId).toBe(title.nodeId);
    });
  });

  // Type checking methods
  describe('type checking', () => {
    it('correctly identifies node types', () => {
      const node = new Node({
        id: '1',
        nodeId: '/test',
        type: 'section',
        text: 'Test'
      });

      expect(node.isSection()).toBe(true);
      expect(node.isSubsection()).toBe(false);
    });

    it('identifies content nodes', () => {
      const contentNode = new Node({
        id: '1',
        nodeId: '/test',
        type: 'subsection',
        text: 'Test'
      });

      const nonContentNode = new Node({
        id: '1',
        nodeId: '/test',
        type: 'chapter',
        text: 'Test'
      });

      expect(contentNode.isContentNode()).toBe(true);
      expect(nonContentNode.isContentNode()).toBe(false);
    });
  });

  // Storage methods
  describe('storage methods', () => {
    beforeEach(() => {
      // Clear localStorage before each test
      localStorage.clear();
    });

    it('saves and restores from localStorage', () => {
      const node = new Node({
        id: '1',
        nodeId: '/test',
        type: 'section',
        text: 'Test'
      });

      node.saveToLocalStorage('testKey');
      expect(localStorage.getItem('testKey')).toBe('/test');
    });

    it('handles missing localStorage data', () => {
      const result = Node.restoreFromLocalStorage('nonexistent', null);
      expect(result).toBeNull();
    });
  });
});
