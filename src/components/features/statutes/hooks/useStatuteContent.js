'use client';

import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useTreeState } from '@/components/features/navtree';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';
import { HighlightService } from '@/components/features/highlights/services/HighlightService';
import { ReferenceService } from '@/components/features/references/services/ReferenceService';

/**
 * Hook for fetching and managing statute content, highlights, and references
 * @returns {Object} Combined data and loading/error states
 */
export function useStatuteContent() {
  const { selectedNodeId } = useTreeState();
  const { data: session } = useSession();

  const contentQuery = useQuery({
    queryKey: ['statute-content', selectedNodeId],
    queryFn: () => selectedNodeId ? StatuteService.getContent(selectedNodeId) : null,
    enabled: !!selectedNodeId,
    keepPreviousData: true
  });

  const highlightsQuery = useQuery({
    queryKey: ['highlights', selectedNodeId],
    queryFn: () => selectedNodeId ? HighlightService.getHighlights(selectedNodeId) : [],
    enabled: !!selectedNodeId && !!session?.user
  });

  const referencesQuery = useQuery({
    queryKey: ['references', selectedNodeId],
    queryFn: () => selectedNodeId ? ReferenceService.getReferences(selectedNodeId) : [],
    enabled: !!selectedNodeId,
    keepPreviousData: true
  });

  // TEMPORARILY DISABLED: Pre-fetch adjacent content (causing memory issues)
  // TODO: Re-enable with more conservative approach once memory usage is under control
  /*
  useEffect(() => {
    if (!selectedNodeId || !treeNodes || contentQuery.isLoading) return;

    const navigationContext = NavigationService.getNavigationContext(selectedNodeId, treeNodes);
    
    // Only pre-fetch statute content (not highlights/references) to reduce memory usage
    // Pre-fetch previous node content
    if (navigationContext.previous?.nodeId) {
      const prevNodeId = navigationContext.previous.nodeId;
      queryClient.prefetchQuery({
        queryKey: ['statute-content', prevNodeId],
        queryFn: () => StatuteService.getContent(prevNodeId),
        staleTime: 60 * 1000, // Only 1 minute to reduce memory
        gcTime: 2 * 60 * 1000, // Garbage collect after 2 minutes
      });
    }

    // Pre-fetch next node content
    if (navigationContext.next?.nodeId) {
      const nextNodeId = navigationContext.next.nodeId;
      queryClient.prefetchQuery({
        queryKey: ['statute-content', nextNodeId],
        queryFn: () => StatuteService.getContent(nextNodeId),
        staleTime: 60 * 1000, // Only 1 minute to reduce memory
        gcTime: 2 * 60 * 1000, // Garbage collect after 2 minutes
      });
    }
  }, [selectedNodeId, treeNodes, contentQuery.isLoading, queryClient]);
  */

  // Combine the data when all queries are successful
  const combinedData = useMemo(() => {
    if (!contentQuery.data) return null;

    const { selectedNode, content } = contentQuery.data;

    // Just return the content as received, do not enhance nodes
    return {
      selectedNode,
      content
    };
  }, [contentQuery.data]);

  return {
    data: combinedData,
    isLoading: contentQuery.isLoading || (session?.user && highlightsQuery.isLoading) || referencesQuery.isLoading,
    isError: contentQuery.isError || (session?.user && highlightsQuery.isError) || referencesQuery.isError,
    error: contentQuery.error || (session?.user && highlightsQuery.error) || referencesQuery.error,
    refetch: () => {
      contentQuery.refetch();
      if (session?.user) {
        highlightsQuery.refetch();
      }
      referencesQuery.refetch();
    }
  };
}
