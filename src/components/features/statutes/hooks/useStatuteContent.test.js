import { renderHook, waitFor } from '@testing-library/react';
import { useTreeState } from '@/components/features/navtree';
import { useSession } from 'next-auth/react';
import { useStatuteContent } from './useStatuteContent';
import { StatuteService } from '../services/StatuteService';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock dependencies
jest.mock('@/components/features/navtree', () => ({
  useTreeState: jest.fn()
}));

jest.mock('next-auth/react', () => ({
  useSession: jest.fn()
}));

jest.mock('../services/StatuteService', () => ({
  StatuteService: {
    getContent: jest.fn()
  }
}));

jest.mock('@/components/features/highlights/services/HighlightService', () => ({
  HighlightService: {
    getHighlights: jest.fn(() => Promise.resolve([]))
  }
}));

jest.mock('@/components/features/references/services/ReferenceService', () => ({
  ReferenceService: {
    getReferences: jest.fn(() => Promise.resolve([]))
  }
}));

// Mock fetch to avoid ReferenceError in Node
beforeAll(() => {
  global.fetch = jest.fn(() => Promise.resolve({ json: () => Promise.resolve({}) }));
});

afterAll(() => {
  delete global.fetch;
});

// Create a wrapper with QueryClientProvider for the tests
function createWrapper() {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries to make testing easier
        retry: false
      }
    }
  });

  const Wrapper = ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  Wrapper.displayName = 'QueryClientWrapper';
  return Wrapper;
}

describe('useStatuteContent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default to no session
    useSession.mockReturnValue({ data: null });
  });

  it('returns null data and not loading when no node is selected', async () => {
    // Mock no selected node
    useTreeState.mockReturnValue({ selectedNodeId: undefined });

    const { result } = renderHook(() => useStatuteContent(), {
      wrapper: createWrapper()
    });

    expect(result.current).toMatchObject({
      data: null,
      isLoading: false,
      isError: false,
      error: null
    });
    expect(typeof result.current.refetch).toBe('function');
    // Verify getContent was not called
    expect(StatuteService.getContent).not.toHaveBeenCalled();
  });

  it('fetches content without highlights when user is not signed in', async () => {
    const mockContent = {
      selectedNode: 'test-node-id',
      content: [{ id: 'test-content' }]
    };

    // Mock selected node but no session
    useTreeState.mockReturnValue({ selectedNodeId: 'test-node-id' });
    useSession.mockReturnValue({ data: null });
    StatuteService.getContent.mockResolvedValue(mockContent);

    const { result } = renderHook(() => useStatuteContent(), {
      wrapper: createWrapper()
    });

    // Should start loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the query to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Verify final state - should have content but not fail due to highlights
    expect(result.current).toMatchObject({
      data: mockContent,
      isLoading: false,
      isError: false,
      error: null
    });
    expect(typeof result.current.refetch).toBe('function');
    // Verify service was called correctly
    expect(StatuteService.getContent).toHaveBeenCalledWith('test-node-id');
  });

  it('fetches content when node is selected', async () => {
    const mockContent = {
      selectedNode: 'test-node-id',
      content: [{ id: 'test-content' }]
    };

    // Mock selected node and session
    useTreeState.mockReturnValue({ selectedNodeId: 'test-node-id' });
    useSession.mockReturnValue({ data: { user: { id: 'user-1' } } });
    StatuteService.getContent.mockResolvedValue(mockContent);

    const { result } = renderHook(() => useStatuteContent(), {
      wrapper: createWrapper()
    });

    // Should start loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the query to complete
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Verify final state
    expect(result.current).toMatchObject({
      data: mockContent,
      isLoading: false,
      isError: false,
      error: null
    });
    expect(typeof result.current.refetch).toBe('function');
    // Verify service was called correctly
    expect(StatuteService.getContent).toHaveBeenCalledWith('test-node-id');
  });

  it('handles error when content fetch fails', async () => {
    const error = new Error('Failed to fetch content');

    // Mock selected node, session, and error response
    useTreeState.mockReturnValue({ selectedNodeId: 'test-node-id' });
    useSession.mockReturnValue({ data: { user: { id: 'user-1' } } });
    StatuteService.getContent.mockRejectedValue(error);

    const { result } = renderHook(() => useStatuteContent(), {
      wrapper: createWrapper()
    });

    // Should start loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the query to fail
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Verify error state
    expect(result.current).toMatchObject({
      data: null,
      isLoading: false,
      isError: true,
      error
    });
    expect(typeof result.current.refetch).toBe('function');
  });

  it('updates when selected node changes', async () => {
    const mockContent1 = {
      selectedNode: 'node-1',
      content: [{ id: 'content-1' }]
    };
    const mockContent2 = {
      selectedNode: 'node-2',
      content: [{ id: 'content-2' }]
    };

    // Setup mock implementations
    StatuteService.getContent
      .mockImplementationOnce(() => Promise.resolve(mockContent1))
      .mockImplementationOnce(() => Promise.resolve(mockContent2));

    // Start with first node and mock session
    useTreeState.mockReturnValue({ selectedNodeId: 'node-1' });
    useSession.mockReturnValue({ data: { user: { id: 'user-1' } } });

    const { result, rerender } = renderHook(() => useStatuteContent(), {
      wrapper: createWrapper()
    });

    // Wait for first query to complete
    await waitFor(() => {
      expect(result.current.data).toEqual(mockContent1);
    });

    // Change selected node
    useTreeState.mockReturnValue({ selectedNodeId: 'node-2' });
    rerender();

    // Wait for second query to complete
    await waitFor(() => {
      expect(result.current.data).toEqual(mockContent2);
    });

    // Verify both calls were made
    expect(StatuteService.getContent).toHaveBeenCalledWith('node-1');
    expect(StatuteService.getContent).toHaveBeenCalledWith('node-2');
  });

  it('caches results for the same node', async () => {
    const mockContent = {
      selectedNode: 'test-node-id',
      content: [{ id: 'test-content' }]
    };

    // Mock selected node and session
    useTreeState.mockReturnValue({ selectedNodeId: 'test-node-id' });
    useSession.mockReturnValue({ data: { user: { id: 'user-1' } } });
    StatuteService.getContent.mockResolvedValue(mockContent);

    // First render
    const { result, rerender } = renderHook(() => useStatuteContent(), {
      wrapper: createWrapper()
    });

    // Wait for first query to complete
    await waitFor(() => {
      expect(result.current.data).toEqual(mockContent);
    });

    // Re-render with same node
    rerender();

    // Verify data is still there and service wasn't called again
    expect(result.current.data).toEqual(mockContent);
    expect(StatuteService.getContent).toHaveBeenCalledTimes(1);
  });
});
