'use client';

import { useRouter, notFound } from 'next/navigation';
import PropTypes from 'prop-types';

export function StatuteError({ message }) {
  const router = useRouter();
  
  if (!message) {
    return notFound();
  }

  return (
    <div className="flex items-center justify-center min-h-[50vh] p-8">
      <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-r max-w-2xl">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-red-800">Error</h3>
            <p className="mt-2 text-sm text-red-700">{message}</p>
            <div className="mt-4">
              <button
                type="button"
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                onClick={() => router.back()}
              >
                Go back
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

StatuteError.propTypes = {
  message: PropTypes.string.isRequired
};