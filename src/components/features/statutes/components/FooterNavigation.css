/* Footer Navigation Component Styles */
.footer-navigation {
  position: relative;
  width: 100%;
  background: white;
  z-index: 50;
  flex-shrink: 0;
  min-height: 60px; /* Prevent height jumping during button loading */
}

/* Fixed mode - traditional footer styling */
.footer-navigation--fixed {
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* Inline mode - positioned below content, no borders */
.footer-navigation--inline {
  border-top: none;
  box-shadow: none;
  margin-top: 0;
}

/* Inline mode buttons - use hover style as default */
.footer-navigation__button--inline {
  background: #f3f4f6;
  color: var(--accent-color);
}

.footer-navigation__button--inline:hover {
  background: #e5e7eb;
  color: var(--accent-color);
}

/* Reduce padding for inline mode to minimize spacing */
.footer-navigation--inline .footer-navigation__container {
  padding: 4px 0;
}

.footer-navigation__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  max-width: 800px;
  margin: 0 auto;
  gap: 16px;
}

.footer-navigation__button-container {
  flex: 1;
  display: flex;
}

.footer-navigation__button-container:first-child {
  justify-content: flex-start;
}

.footer-navigation__button-container:last-child {
  justify-content: flex-end;
}

/* Navigation buttons */
.footer-navigation__button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: var(--gray-text-color);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  flex: 1;
  max-width: 180px;
  height: 44px;
}

.footer-navigation__button:hover:not(.footer-navigation__button--disabled) {
  background: #f3f4f6;
  color: var(--accent-color);
}

.footer-navigation__button:active:not(.footer-navigation__button--disabled) {
  background: #e5e7eb;
}

.footer-navigation__button--disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: #d1d5db;
}

/* Previous button alignment */
.footer-navigation__button--previous {
  justify-content: center;
}

/* Next button alignment */
.footer-navigation__button--next {
  justify-content: center;
}

/* TOC button styling */
.footer-navigation__toc-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 4px 12px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: var(--gray-text-color);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 44px;
  min-width: 60px;
}

.footer-navigation__toc-button:hover {
  background: #f3f4f6;
  color: var(--accent-color);
}

.footer-navigation__toc-button:active {
  background: #e5e7eb;
}

/* Arrow styling */
.footer-navigation__arrow {
  font-size: 40px;
  font-weight: normal;
  color: inherit;
  transform: translateY(-2px);
}

/* Label container */
.footer-navigation__label {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0px;
  min-width: 0; /* Allow text truncation */
  line-height: 1;
}

.footer-navigation__button--next .footer-navigation__label {
  align-items: flex-end;
}

/* Prefix styling (Prev, Next) */
.footer-navigation__prefix {
  font-size: 12px;
  font-weight: 500;
  color: inherit;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
  margin-bottom: -1px;
}

/* Node text styling */
.footer-navigation__text {
  font-weight: 600;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  line-height: 1;
}


/* Responsive design */
@media (max-width: 768px) {
  .footer-navigation__container {
    padding: 12px 16px;
    gap: 8px;
    max-width: none;
  }

  .footer-navigation__button {
    min-width: 100px;
    max-width: 140px;
    padding: 8px 12px;
    font-size: 13px;
    height: 48px; /* Larger touch target */
  }

  .footer-navigation__text {
    max-width: 100px;
  }

  .footer-navigation__toc-button {
    height: 48px; /* Match button height */
    min-width: 64px;
    padding: 6px 16px;
  }

  .footer-navigation__prefix {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .footer-navigation__container {
    padding: 10px 12px;
    gap: 6px;
    max-width: none;
  }

  .footer-navigation__button {
    min-width: 85px;
    max-width: 110px;
    padding: 6px 8px;
    font-size: 12px;
    height: 44px; /* Adequate touch target for small screens */
  }

  .footer-navigation__text {
    max-width: 75px;
    font-size: 11px;
  }

  .footer-navigation__toc-button {
    height: 44px;
    min-width: 56px;
    padding: 4px 12px;
  }

  .footer-navigation__prefix {
    font-size: 9px;
  }

  .footer-navigation__arrow {
    font-size: 20px;
  }
}
