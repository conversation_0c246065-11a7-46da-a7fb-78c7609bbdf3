import { render, screen } from '@testing-library/react';
import { StatuteContent } from './StatuteContent';
import { useStatuteContent } from '../hooks/useStatuteContent';

// Mock required dependencies
jest.mock('../hooks/useStatuteContent', () => ({
  useStatuteContent: jest.fn()
}));

jest.mock('@/components/ui/LoadingSpinner', () => ({
  LoadingSpinner: ({ loading }) => loading ? <div data-testid="loading-spinner">Loading...</div> : null
}));

jest.mock('../components/StatuteError', () => ({
  StatuteError: ({ message }) => <div data-testid="error-message">{message}</div>
}));

jest.mock('../views/ContentView', () => ({
  ContentView: ({ selectedNode }) => <div data-testid="content-view">Content for {selectedNode?.id || selectedNode?.nodeId}</div>
}));

describe('StatuteContent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading spinner when content is loading', () => {
    useStatuteContent.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
      error: null
    });
    render(<StatuteContent />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('shows error message when there is an error', () => {
    const errorMessage = 'Failed to load content';
    useStatuteContent.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
      error: { message: errorMessage }
    });
    render(<StatuteContent />);
    expect(screen.getByTestId('error-message')).toHaveTextContent(errorMessage);
  });

  it('shows "No content available" message when data is empty', () => {
    useStatuteContent.mockReturnValue({
      data: { selectedNode: null, content: [] },
      isLoading: false,
      isError: false,
      error: null
    });
    render(<StatuteContent />);
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'No content available for this node'
    );
  });

  it('renders content view when content is available', () => {
    const mockData = {
      selectedNode: 'test-node-id',
      content: [{ id: 'test-content', nodeId: 'test-node-id' }]
    };
    useStatuteContent.mockReturnValue({
      data: mockData,
      isLoading: false,
      isError: false,
      error: null
    });
    render(<StatuteContent />);
    expect(screen.getByTestId('content-view')).toBeInTheDocument();
    expect(screen.getByTestId('content-view')).toHaveTextContent('Content for test-content');
  });
});
