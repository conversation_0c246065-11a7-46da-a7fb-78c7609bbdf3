import { memo } from 'react';
import { useLayout } from '@/app/LayoutContext';

export const NavigationHintView = memo(() => {
  const { isMobile } = useLayout();

  return (
    <div className="mt-8 text-sm text-gray-400 flex flex-col items-center gap-2">
      <span>Use the Table of Contents to navigate through the statutes</span>
      {isMobile ? (
        // Mobile: Arrow pointing down (to footer)
        <svg className="w-16 h-32" viewBox="0 0 64 128" fill="none">
          <path
            d="M32 20 C32 50, 32 70, 32 100 L24 92 M32 100 L40 92"
            stroke="rgb(59 130 246)"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="animate-pulse"
          />
        </svg>
      ) : (
        // Desktop/Tablet: Arrow pointing left (to sidebar)
        <svg className="w-32 h-16" viewBox="0 0 128 64" fill="none">
          <path
            d="M108 32 C78 32, 58 32, 28 32 L36 24 M28 32 L36 40"
            stroke="rgb(59 130 246)"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="animate-pulse"
          />
        </svg>
      )}
    </div>
  );
});

NavigationHintView.displayName = 'NavigationHintView';
