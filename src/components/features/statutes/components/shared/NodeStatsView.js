import { memo } from 'react';
import PropTypes from 'prop-types';

export const NodeStatsView = memo(({ stats }) => (
  <div className="flex flex-col items-center text-sm text-gray-500">
    <span className="font-semibold text-lg">{stats.id}</span>
    <span>{stats.type}</span>
    <span className="font-semibold text-lg">{stats.count}</span>
    <span>{stats.label}</span>
  </div>
));

NodeStatsView.propTypes = {
  stats: PropTypes.shape({
    id: PropTypes.string.isRequired,
    type: PropTypes.string.isRequired,
    count: PropTypes.number.isRequired,
    label: PropTypes.string.isRequired
  }).isRequired
};

NodeStatsView.displayName = 'NodeStatsView';
