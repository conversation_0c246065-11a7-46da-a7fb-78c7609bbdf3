import { memo } from 'react';
import { Scroll as ScrollIcon } from 'lucide-react';
import PropTypes from 'prop-types';
import { NodeStatsView } from './NodeStatsView';

export const NodeSummaryView = memo(({ stats }) => (
  <div className="mb-6 text-gray-400 relative">
    <ScrollIcon size={300} strokeWidth={0.5} />
    <div className="absolute inset-0 flex items-center justify-center -mt-12 -mr-4">
      {Array.isArray(stats) ? (
        <div className="flex gap-8 text-sm text-gray-500">
          {stats.map((stat, index) => (
            <NodeStatsView key={index} stats={stat} />
          ))}
        </div>
      ) : (
        <NodeStatsView stats={stats} />
      )}
    </div>
  </div>
));

NodeSummaryView.propTypes = {
  stats: PropTypes.oneOfType([
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      count: PropTypes.number.isRequired,
      label: PropTypes.string.isRequired
    }),
    PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      count: PropTypes.number.isRequired,
      label: PropTypes.string.isRequired
    }))
  ]).isRequired
};

NodeSummaryView.displayName = 'NodeSummaryView';
