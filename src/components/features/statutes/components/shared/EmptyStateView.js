import { memo } from 'react';
import PropTypes from 'prop-types';
import { PageTitle } from '@/components/features/shared';
import { getPageTitle } from '../../utils';
import { NavigationHintView } from './NavigationHintView';

export const EmptyStateView = memo(({ node, title, children }) => (
  <>
    <PageTitle title={getPageTitle(node)} />
    <div className="empty-state flex flex-col items-center justify-center p-8 py-16 text-gray-600 h-full top-[60px]">
      {title && <h3 className="text-xl font-semibold mb-6">{title}</h3>}
      {children}
      <NavigationHintView />
    </div>
  </>
));

EmptyStateView.propTypes = {
  node: PropTypes.shape({
    nodeId: PropTypes.string.isRequired,
    type: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired
  }).isRequired,
  title: PropTypes.string,
  children: PropTypes.node
};

EmptyStateView.displayName = 'EmptyStateView';
