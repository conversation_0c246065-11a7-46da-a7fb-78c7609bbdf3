import { memo, useMemo } from 'react';
import PropTypes from 'prop-types';
import { List } from 'lucide-react';
import { NavigationService } from '../services/NavigationService';
import { useTreeState } from '@/components/features/navtree';
import { useFlatNavTree } from '@/components/features/navtree/hooks/useFlatNavTree';
import { useLayout } from '@/app/LayoutContext';

/**
 * Footer navigation component that provides previous/next navigation between statute nodes.
 * Also includes central TOC button for outline access.
 * Functions as persistent app footer.
 */
export const FooterNavigation = memo(({ currentNode, isFixed = true }) => {
  const { selectNode } = useTreeState();
  const { isMobile, isSmallMobile, showMobileTOCPanel, setShowMobileTOCPanel } = useLayout();

  // Memoize the nodeId to prevent unnecessary API calls
  const stableNodeId = useMemo(() => currentNode?.nodeId, [currentNode?.nodeId]);
  const { flatNodes } = useFlatNavTree(stableNodeId);

  // Calculate navigation context using ONLY flat nodes from MongoDB
  const navigationContext = useMemo(() => {
    if (!stableNodeId) {
      return { previous: null, current: null, next: null };
    }

    // Skip navigation for root node
    if (stableNodeId === '/') {
      return { previous: null, current: null, next: null };
    }

    // Only use flat nodes from MongoDB - never fallback to tree processing
    if (!flatNodes || flatNodes.length === 0) {
      return { previous: null, current: null, next: null };
    }

    try {
      return NavigationService.getNavigationContext(stableNodeId, flatNodes);
    } catch (error) {
      console.error('Error in navigation context calculation:', error);
      return { previous: null, current: null, next: null };
    }
  }, [stableNodeId, flatNodes]);

  const { previous, next } = navigationContext;


  const handlePreviousClick = () => {
    if (previous?.nodeId) {
      selectNode(previous.nodeId);
    }
  };

  const handleNextClick = () => {
    if (next?.nodeId) {
      selectNode(next.nodeId);
    }
  };

  // Adjust text length based on screen size
  const getNodeText = (node) => {
    if (!node) return '';
    const fullText = NavigationService.getAbbreviatedNodeText(node);
    if (isSmallMobile) {
      // For very small screens, show just the section number/id
      return node.id || fullText.split(' ').pop() || fullText;
    }
    return fullText;
  };

  const previousText = getNodeText(previous);
  const nextText = getNodeText(next);

  return (
    <>
      <div className={`footer-navigation ${isFixed ? 'footer-navigation--fixed' : 'footer-navigation--inline'}`}>
        <div className="footer-navigation__container">
          {/* Previous button */}
          <div className="footer-navigation__button-container">
            {previous && (
              <button
                className={`footer-navigation__button footer-navigation__button--previous ${!isFixed ? 'footer-navigation__button--inline' : ''}`}
                onClick={handlePreviousClick}
                title={`Go to ${previousText}`}
              >
                <span className="footer-navigation__arrow">‹</span>
                <span className="footer-navigation__label">
                  <span className="footer-navigation__prefix">Prev</span>
                  <span className="footer-navigation__text">{previousText}</span>
                </span>
              </button>
            )}
          </div>

          {/* Central TOC button - Only show on mobile */}
          {isMobile && (
            <button
              onClick={() => {
                setShowMobileTOCPanel?.(!showMobileTOCPanel);
              }}
              className="footer-navigation__toc-button"
              title="Table of Contents"
            >
              <List size={isSmallMobile ? 18 : 20} />
              <span className={isSmallMobile ? 'text-xs' : ''}>TOC</span>
            </button>
          )}

          {/* Next button */}
          <div className="footer-navigation__button-container">
            {next && (
              <button
                className={`footer-navigation__button footer-navigation__button--next ${!isFixed ? 'footer-navigation__button--inline' : ''}`}
                onClick={handleNextClick}
                title={`Go to ${nextText}`}
              >
                <span className="footer-navigation__label">
                  <span className="footer-navigation__prefix">Next</span>
                  <span className="footer-navigation__text">{nextText}</span>
                </span>
                <span className="footer-navigation__arrow">›</span>
              </button>
            )}
          </div>
        </div>
      </div>

    </>
  );
});

FooterNavigation.displayName = 'FooterNavigation';

FooterNavigation.propTypes = {
  currentNode: PropTypes.object.isRequired,
  isFixed: PropTypes.bool
};
