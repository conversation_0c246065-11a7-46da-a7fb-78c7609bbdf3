import { render, screen, fireEvent } from '@testing-library/react';
import { StatuteNodeRenderer } from './StatuteNodeRenderer';
import { Node } from '../models/Node';

// Mock the child components
jest.mock('@/components/features/bookmarks', () => ({
  BookmarkButton: ({ node }) => <button data-testid="bookmark-button">{node.nodeId}</button>
}));

jest.mock('@/components/features/breadcrumb', () => ({
  LocateNodeButton: ({ node, onLocate }) => (
    <button data-testid="locate-button" onClick={() => onLocate(node)}>
      Locate {node.nodeId}
    </button>
  )
}));

jest.mock('@/components/features/highlights', () => ({
  HighlightContent: ({ children }) => <div data-testid="highlight-content">{children}</div>
}));

describe('StatuteNodeRenderer', () => {
  const mockOnLocateNode = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Header sections', () => {
    it.each([
      ['collection', 'h4'],
      ['code', 'h1'],
      ['title', 'h2'],
      ['subtitle', 'h2'],
      ['chapter', 'h3'],
      ['subchapter', 'h3'],
      ['article', 'h2'],
      ['subarticle', 'h2']
    ])('renders %s with correct header tag %s', (type, expectedTag) => {
      const node = new Node({
        id: '1',
        nodeId: `/${type}/1`,
        type,
        text: `Test ${type}`,
        code: 'TEST'
      });

      render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      const header = screen.getByRole('heading', { level: parseInt(expectedTag.slice(1)) });
      expect(header).toBeInTheDocument();
      expect(screen.getByTestId('highlight-content')).toHaveTextContent(`TEST ${type}`.toUpperCase());
    });

    it('applies selected class when isSelected is true', () => {
      const node = new Node({
        id: '1',
        nodeId: '/code/1',
        type: 'code',
        text: 'Test Code',
        code: 'TEST'
      });

      const { container } = render(
        <StatuteNodeRenderer
          node={node}
          isSelected={true}
          onLocateNode={mockOnLocateNode}
        />
      );

      expect(container.firstChild).toHaveClass('selected-section');
    });
  });

  describe('Section rendering', () => {
    it('renders section with title and content', () => {
      const node = new Node({
        id: '1',
        nodeId: '/section/1',
        type: 'section',
        text: 'Test Section',
        code: 'TEST'
      });

      render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      expect(screen.getByRole('heading', { level: 4 })).toHaveTextContent('Test Section');
      expect(screen.getByTestId('bookmark-button')).toBeInTheDocument();
      expect(screen.getByTestId('locate-button')).toBeInTheDocument();
    });

    it('renders amendment history if present', () => {
      const node = new Node({
        id: '1',
        nodeId: '/section/1',
        type: 'section',
        text: 'Test Section',
        code: 'TEST',
        amendment_history: ['Amended by Act 123', 'Revised in 2022']
      });

      render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      expect(screen.getByText('Amended by Act 123')).toBeInTheDocument();
      expect(screen.getByText('Revised in 2022')).toBeInTheDocument();
    });
  });

  describe('Subsection rendering', () => {
    it('returns null for subsection type nodes', () => {
      const node = new Node({
        id: '1',
        nodeId: '/subsection/1',
        type: 'subsection',
        text: 'Test Subsection',
        code: 'TEST'
      });

      const { container } = render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      expect(container.firstChild).toBeNull();
    });

    it('renders nested subsections correctly', () => {
      // Create subsection nodes
      const subsection1 = new Node({
        id: '1.1',
        nodeId: '/section/1/1',
        type: 'subsection',
        text: 'Subsection 1',
        level: 1
      });

      const subsection2_1 = new Node({
        id: '1.2.1',
        nodeId: '/section/1/2/1',
        type: 'subsection',
        text: 'Subsection 2.1',
        level: 2
      });

      const subsection2 = new Node({
        id: '1.2',
        nodeId: '/section/1/2',
        type: 'subsection',
        text: 'Subsection 2',
        level: 1,
        children: [subsection2_1]
      });

      const node = new Node({
        id: '1',
        nodeId: '/section/1',
        type: 'section',
        text: 'Test Section',
        code: 'TEST',
        children: [subsection1, subsection2]
      });

      const { container } = render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      // Check for subsection content within highlight-content divs
      const highlightDivs = container.querySelectorAll('[data-testid="highlight-content"]');
      const subsectionTexts = Array.from(highlightDivs).map(div => div.textContent);
      expect(subsectionTexts).toContain('Subsection 1');
      expect(subsectionTexts).toContain('Subsection 2');
      expect(subsectionTexts).toContain('Subsection 2.1');

      // Check for correct subsection levels
      expect(container.querySelector('.subsection-level-1')).toBeInTheDocument();
      expect(container.querySelector('.subsection-level-2')).toBeInTheDocument();
    });

    it('marks repealed subsections', () => {
      const subsection = new Node({
        id: '1.1',
        nodeId: '/section/1/1',
        type: 'subsection',
        text: 'Repealed by Act 123',
        level: 1
      });

      const node = new Node({
        id: '1',
        nodeId: '/section/1',
        type: 'section',
        text: 'Test Section',
        code: 'TEST',
        children: [subsection]
      });

      const { container } = render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      // Check for repealed class on the subsection container
      const subsectionDiv = container.querySelector('.statute-section');
      expect(subsectionDiv).toBeInTheDocument();
      expect(subsectionDiv).toHaveClass('repealed');
    });
  });

  describe('Interactions', () => {
    it('calls onLocateNode when locate button is clicked', () => {
      const node = new Node({
        id: '1',
        nodeId: '/code/1',
        type: 'code',
        text: 'Test Code',
        code: 'TEST'
      });

      render(
        <StatuteNodeRenderer
          node={node}
          isSelected={false}
          onLocateNode={mockOnLocateNode}
        />
      );

      fireEvent.click(screen.getByTestId('locate-button'));
      expect(mockOnLocateNode).toHaveBeenCalledWith(node);
    });
  });
});
