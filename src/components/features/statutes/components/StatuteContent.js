'use client';

import React from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StatuteError } from '../components/StatuteError';
import { ContentView } from '../views/ContentView';
import { useStatuteContent } from '../hooks/useStatuteContent';

export function StatuteContent() {
  const { data, isLoading, isError, error } = useStatuteContent();

  // Show loading state if we're loading or if we don't have data yet but expect to get some
  if (isLoading || (!data && !isError)) {
    return (
      <div className="mt-[60px]">
        <LoadingSpinner loading={isLoading} />
      </div>
    );
  }

  // Show error state if there was an error
  if (isError) {
    return <StatuteError message={error?.message || "An error occurred"} />;
  }

  // If we have data but no content, show appropriate message
  if (!data?.content?.length) {
    return <StatuteError message="No content available for this node" />;
  }

  try {
    // Find the actual selected node object from content
    // data.selectedNode is just the nodeId string, we need the actual node object
    const selectedNodeId = data.selectedNode;
    const selectedNodeObject = data.content.find(node => node.nodeId === selectedNodeId) || data.content[0];
    
    
    return (
      <ContentView 
        selectedNode={selectedNodeObject}
        content={data.content}
      />
    );
  } catch (e) {
    console.error('Error creating content view:', e);
    return <StatuteError message="Error displaying content" />;
  }
}
