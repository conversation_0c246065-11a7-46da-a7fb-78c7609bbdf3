import { render, screen, fireEvent } from '@testing-library/react';
import { useRouter, notFound } from 'next/navigation';
import { StatuteError } from './StatuteError';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    back: jest.fn()
  })),
  notFound: jest.fn()
}));

describe('StatuteError', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders error message', () => {
    const message = 'Test error message';
    render(<StatuteError message={message} />);

    // Check that the error message is displayed
    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByText(message)).toBeInTheDocument();
  });

  it('calls router.back() when back button is clicked', () => {
    const mockBack = jest.fn();
    useRouter.mockImplementation(() => ({
      back: mockBack
    }));

    render(<StatuteError message="Test error" />);

    // Click the back button
    const backButton = screen.getByRole('button', { name: /go back/i });
    fireEvent.click(backButton);

    // Verify router.back was called
    expect(mockBack).toHaveBeenCalledTimes(1);
  });

  it('calls notFound() when message is empty', () => {
    render(<StatuteError message="" />);
    expect(notFound).toHaveBeenCalledTimes(1);
  });

  it('calls notFound() when message is undefined', () => {
    render(<StatuteError />);
    expect(notFound).toHaveBeenCalledTimes(1);
  });

  it('renders error icon', () => {
    const { container } = render(<StatuteError message="Test error" />);

    // Check that the error icon SVG is present
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveClass('h-5', 'w-5', 'text-red-400');
  });

  it('applies correct styling classes', () => {
    const { container } = render(<StatuteError message="Test error" />);

    // Check button styling
    expect(screen.getByRole('button')).toHaveClass(
      'px-4',
      'py-2',
      'text-sm',
      'font-medium',
      'text-white',
      'bg-red-600',
      'rounded-md',
      'hover:bg-red-700'
    );

    // Check error message container styling (the outer container)
    const errorContainer = container.querySelector('.bg-red-50');
    expect(errorContainer).toHaveClass('bg-red-50', 'border-l-4', 'border-red-400', 'p-6', 'rounded-r');
  });

  it('renders back button with correct text', () => {
    render(<StatuteError message="Test error" />);

    const backButton = screen.getByRole('button', { name: /go back/i });
    expect(backButton).toBeInTheDocument();
    expect(backButton).toHaveTextContent('Go back');
  });

  it('maintains error message visibility after back button click', () => {
    const message = 'Test error message';
    const mockBack = jest.fn();
    useRouter.mockImplementation(() => ({
      back: mockBack
    }));

    render(<StatuteError message={message} />);

    // Click the back button
    const backButton = screen.getByRole('button', { name: /go back/i });
    fireEvent.click(backButton);

    // Error message should still be visible
    expect(screen.getByText(message)).toBeInTheDocument();
  });
});
