import { RootView } from '../views/RootView';
import { CollectionView } from '../views/CollectionView';
import { CodeView } from '../views/CodeView';
import { TitleView } from '../views/TitleView';

export const ContentViewFactory = {
  getContentComponent(selectedNode) {
    if (!selectedNode) return null;

    switch (selectedNode.type) {
      case 'root':
        return { Component: RootView, props: { node: selectedNode } };
      case 'collection':
        return { Component: CollectionView, props: { node: selectedNode } };
      case 'code':
        return { Component: CodeView, props: { node: selectedNode } };
      case 'title':
        return { Component: TitleView, props: { node: selectedNode } };
      default:
        return null; // Let ContentView handle the default case
    }
  }
};
