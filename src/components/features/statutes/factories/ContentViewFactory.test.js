// Mock declarations must be at the top
jest.mock('@/components/features/statutes/views/RootView', () => ({
  RootView: jest.fn(props => props)
}));

jest.mock('@/components/features/statutes/views/CollectionView', () => ({
  CollectionView: jest.fn(props => props)
}));

jest.mock('@/components/features/statutes/views/CodeView', () => ({
  CodeView: jest.fn(props => props)
}));

jest.mock('@/components/features/statutes/views/TitleView', () => ({
  TitleView: jest.fn(props => props)
}));

import { ContentViewFactory } from './ContentViewFactory';
import { RootView } from '@/components/features/statutes/views/RootView';
import { CollectionView } from '@/components/features/statutes/views/CollectionView';
import { CodeView } from '@/components/features/statutes/views/CodeView';
import { TitleView } from '@/components/features/statutes/views/TitleView';

describe('ContentViewFactory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns null when selectedNode is missing', () => {
    expect(ContentViewFactory.getContentComponent(null)).toBeNull();
    expect(ContentViewFactory.getContentComponent(undefined)).toBeNull();
  });

  it('returns RootView for root type node', () => {
    const rootNode = { type: 'root', nodeId: 'root-1', text: 'Root' };
    const result = ContentViewFactory.getContentComponent(rootNode);
    expect(result.type).toBe(RootView);
    expect(result.props).toEqual({ node: rootNode });
  });

  it('returns CollectionView for collection type node', () => {
    const collectionNode = { type: 'collection', nodeId: 'coll-1', text: 'Collection' };
    const result = ContentViewFactory.getContentComponent(collectionNode);
    expect(result.type).toBe(CollectionView);
    expect(result.props).toEqual({ node: collectionNode });
  });

  it('returns CodeView for code type node', () => {
    const codeNode = { type: 'code', nodeId: 'code-1', text: 'Code' };
    const result = ContentViewFactory.getContentComponent(codeNode);
    expect(result.type).toBe(CodeView);
    expect(result.props).toEqual({ node: codeNode });
  });

  it('returns TitleView for title type node', () => {
    const titleNode = { type: 'title', nodeId: 'title-1', text: 'Title' };
    const result = ContentViewFactory.getContentComponent(titleNode);
    expect(result.type).toBe(TitleView);
    expect(result.props).toEqual({ node: titleNode });
  });

  it('returns null for unknown node type', () => {
    const unknownNode = { type: 'section', nodeId: 'section-1', text: 'Section' };
    const result = ContentViewFactory.getContentComponent(unknownNode);
    expect(result).toBeNull();
  });
});
