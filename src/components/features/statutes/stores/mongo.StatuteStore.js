export const runtime = "nodejs";

import { mongoClient } from '@/lib/mongodb';
import { SearchMatch } from '@/components/features/search/models/SearchMatch';

export class StatuteStore {
  static COLLECTIONS_COLLECTION = 'collections';
  static CODES_COLLECTION = 'codes';
  static STATUTES_COLLECTION = 'statutes';
  static TITLE_TYPES = [
    'title',
    'subtitle',
    'code',
    'chapter',
    'subchapter',
    'article',
    'subarticle',
    'section'
  ];

  static CONTENT_TYPES = [
    'subsection',
    'subdivision',
    'subparagraph',
    'subpart',
    'subitem'
  ];

  static COLLECTION_ORDER_KEY = 'collection_order';
  static CODE_ORDER_KEY = 'code_order';

  static async statutesCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.STATUTES_COLLECTION);
  }

  static async collectionsCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.COLLECTIONS_COLLECTION);
  }

  static async codesCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.CODES_COLLECTION);
  }

  // Get enabled collections metadata
  static async getCollections() {
    try {
      const collectionsCollection = await this.collectionsCollection();
      const collections = await collectionsCollection
        .find({ enabled: true })
        .sort({ order: 1 })
        .toArray();
      return collections;
    } catch (error) {
      console.error('Error getting enabled collections:', error);
      throw error;
    }
  }

  // Get enabled codes metadata
  static async getCodes() {
    try {
      const codesCollection = await this.codesCollection();
      const codes = await codesCollection
        .find({ enabled: true })
        .sort({ order: 1 })
        .toArray();
      return codes;
    } catch (error) {
      console.error('Error getting enabled codes:', error);
      throw error;
    }
  }

  // Get enabled codes for a specific collection
  static async getCodesForCollection(collectionId) {
    try {
      const codesCollection = await this.codesCollection();
      return await codesCollection
        .find({
          collection: collectionId,
          enabled: true
        })
        .sort({ order: 1 })
        .toArray();
    } catch (error) {
      console.error('Error getting enabled codes for collection:', error);
      throw error;
    }
  }

  // Get flattened nav tree for navigation (filtered by node's code and type if nodeId provided)
  static async getFlatNavTree(nodeId = null) {
    try {
      const statutesCollection = await this.statutesCollection();

      // If nodeId is provided, get the current node to determine filtering
      let targetCode = null;
      let targetType = null;

      if (nodeId) {
        // Sanitize nodeId to remove content-type suffixes for lookup
        const sanitizedNodeId = nodeId.replace(/\(.*/, '');
        const currentNode = await statutesCollection.findOne(
          { nodeId: sanitizedNodeId },
          { projection: { code: 1, type: 1 } }
        );

        if (currentNode) {
          targetCode = currentNode.code;
          targetType = currentNode.type;
        }
      }

      // Build match conditions
      let matchConditions = {
        type: { $nin: this.CONTENT_TYPES }
      };

      // If we have a specific node, filter by its code and type
      if (targetCode && targetType) {
        matchConditions.code = targetCode;
        matchConditions.type = targetType;
      } else {
        // Fallback to enabled collections and codes
        const enabledCollections = await this.getCollections();
        const enabledCodes = await this.getCodes();

        matchConditions.$or = [
          // Include nodes from enabled collections
          {
            type: 'collection',
            nodeId: { $in: enabledCollections.map(c => `/collection/${c.id}`) }
          },
          // Include nodes from enabled codes and their children
          {
            $or: [
              {
                type: 'code',
                nodeId: { $in: enabledCodes.map(c => `/collection/${c.collection}/code/${c.code}`) }
              },
              {
                type: { $nin: ['collection', 'code'] },
                code: { $in: enabledCodes.map(c => c.code) }
              }
            ]
          }
        ];
      }

      // Optimized pipeline for filtered navigation
      const pipeline = [
        // Stage 1: Match filtered navigation nodes
        { $match: matchConditions },
        // Stage 2: Get essential fields only for navigation
        {
          $project: {
            _id: 0,
            id: 1,
            nodeId: 1,
            type: 1,
            text: 1,
            code: 1,
            order: 1
          }
        },
        // Stage 3: Sort by order for consistent navigation ordering
        {
          $sort: {
            order: 1,
            nodeId: 1
          }
        }
      ];

      const nodes = await statutesCollection.aggregate(pipeline, {
        collation: { locale: "en", numericOrdering: true },
        allowDiskUse: true
      }).toArray();

      return nodes;
    } catch (error) {
      console.error('Error in getFlatNavTree:', error);
      throw error;
    }
  }

  // Get nav tree (excluding content nodes)
  static async getNavTree() {
    try {
      // Get enabled collections and codes first
      const enabledCollections = await this.getCollections();
      const enabledCodes = await this.getCodes();

      // Pipeline without heavy aggregation operations to avoid memory limits
      const pipeline = [
        // Stage 1: Match only navigation nodes from enabled collections and codes
        {
          $match: {
            type: { $nin: this.CONTENT_TYPES },
            $or: [
              // 1. Collection nodes: included if their collection is enabled
              {
                type: 'collection',
                collection: { $in: enabledCollections.map(c => c.id) }
              },
              // 2. All other nodes: included if their collection AND code are both enabled
              {
                type: { $ne: 'collection' },
                collection: { $in: enabledCollections.map(c => c.id) },
                code: { $in: enabledCodes.map(c => c.code) }
              }
            ]
          }
        },
        // Stage 2: Get essential fields only
        {
          $project: {
            _id: 0,
            id: 1,
            nodeId: 1,
            type: 1,
            text: 1,
            parentId: 1,
            code: 1,
            order: 1
          }
        },
        // Note: Removed $sort stage to avoid memory limit issues
        // Sorting will be handled client-side in StatuteService.getNavTree()
      ];

      const statutesCollection = await this.statutesCollection();
      const nodes = await statutesCollection.aggregate(pipeline, {
        allowDiskUse: true,
        maxTimeMS: 30000  // Set timeout to prevent long-running queries
      }).toArray();

      return nodes;
    } catch (error) {
      console.error('Error in getNavTree:', error);
      throw error;
    }
  }

  static hierarchyLookupStage(startWithField = '$parentId') {
    return {
      $graphLookup: {
        from: 'statutes',
        startWith: startWithField,
        connectFromField: 'parentId',
        connectToField: 'nodeId',
        as: 'hierarchy',
        restrictSearchWithMatch: {
          type: { $ne: 'root' }  // Only filter out root nodes during traversal
        }
      }
    };
  }

  static descendantsLookupStage(startWithField = '$nodeId') {
    return {
      $graphLookup: {
        from: 'statutes',
        startWith: startWithField,
        connectFromField: 'nodeId',
        connectToField: 'parentId',
        as: 'descendants'
      }
    };
  }

  static sortedHierarchy() {
    return {
      hierarchy: {
        $sortArray: {
          input: {
            $filter: {
              input: '$hierarchy',
              as: 'h',
              cond: {
                $not: { $in: ['$$h.type', this.CONTENT_TYPES] }
              }
            }
          },
          sortBy: { nodeId: 1 }
        }
      }
    }
  }

  static sortedDescendants() {
    return {
      descendants: {
        $sortArray: {
          input: '$descendants',
          sortBy: { nodeId: 1 }
        }
      }
    }
  }

  static async getNode(nodeId) {
    try {
      const statutesCollection = await this.statutesCollection();

      const pipeline = [
        // Stage 1: Match the target node
        {
          $match: { nodeId }
        },

        // Stage 2: Get hierarchy
        this.hierarchyLookupStage(),

        // Stage 3: Get descendants
        this.descendantsLookupStage(),

        // Stage 4: Project the final shape with only necessary fields
        {
          $project: {
            _id: 0,
            // Target node fields - keep all fields
            id: 1,
            nodeId: 1,
            type: 1,
            text: 1,
            code: 1,
            parentId: 1,
            amendment_history: 1,
            description: 1,
            // Hierarchy nodes (ancestors) - filter content types and sort
            hierarchy: {
              $sortArray: {
                input: {
                  $filter: {
                    input: '$hierarchy',
                    as: 'h',
                    cond: {
                      $not: { $in: ['$$h.type', this.CONTENT_TYPES] }
                    }
                  }
                },
                sortBy: { nodeId: 1 }
              }
            },
            // Descendants - sort all nodes
            descendants: {
              $sortArray: {
                input: '$descendants',
                sortBy: { nodeId: 1 }
              }
            }
          }
        }
      ];

      const result = await statutesCollection.aggregate(pipeline, {
        collation: { locale: "en", numericOrdering: true },
        allowDiskUse: true
      }).toArray();
      return result[0] || null;
    } catch (error) {
      console.error('Error getting node:', error);
      throw error;
    }
  }

  static async searchNodes(query, selectedCodes = [], searchField = 'text') {
    if (!query || query.trim().length === 0) {
      return [];
    }

    try {
      const statutesCollection = await this.statutesCollection();

      // If searching by nodeId
      if (searchField === 'nodeId') {
        // Always treat query as a string, split into tokens for AND search
        const tokens = query.trim().split(/\s+/).filter(Boolean);
        let filter = { type: { $nin: ['root', 'collection'] } };
        if (tokens.length > 1) {
          filter.$and = tokens.map(token => ({
            nodeId: { $regex: token.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), $options: 'i' }
          }));
        } else {
          // Single token: just use a single regex
          filter.nodeId = { $regex: tokens[0].replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), $options: 'i' };
        }
        const matches = await statutesCollection.find(
          filter,
          { projection: { _id: 0, nodeId: 1 } }
        ).limit(100).toArray();
        return matches;
      }

      // If no codes provided, get all enabled codes
      let searchCodes = selectedCodes;
      if (!selectedCodes.length) {
        const enabledCodes = await this.getCodes();
        searchCodes = enabledCodes.map(code => code.code);
      }

      // Format query as exact phrase by wrapping in quotes
      const formattedQuery = `"${query.trim()}"`;

      // First get matches without hierarchy, excluding collection and code types
      const matchPipeline = [
        {
          $match: {
            $text: {
              $search: formattedQuery,
              $caseSensitive: false
            },
            type: {
              $nin: ['root', 'collection', 'code']
            },
            code: { $in: searchCodes }
          }
        },
        {
          $sort: {
            score: { $meta: 'textScore' }
          }
        },
        {
          $limit: 100
        },
        {
          $project: {
            _id: 0,
            nodeId: 1,
            text: 1,
            code: 1,
            type: 1,
            parentId: 1,
            score: { $meta: 'textScore' }
          }
        }
      ];

      const matches = await statutesCollection.aggregate(matchPipeline, { allowDiskUse: true }).toArray();

      if (!matches.length) {
        return [];
      }

      // Then get hierarchies for just the matched nodes
      const nodeIds = matches.map(m => m.nodeId);
      const hierarchyPipeline = [
        {
          $match: {
            nodeId: { $in: nodeIds }
          }
        },
        this.hierarchyLookupStage(),
        {
          $project: {
            _id: 0,
            nodeId: 1,
            hierarchy: {
              $sortArray: {
                input: {
                  $filter: {
                    input: '$hierarchy',
                    as: 'h',
                    cond: {
                      $not: { $in: ['$$h.type', this.CONTENT_TYPES] }
                    }
                  }
                },
                sortBy: { nodeId: 1 }
              }
            }
          }
        }
      ];

      const hierarchies = await statutesCollection.aggregate(hierarchyPipeline, { allowDiskUse: true }).toArray();
      const hierarchyMap = new Map(hierarchies.map(h => [h.nodeId, h.hierarchy]));

      // Combine matches with their hierarchies
      return matches.map(match => ({
        ...match,
        hierarchy: (hierarchyMap.get(match.nodeId) || []).sort((a, b) => a.nodeId.localeCompare(b.nodeId)),
        matches: SearchMatch.findMatches(match.text, query.trim())
      }));

    } catch (error) {
      console.error('MongoDB search failed:', error);
      throw error;
    }
  }

  static async getContentNodes(nodeId) {
    try {
      const statutesCollection = await this.statutesCollection();

      // First get the node to check its type
      const node = await statutesCollection.findOne({ nodeId }, { projection: { type: 1, parentId: 1 } });
      if (!node) {
        throw new Error(`Node not found: ${nodeId}`);
      }

      // For collection nodes, use a simpler query without descendants
      if (node.type === 'collection') {
        const pipeline = [
          // Stage 1: Get the collection node
          { $match: { nodeId } },

          // Stage 2: Project the final shape
          {
            $project: {
              _id: 0,
              selectedNode: '$nodeId',
              content: [{
                nodeId: '$nodeId',
                type: '$type',
                text: '$text',
                id: '$id',
                code: '$code',
                collection: '$collection'
              }]
            }
          }
        ];

        const result = await statutesCollection.aggregate(pipeline, { allowDiskUse: true }).toArray();
        return result[0] || { selectedNode: null, content: [] };
      }

      // For content nodes (subsection, subdivision, etc), get the parent section
      if (this.CONTENT_TYPES.includes(node.type)) {
        // Find the parent section by traversing up the hierarchy
        let currentNodeId = node.parentId;
        let parentNode;

        while (currentNodeId) {
          parentNode = await statutesCollection.findOne(
            { nodeId: currentNodeId },
            { projection: { type: 1, parentId: 1 } }
          );

          if (!parentNode) break;

          if (parentNode.type === 'section') {
            nodeId = currentNodeId; // Use the section's nodeId for content lookup
            break;
          }

          currentNodeId = parentNode.parentId;
        }
      }

      // Main pipeline for getting content
      const pipeline = [
        // Stage 1: Get the target node and its hierarchy
        {
          $match: { nodeId }
        },

        // Stage 2: Get hierarchy (parents only, excluding current node)
        this.hierarchyLookupStage(),

        // Stage 3: Get descendants
        this.descendantsLookupStage(),

        // Stage 4: Sort hierarchy and descendants by order
        {
          $addFields: {
            hierarchy: {
              $sortArray: {
                input: "$hierarchy",
                sortBy: { order: 1 }
              }
            },
            descendants: {
              $sortArray: {
                input: "$descendants",
                sortBy: { order: 1 }
              }
            }
          }
        },

        // Stage 5: Project the final shape with sorted arrays
        {
          $project: {
            _id: 0,
            selectedNode: nodeId,
            content: {
              $concatArrays: [
                [{
                  $mergeObjects: [
                    '$$ROOT',
                    { _id: '$$REMOVE', descendants: '$$REMOVE', hierarchy: '$$REMOVE' }
                  ]
                }],
                // Include descendants
                { $ifNull: ['$descendants', []] }
              ]
            }
          }
        }
      ];

      const result = await statutesCollection.aggregate(pipeline, { allowDiskUse: true }).toArray();
      return result[0] || { selectedNode: null, content: [] };
    } catch (error) {
      console.error('Error getting content nodes:', error);
      throw error;
    }
  }

  /**
   * Find a node by matching both the start and end of its path
   * This method is used by reference resolvers to find nodes based on partial path information
   *
   * @param {string} startPath - The beginning of the node path (e.g., "/collection/tx/code/gv")
   * @param {string} endPath - The end of the node path (e.g., "/section/411.087")
   * @returns {Promise<object|null>} - The found node or null if not found
   */
  static async findNodeByStartEndPath(startPath, endPath) {
    try {
      if (!startPath || !endPath) return null;

      const statutesCollection = await this.statutesCollection();

      // Create a regex pattern to match nodes that start with startPath and end with endPath
      // We escape special regex characters in the paths to ensure they're treated as literals
      const escapedStartPath = startPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const escapedEndPath = endPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const nodeIdPattern = new RegExp(`^${escapedStartPath}.*${escapedEndPath}$`);

      // Query MongoDB for matching nodes with hierarchy
      const pipeline = [
        // Stage 1: Match the node by pattern
        {
          $match: { nodeId: nodeIdPattern }
        },

        // Stage 2: Get hierarchy
        this.hierarchyLookupStage(),

        // Stage 3: Add sorted hierarchy field
        {
          $addFields: {
            ...this.sortedHierarchy()
          }
        },

        // Stage 4: Project the final shape
        {
          $project: {
            _id: 0,
            nodeId: 1,
            type: 1,
            code: 1,
            collection: 1,
            text: 1,
            id: 1,
            parentId: 1,
            hierarchy: 1
          }
        }
      ];

      const results = await statutesCollection.aggregate(pipeline, {
        collation: { locale: "en", numericOrdering: true },
        allowDiskUse: true
      }).toArray();

      return results[0] || null;
    } catch (error) {
      console.error('Error in findNodeByStartEndPath:', error);
      return null;
    }
  }
}
