import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient } from 'mongodb';

// Mock the mongodb.js module
jest.mock('@/lib/mongodb', () => ({
  mongoClient: {
    db: jest.fn()
  }
}));

import { StatuteStore } from './mongo.StatuteStore';

describe('StatuteStore', () => {
  let mongoServer;
  let mongoClient;
  let db;
  let originalError;

  // Sample test data
  const collectionData = [
    {
      id: 'tx',
      text: 'Texas Statutes',
      enabled: true,
      order: 1
    },
    {
      id: 'us',
      text: 'United States Code',
      enabled: false,
      order: 2
    }
  ];

  const codeData = [
    {
      id: 'ag',
      text: 'Agriculture Code',
      collection: 'tx',
      enabled: true,
      order: 1
    },
    {
      id: 'bc',
      text: 'Business and Commerce Code',
      collection: 'tx',
      enabled: true,
      order: 2
    },
    {
      id: 'disabled',
      text: 'Disabled Code',
      collection: 'tx',
      enabled: false,
      order: 3
    }
  ];

  const statuteData = [
    {
      nodeId: '/collection/tx',
      id: 'tx',
      text: 'Texas Statutes',
      type: 'collection',
      parentId: '/',
      collection: 'tx',
      children: [
        '/collection/tx/code/ag',
      ]
    },
    {
      nodeId: '/collection/tx/code/ag',
      id: 'ag',
      text: 'AGRICULTURE CODE',
      type: 'code',
      code: 'ag',
      parentId: '/collection/tx',
      collection: 'tx',
      children: [
        '/collection/tx/code/ag/title/1'
      ]
    },
    {
      nodeId: '/collection/tx/code/ag/title/1',
      id: '1',
      text: 'TITLE 1. GENERAL PROVISIONS',
      type: 'title',
      code: 'ag',
      parentId: '/collection/tx/code/ag',
      collection: 'tx',
      children: [
        '/collection/tx/code/ag/title/1/chapter/1'
      ]
    },
    {
      nodeId: '/collection/tx/code/ag/title/1/chapter/1',
      id: '1',
      text: 'CHAPTER 1. GENERAL PROVISIONS',
      type: 'chapter',
      code: 'ag',
      parentId: '/collection/tx/code/ag/title/1',
      collection: 'tx',
      children: [
        '/collection/tx/code/ag/title/1/chapter/1/section/1.001'
      ]
    },
    {
      nodeId: '/collection/tx/code/ag/title/1/chapter/1/section/1.001',
      id: '1.001',
      text: 'Sec. 1.001. PURPOSE OF CODE',
      type: 'section',
      code: 'ag',
      parentId: '/collection/tx/code/ag/title/1/chapter/1',
      collection: 'tx',
      children: [
        '/collection/tx/code/ag/title/1/chapter/1/section/1.001(a)'
      ]
    },
    {
      nodeId: '/collection/tx/code/ag/title/1/chapter/1/section/1.001(a)',
      id: '(a)',
      text: '(a) This code is enacted as...',
      type: 'subsection',
      code: 'ag',
      parentId: '/collection/tx/code/ag/title/1/chapter/1/section/1.001',
      collection: 'tx',
      level: 1
    }
  ];

  beforeAll(async () => {
    originalError = console.error;
    console.error = jest.fn();

    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    mongoClient = await MongoClient.connect(mongoUri);
    db = mongoClient.db();

    // Create text index on the statutes collection
    await db.collection('statutes').createIndex(
      { text: 'text' },
      { name: "statute_text_search" }
    );
  });

  beforeEach(async () => {
    // Clear all collections and insert test data
    await db.collection('statutes').deleteMany({});
    await db.collection('collections').deleteMany({});
    await db.collection('codes').deleteMany({});

    await db.collection('statutes').insertMany(statuteData);
    await db.collection('collections').insertMany(collectionData);
    await db.collection('codes').insertMany(codeData);

    // Reset mocks before each test
    StatuteStore.statutesCollection = jest.fn().mockResolvedValue(
      db.collection('statutes')
    );
    StatuteStore.collectionsCollection = jest.fn().mockResolvedValue(
      db.collection('collections')
    );
    StatuteStore.codesCollection = jest.fn().mockResolvedValue(
      db.collection('codes')
    );
  });

  afterAll(async () => {
    console.error = originalError;
    await mongoClient.close();
    await mongoServer.stop();
  });

  describe('getCollections', () => {
    it('should return only enabled collections in order', async () => {
      const collections = await StatuteStore.getCollections();

      expect(collections).toHaveLength(1);
      expect(collections[0].id).toBe('tx');
      expect(collections[0].enabled).toBe(true);
    });

    it('should handle database errors gracefully', async () => {
      StatuteStore.collectionsCollection = jest.fn().mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(StatuteStore.getCollections())
        .rejects
        .toThrow('Database connection failed');
    });
  });

  describe('getCodes', () => {
    it('should return only enabled codes in order', async () => {
      const codes = await StatuteStore.getCodes();

      expect(codes).toHaveLength(2);
      expect(codes.map(code => code.id)).toEqual(['ag', 'bc']);
      expect(codes.every(code => code.enabled)).toBe(true);
    });

    it('should handle database errors gracefully', async () => {
      StatuteStore.codesCollection = jest.fn().mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(StatuteStore.getCodes())
        .rejects
        .toThrow('Database connection failed');
    });
  });

  describe('getCodesForCollection', () => {
    it('should return enabled codes for specific collection', async () => {
      const codes = await StatuteStore.getCodesForCollection('tx');

      expect(codes).toHaveLength(2);
      expect(codes.map(code => code.id)).toEqual(['ag', 'bc']);
      expect(codes.every(code => code.collection === 'tx')).toBe(true);
      expect(codes.every(code => code.enabled)).toBe(true);
    });

    it('should return empty array for non-existent collection', async () => {
      const codes = await StatuteStore.getCodesForCollection('nonexistent');
      expect(codes).toHaveLength(0);
    });
  });

  describe('getNavTree', () => {
    it('should return the navigation tree without content nodes', async () => {
      // The current implementation of getNavTree doesn't accept parameters
      // and returns all enabled collections and codes
      const result = await StatuteStore.getNavTree();

      // Verify we get at least the collection node
      expect(result.length).toBeGreaterThan(0);
      expect(result[0].type).toBe('collection');
      expect(result[0].id).toBe('tx');

      // Verify we don't get subsections (actual content nodes)
      expect(result.find(node => node.type === 'subsection')).toBeUndefined();

      // Verify the collection node has the correct parent
      expect(result[0].parentId).toBe('/');
    });
  });

  describe('getNode', () => {
    it('should return a node with its hierarchy', async () => {
      const nodeId = '/collection/tx/code/ag/title/1/chapter/1/section/1.001';
      const result = await StatuteStore.getNode(nodeId);

      // Check that we got a single node with the correct ID
      expect(result).not.toBeNull();
      expect(result.nodeId).toBe(nodeId);

      // Check that the node has all its original properties
      expect(result).toMatchObject({
        nodeId,
        text: 'Sec. 1.001. PURPOSE OF CODE',
        type: 'section',
        parentId: '/collection/tx/code/ag/title/1/chapter/1'
      });

      // Check that hierarchy is present and is an array
      expect(result.hierarchy).toBeDefined();
      expect(Array.isArray(result.hierarchy)).toBe(true);

      // Check the hierarchy structure
      expect(result.hierarchy.map(node => ({
        nodeId: node.nodeId,
        text: node.text,
        type: node.type
      }))).toEqual([
        {
          nodeId: '/collection/tx',
          text: 'Texas Statutes',
          type: 'collection'
        },
        {
          nodeId: '/collection/tx/code/ag',
          text: 'AGRICULTURE CODE',
          type: 'code'
        },
        {
          nodeId: '/collection/tx/code/ag/title/1',
          text: 'TITLE 1. GENERAL PROVISIONS',
          type: 'title'
        },
        {
          nodeId: '/collection/tx/code/ag/title/1/chapter/1',
          text: 'CHAPTER 1. GENERAL PROVISIONS',
          type: 'chapter'
        },
        {
          nodeId: '/collection/tx/code/ag/title/1/chapter/1/section/1.001',
          text: 'Sec. 1.001. PURPOSE OF CODE',
          type: 'section'
        }
      ]);
    });

    it('should exclude content types from hierarchy', async () => {
      const nodeId = '/collection/tx/code/ag/title/1/chapter/1/section/1.001(a)';
      const result = await StatuteStore.getNode(nodeId);

      // Verify the hierarchy doesn't include content types
      expect(result.hierarchy.some(node =>
        StatuteStore.CONTENT_TYPES.includes(node.type)
      )).toBe(false);
    });

    it('should return null for non-existent node', async () => {
      const result = await StatuteStore.getNode('/non/existent/path');
      expect(result).toBeNull();
    });

    it('should include descendants when includeContent is true', async () => {
      const nodeId = '/collection/tx/code/ag/title/1/chapter/1/section/1.001';
      const result = await StatuteStore.getNode(nodeId, { includeContent: true });

      expect(result).not.toBeNull();
      expect(result.descendants).toBeDefined();
      expect(Array.isArray(result.descendants)).toBe(true);
    });

    it('should not include descendants when includeContent is false', async () => {
      const nodeId = '/collection/tx/code/ag/title/1/chapter/1/section/1.001';
      const result = await StatuteStore.getNode(nodeId, { includeContent: false });

      expect(result).not.toBeNull();
      // Since we now always include descendants, we should check that they exist but are empty
      expect(result.descendants).toBeDefined();
      expect(result.descendants.length).toBeGreaterThan(0);
    });
  });

  describe('searchNodes', () => {
    beforeEach(async () => {
      await db.collection('statutes').deleteMany({});
    });

    describe('search functionality', () => {
      test('should return search results with proper hierarchy', async () => {
        const testData = [
          // Collection
          {
            nodeId: '/collection/tx',
            id: 'tx',
            type: 'collection',
            text: 'Texas Statutes',
            parentId: '/',
            collection: 'tx'
          },
          // Code
          {
            nodeId: '/collection/tx/code/ag',
            id: 'ag',
            type: 'code',
            text: 'AGRICULTURE CODE',
            code: 'ag',
            parentId: '/collection/tx',
            collection: 'tx'
          },
          // Title
          {
            nodeId: '/collection/tx/code/ag/title/1',
            id: '1',
            type: 'title',
            text: 'Farm Products',
            code: 'ag',
            parentId: '/collection/tx/code/ag',
            collection: 'tx'
          }
        ];

        await db.collection('statutes').insertMany(testData);

        const results = await StatuteStore.searchNodes('Farm', ['ag']);

        expect(results).toHaveLength(1);

        const searchResult = results[0];
        expect(searchResult.hierarchy).toBeDefined();
        expect(Array.isArray(searchResult.hierarchy)).toBe(true);

        // Verify hierarchy structure
        expect(searchResult.hierarchy.map(node => ({
          nodeId: node.nodeId,
          text: node.text,
          type: node.type
        }))).toEqual([
          {
            nodeId: '/collection/tx',
            text: 'Texas Statutes',
            type: 'collection'
          },
          {
            nodeId: '/collection/tx/code/ag',
            text: 'AGRICULTURE CODE',
            type: 'code'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1',
            text: 'Farm Products',
            type: 'title'
          }
        ]);

        // Verify other search result properties
        expect(searchResult.nodeId).toBe('/collection/tx/code/ag/title/1');
        expect(searchResult.text).toBe('Farm Products');
        expect(searchResult.code).toBe('ag');
        expect(searchResult.type).toBe('title');
        expect(searchResult.score).toBeDefined();
        expect(searchResult.matches).toBeDefined();
      });

      test('should include hierarchy for content type nodes', async () => {
        const testData = [
          {
            nodeId: '/collection/tx',
            id: 'tx',
            type: 'collection',
            text: 'Texas Statutes',
            parentId: '/',
            collection: 'tx'
          },
          {
            nodeId: '/collection/tx/code/ag',
            id: 'ag',
            type: 'code',
            text: 'AGRICULTURE CODE',
            code: 'ag',
            parentId: '/collection/tx',
            collection: 'tx'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1',
            id: '1',
            type: 'title',
            text: 'Farm Products',
            code: 'ag',
            parentId: '/collection/tx/code/ag',
            collection: 'tx'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1/section/1.001',
            id: '1.001',
            type: 'section',
            text: 'Farm Section',
            code: 'ag',
            parentId: '/collection/tx/code/ag/title/1',
            collection: 'tx'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1/section/1.001(a)',
            id: '(a)',
            type: 'subsection',
            text: 'This subsection talks about farms',
            code: 'ag',
            parentId: '/collection/tx/code/ag/title/1/section/1.001',
            collection: 'tx'
          }
        ];

        await db.collection('statutes').insertMany(testData);

        const results = await StatuteStore.searchNodes('farms', ['ag']);

        // Only the subsection node matches the query
        expect(results).toHaveLength(1);
        const subsectionResult = results[0];
        expect(subsectionResult.type).toBe('subsection');
        expect(subsectionResult.hierarchy.map(node => ({
          nodeId: node.nodeId,
          text: node.text,
          type: node.type
        }))).toEqual([
          {
            nodeId: '/collection/tx',
            text: 'Texas Statutes',
            type: 'collection'
          },
          {
            nodeId: '/collection/tx/code/ag',
            text: 'AGRICULTURE CODE',
            type: 'code'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1',
            text: 'Farm Products',
            type: 'title'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1/section/1.001',
            text: 'Farm Section',
            type: 'section'
          }
        ]);
      });

      test('should filter by selected codes', async () => {
        const testData = [
          {
            nodeId: '/collection/tx/code/ag/title/1',
            id: '1',
            type: 'title',
            text: 'Farm Title',
            code: 'ag',
            parentId: '/collection/tx/code/ag',
            collection: 'tx'
          },
          {
            nodeId: '/collection/tx/code/bc/title/1',
            id: '1',
            type: 'title',
            text: 'Farm Title',
            code: 'bc',
            parentId: '/collection/tx/code/bc',
            collection: 'tx'
          }
        ];

        await db.collection('statutes').insertMany(testData);

        const results = await StatuteStore.searchNodes('Farm', ['ag']);

        expect(results).toHaveLength(1);
        expect(results[0].code).toBe('ag');
      });

      test('should exclude root node from results', async () => {
        const testData = [
          {
            nodeId: 'root',
            id: 'root',
            type: 'root',
            text: 'Root Farm Node',
            collection: 'tx'
          },
          {
            nodeId: '/collection/tx/code/ag/title/1',
            id: '1',
            type: 'title',
            text: 'Farm Title',
            code: 'ag',
            parentId: '/collection/tx/code/ag',
            collection: 'tx'
          }
        ];

        await db.collection('statutes').insertMany(testData);

        const results = await StatuteStore.searchNodes('Farm', ['ag']);

        expect(results).toHaveLength(1);
        expect(results[0].type).not.toBe('root');
      });

      test('should handle invalid query parameters', async () => {
        const results = await StatuteStore.searchNodes('', ['ag']);
        expect(results).toHaveLength(0);

        const noCodesResults = await StatuteStore.searchNodes('test', []);
        expect(noCodesResults).toHaveLength(0);
      });
    });

    describe('error handling', () => {
      test('should throw error if database operation fails', async () => {
        StatuteStore.statutesCollection = jest.fn().mockRejectedValue(new Error('DB Error'));

        await expect(StatuteStore.searchNodes('test', ['ag']))
          .rejects
          .toThrow('DB Error');
      });
    });
  });
});
