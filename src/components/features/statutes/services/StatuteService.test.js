import { StatuteService } from './StatuteService';
import { Node } from '../models/Node';

// Mock fetch globally
global.fetch = jest.fn();

describe('StatuteService', () => {
  // Mock console.error before all tests
  const originalConsoleError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  // Restore console.error after all tests
  afterAll(() => {
    console.error = originalConsoleError;
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getNavTree', () => {
    const mockRawNodes = [
      { id: '1', nodeId: 'root', type: 'root', text: 'Root Node' },
      { id: '2', nodeId: 'collection1', type: 'collection', text: 'Collection 1', parentId: 'root' },
      { id: '3', nodeId: 'code1', type: 'code', text: 'Code 1', parentId: 'collection1' }
    ];

    it('fetches and transforms navigation tree successfully', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockRawNodes)
      });

      const result = await StatuteService.getNavTree();

      expect(fetch).toHaveBeenCalledWith('/api/v1/statutes');
      expect(result).toHaveLength(3);
      expect(result[0]).toBeInstanceOf(Node);
      expect(result[0].children[0]).toBeInstanceOf(Node);
      expect(result[0].children[0].type).toBe('collection');
      expect(result[0].children[0].children[0].type).toBe('code');
    });

    it('handles API error', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      });

      await expect(StatuteService.getNavTree()).rejects.toThrow('Failed to fetch navigation tree: Not Found');
    });

    it('handles network error', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(StatuteService.getNavTree()).rejects.toThrow('Network error');
    });
  });

  describe('getNode', () => {
    const mockNodeData = {
      id: '1',
      nodeId: 'test-node',
      type: 'section',
      text: 'Test Node',
      hierarchy: [
        { id: '2', nodeId: 'parent', type: 'code', text: 'Parent Node' }
      ],
      descendants: [
        { id: '3', nodeId: 'child', type: 'subsection', text: 'Child Node' }
      ]
    };

    it('fetches and transforms node successfully', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockNodeData)
      });

      const result = await StatuteService.getNode('test-node');

      expect(fetch).toHaveBeenCalledWith('/api/v1/statutes/test-node');
      expect(result).toBeInstanceOf(Node);
      expect(result.nodeId).toBe('test-node');
      expect(result.getHierarchy()).toHaveLength(1);
      expect(result.descendants).toHaveLength(1);
    });

    it('handles invalid nodeId', async () => {
      await expect(StatuteService.getNode('')).rejects.toThrow('Invalid nodeId');
      await expect(StatuteService.getNode(null)).rejects.toThrow('Invalid nodeId');
      await expect(StatuteService.getNode(undefined)).rejects.toThrow('Invalid nodeId');
    });

    it('normalizes nodeId path', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockNodeData)
      });

      await StatuteService.getNode('/test/path');
      expect(fetch).toHaveBeenCalledWith('/api/v1/statutes/test/path');
    });
  });

  describe('getAvailableCodes', () => {
    const mockCodes = [
      { code: 'CODE1', title: 'Code 1' },
      { code: 'CODE2', title: 'Code 2' }
    ];

    it('fetches available codes successfully', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCodes)
      });

      const result = await StatuteService.getAvailableCodes();

      expect(fetch).toHaveBeenCalledWith('/api/v1/codes');
      expect(result).toEqual(mockCodes);
    });

    it('handles API error', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      });

      await expect(StatuteService.getAvailableCodes()).rejects.toThrow('Failed to fetch available codes');
    });
  });

  describe('getContent', () => {
    const mockContentData = {
      selectedNode: 'code1',
      content: [
        {
          id: '1',
          nodeId: 'collection1',
          type: 'collection',
          text: 'Collection 1',
          code: 'CODE1',
          collection: 'COLL1',
          descendants: [
            {
              id: '2',
              nodeId: 'code1',
              type: 'code',
              text: 'Code 1',
              parentId: 'collection1',
              code: 'CODE1',
              collection: 'COLL1',
              descendants: [
                {
                  id: '3',
                  nodeId: 'title1',
                  type: 'title',
                  text: 'Title 1',
                  parentId: 'code1',
                  code: 'CODE1',
                  collection: 'COLL1',
                  descendants: [
                    {
                      id: '4',
                      nodeId: 'subsection1',
                      type: 'subsection',
                      text: 'Subsection 1',
                      parentId: 'title1',
                      code: 'CODE1',
                      collection: 'COLL1'
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    };

    it('returns root node content when "/" is requested', async () => {
      // Mock the getNavTree call that getContent makes internally
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([
          { id: 'root', nodeId: '/', type: 'root', text: 'Statute Browser' }
        ])
      });

      const result = await StatuteService.getContent('/');

      expect(result).toEqual({
        selectedNode: '/',
        content: [expect.objectContaining({
          nodeId: '/',
          type: 'root',
          text: 'Statute Browser',
          id: 'root'
        })]
      });
      expect(result.content[0]).toBeInstanceOf(Node);
      // Verify fetch was called for getNavTree
      expect(fetch).toHaveBeenCalledWith('/api/v1/statutes');
    });

    it('handles invalid nodeId', async () => {
      await expect(StatuteService.getContent('')).rejects.toThrow('Invalid nodeId: must be a non-empty string');
      await expect(StatuteService.getContent(null)).rejects.toThrow('Invalid nodeId: must be a non-empty string');
      await expect(StatuteService.getContent(undefined)).rejects.toThrow('Invalid nodeId: must be a non-empty string');
    });

    it('handles empty content array in response', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          selectedNode: 'test-node',
          content: []
        })
      });

      const result = await StatuteService.getContent('test-node');
      expect(result.content).toEqual([]);
    });

    it('preserves code and collection in nested subsections', async () => {
      const contentWithNestedSubsections = {
        selectedNode: 'section1',
        content: [{
          id: '1',
          nodeId: 'section1',
          type: 'section',
          text: 'Section 1',
          code: 'TEST_CODE',
          collection: 'TEST_COLL',
          descendants: [{
            id: '2',
            nodeId: 'sub1',
            type: 'subsection',
            text: 'Subsection 1',
            parentId: 'section1',
            descendants: [{
              id: '3',
              nodeId: 'sub2',
              type: 'subsection',
              text: 'Subsection 2',
              parentId: 'sub1'
            }]
          }]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(contentWithNestedSubsections)
      });

      const result = await StatuteService.getContent('section1');
      const section = result.content[0];
      const subsection1 = section.children[0];
      const subsection2 = subsection1.children[0];

      // Verify code and collection are inherited
      expect(subsection1.code).toBe('TEST_CODE');
      expect(subsection1.collection).toBe('TEST_COLL');
      expect(subsection2.code).toBe('TEST_CODE');
      expect(subsection2.collection).toBe('TEST_COLL');
    });

    it('fetches and transforms content successfully with proper relationships', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockContentData)
      });

      const result = await StatuteService.getContent('code1');

      expect(fetch).toHaveBeenCalledWith('/api/v1/content/code1');
      expect(result.selectedNode).toBe('code1');

      // Should only return root nodes (collection in this case)
      expect(result.content).toHaveLength(1);

      // Verify collection node
      const collection = result.content[0];
      expect(collection).toBeInstanceOf(Node);
      expect(collection.type).toBe('collection');
      expect(collection.code).toBe('CODE1');
      expect(collection.collection).toBe('COLL1');

      // Verify code node (child of collection)
      expect(collection.children).toHaveLength(1);
      const code = collection.children[0];
      expect(code).toBeInstanceOf(Node);
      expect(code.type).toBe('code');
      expect(code.parent).toBe(collection);
      expect(code.code).toBe('CODE1');
      expect(code.collection).toBe('COLL1');

      // Verify title node (child of code)
      expect(code.children).toHaveLength(1);
      const title = code.children[0];
      expect(title).toBeInstanceOf(Node);
      expect(title.type).toBe('title');
      expect(title.parent).toBe(code);
      expect(title.code).toBe('CODE1');
      expect(title.collection).toBe('COLL1');

      // Verify subsection (child of title)
      expect(title.children).toHaveLength(1);
      const subsection = title.children[0];
      expect(subsection).toBeInstanceOf(Node);
      expect(subsection.type).toBe('subsection');
      expect(subsection.parent).toBe(title);
      expect(subsection.code).toBe('CODE1');
      expect(subsection.collection).toBe('COLL1');
    });

    it('transforms nested subsections correctly', async () => {
      const deeplyNestedContent = {
        selectedNode: 'section1',
        content: [{
          id: '1',
          nodeId: 'section1',
          type: 'section',
          text: 'Section 1',
          code: 'CODE1',
          collection: 'COLL1',
          descendants: [{
            id: '2',
            nodeId: 'sub1',
            type: 'subsection',
            text: 'Level 1',
            parentId: 'section1',
            descendants: [{
              id: '3',
              nodeId: 'sub2',
              type: 'subsection',
              text: 'Level 2',
              parentId: 'sub1'
            }]
          }]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(deeplyNestedContent)
      });

      const result = await StatuteService.getContent('section1');
      const section = result.content[0];

      // Verify first level subsection
      expect(section.children).toHaveLength(1);
      const level1 = section.children[0];
      expect(level1).toBeInstanceOf(Node);
      expect(level1.type).toBe('subsection');
      expect(level1.parent).toBe(section);
      expect(level1.code).toBe('CODE1');
      expect(level1.collection).toBe('COLL1');

      // Verify second level subsection
      expect(level1.children).toHaveLength(1);
      const level2 = level1.children[0];
      expect(level2).toBeInstanceOf(Node);
      expect(level2.type).toBe('subsection');
      expect(level2.parent).toBe(level1);
      expect(level2.code).toBe('CODE1');
      expect(level2.collection).toBe('COLL1');
    });

    it('handles invalid response structure', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ invalidData: true })
      });

      await expect(StatuteService.getContent('test-node')).rejects.toThrow('Invalid content data in response');
    });

    it('normalizes nodeId path', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockContentData)
      });

      await StatuteService.getContent('/test/path');
      expect(fetch).toHaveBeenCalledWith('/api/v1/content/test/path');
    });

    it('handles API error', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      });

      await expect(StatuteService.getContent('test-node')).rejects.toThrow('Failed to fetch content: Not Found');
    });

    it('handles network error', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(StatuteService.getContent('test-node')).rejects.toThrow('Network error');
    });

    it('processes chapter nodes with descendants correctly', async () => {
      const contentWithChapterDescendants = {
        selectedNode: 'chapter1',
        content: [{
          id: '1',
          nodeId: 'chapter1',
          type: 'chapter',
          text: 'Chapter 1',
          code: 'TEST_CODE',
          collection: 'TEST_COLL',
          descendants: [
            {
              id: '2',
              nodeId: 'section1',
              type: 'section',
              text: 'Section 1',
              parentId: 'chapter1',
              descendants: [
                {
                  id: '3',
                  nodeId: 'subsection1',
                  type: 'subsection',
                  text: 'Subsection 1',
                  parentId: 'section1'
                }
              ]
            },
            {
              id: '4',
              nodeId: 'section2',
              type: 'section',
              text: 'Section 2',
              parentId: 'chapter1'
            }
          ]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(contentWithChapterDescendants)
      });

      const result = await StatuteService.getContent('chapter1');

      // Verify chapter structure
      const chapter = result.content[0];
      expect(chapter.type).toBe('chapter');
      expect(chapter.nodeId).toBe('chapter1');

      // Verify descendants
      expect(chapter.children).toHaveLength(2);
      expect(chapter.children[0].type).toBe('section');
      expect(chapter.children[1].type).toBe('section');

      // Verify nested subsections
      const section1 = chapter.children[0];
      expect(section1.children).toHaveLength(1);
      expect(section1.children[0].type).toBe('subsection');

      // Verify code/collection inheritance
      expect(section1.code).toBe('TEST_CODE');
      expect(section1.collection).toBe('TEST_COLL');
      expect(section1.children[0].code).toBe('TEST_CODE');
      expect(section1.children[0].collection).toBe('TEST_COLL');
    });

    it('processes subchapter nodes with descendants correctly', async () => {
      const contentWithSubchapterDescendants = {
        selectedNode: 'subchapter1',
        content: [{
          id: '1',
          nodeId: 'subchapter1',
          type: 'subchapter',
          text: 'Subchapter 1',
          code: 'TEST_CODE',
          collection: 'TEST_COLL',
          descendants: [
            {
              id: '2',
              nodeId: 'section1',
              type: 'section',
              text: 'Section 1',
              parentId: 'subchapter1',
              descendants: [
                {
                  id: '3',
                  nodeId: 'subsection1',
                  type: 'subsection',
                  text: 'Subsection 1',
                  parentId: 'section1'
                }
              ]
            }
          ]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(contentWithSubchapterDescendants)
      });

      const result = await StatuteService.getContent('subchapter1');

      // Verify subchapter structure
      const subchapter = result.content[0];
      expect(subchapter.type).toBe('subchapter');
      expect(subchapter.nodeId).toBe('subchapter1');

      // Verify descendants
      expect(subchapter.children).toHaveLength(1);
      const section = subchapter.children[0];
      expect(section.type).toBe('section');
      expect(section.children).toHaveLength(1);

      // Verify code/collection inheritance
      expect(section.code).toBe('TEST_CODE');
      expect(section.collection).toBe('TEST_COLL');
      expect(section.children[0].code).toBe('TEST_CODE');
      expect(section.children[0].collection).toBe('TEST_COLL');
    });

    it('processes article nodes with complex descendants correctly', async () => {
      const contentWithArticleDescendants = {
        selectedNode: 'article1',
        content: [{
          id: '1',
          nodeId: 'article1',
          type: 'article',
          text: 'Article 1',
          code: 'TEST_CODE',
          collection: 'TEST_COLL',
          descendants: [
            {
              id: '2',
              nodeId: 'subarticle1',
              type: 'subarticle',
              text: 'Subarticle 1',
              parentId: 'article1',
              descendants: [
                {
                  id: '3',
                  nodeId: 'section1',
                  type: 'section',
                  text: 'Section 1',
                  parentId: 'subarticle1',
                  descendants: [
                    {
                      id: '4',
                      nodeId: 'subsection1',
                      type: 'subsection',
                      text: 'Subsection 1',
                      parentId: 'section1',
                      descendants: [
                        {
                          id: '5',
                          nodeId: 'subsubsection1',
                          type: 'subsection',
                          text: 'Sub-subsection 1',
                          parentId: 'subsection1'
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(contentWithArticleDescendants)
      });

      const result = await StatuteService.getContent('article1');

      // Verify article structure
      const article = result.content[0];
      expect(article.type).toBe('article');
      expect(article.nodeId).toBe('article1');

      // Verify first level descendants (subarticle)
      expect(article.children).toHaveLength(1);
      const subarticle = article.children[0];
      expect(subarticle.type).toBe('subarticle');

      // Verify second level descendants (section)
      expect(subarticle.children).toHaveLength(1);
      const section = subarticle.children[0];
      expect(section.type).toBe('section');

      // Verify subsections and nested subsections
      expect(section.children).toHaveLength(1);
      const subsection = section.children[0];
      expect(subsection.children).toHaveLength(1);

      // Verify code/collection inheritance at all levels
      expect(subarticle.code).toBe('TEST_CODE');
      expect(subarticle.collection).toBe('TEST_COLL');
      expect(section.code).toBe('TEST_CODE');
      expect(section.collection).toBe('TEST_COLL');
      expect(subsection.code).toBe('TEST_CODE');
      expect(subsection.collection).toBe('TEST_COLL');
      expect(subsection.children[0].code).toBe('TEST_CODE');
      expect(subsection.children[0].collection).toBe('TEST_COLL');
    });

    it('handles duplicate nodes in descendants correctly', async () => {
      const contentWithDuplicateDescendants = {
        selectedNode: 'chapter1',
        content: [{
          id: '1',
          nodeId: 'chapter1',
          type: 'chapter',
          text: 'Chapter 1',
          code: 'TEST_CODE',
          collection: 'TEST_COLL',
          descendants: [
            {
              id: '2',
              nodeId: 'section1',
              type: 'section',
              text: 'Section 1',
              parentId: 'chapter1'
            },
            {
              id: '2',  // Same node appearing twice
              nodeId: 'section1',
              type: 'section',
              text: 'Section 1',
              parentId: 'chapter1'
            }
          ]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(contentWithDuplicateDescendants)
      });

      const result = await StatuteService.getContent('chapter1');

      // Verify chapter structure
      const chapter = result.content[0];
      expect(chapter.type).toBe('chapter');

      // Verify descendants (should only have one copy)
      expect(chapter.children).toHaveLength(1);
      expect(chapter.children[0].type).toBe('section');
      expect(chapter.children[0].nodeId).toBe('section1');
    });

    it('handles circular references in descendants gracefully', async () => {
      const contentWithCircularReferences = {
        selectedNode: 'chapter1',
        content: [{
          id: '1',
          nodeId: 'chapter1',
          type: 'chapter',
          text: 'Chapter 1',
          code: 'TEST_CODE',
          collection: 'TEST_COLL',
          descendants: [
            {
              id: '2',
              nodeId: 'section1',
              type: 'section',
              text: 'Section 1',
              parentId: 'chapter1',
              descendants: [
                {
                  id: '1',  // Circular reference back to chapter
                  nodeId: 'chapter1',
                  type: 'chapter',
                  text: 'Chapter 1',
                  parentId: 'section1'
                }
              ]
            }
          ]
        }]
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(contentWithCircularReferences)
      });

      const result = await StatuteService.getContent('chapter1');

      // Verify structure (should not cause infinite recursion)
      const chapter = result.content[0];
      expect(chapter.type).toBe('chapter');
      expect(chapter.children).toHaveLength(1);
      expect(chapter.children[0].type).toBe('section');

      // The circular reference should be ignored
      expect(chapter.children[0].children).toHaveLength(0);
    });
  });
});
