import { Node } from '../models/Node';

const API_VERSION = 'v1';
const STATUTES_URL = `/api/${API_VERSION}/statutes`;
const CONTENT_URL = `/api/${API_VERSION}/content`;
const FINDNODE_URL = `/api/${API_VERSION}/findnode`;

/**
 * Service class for interacting with the statutes API.
 * Handles fetching and transforming statute data from the server.
 */
export class StatuteService {
  /**
   * Fetches a filtered array of navigation nodes for efficient navigation.
   * Returns nodes of the same code and type as the current node, pre-sorted and ready for navigation calculations.
   *
   * @param {string} nodeId - The current node ID to determine code and type filtering
   * @returns {Promise<Object[]>} Array of filtered navigation nodes
   * @throws {Error} If the API request fails or returns invalid data
   */
  static async getFlatNavTree(nodeId) {
    try {
      const url = nodeId 
        ? `${STATUTES_URL}/flat?nodeId=${encodeURIComponent(nodeId)}`
        : `${STATUTES_URL}/flat`;
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch flat navigation tree: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error in StatuteService.getFlatNavTree:', error);
      throw error;
    }
  }

  /**
   * Fetches the complete navigation tree of statutes.
   * This includes all collections, codes, and their relationships.
   *
   * @returns {Promise<Node[]>} Array of Node instances with parent-child relationships established
   * @throws {Error} If the API request fails or returns invalid data
   */
  static async getNavTree() {
    try {
      // 1. Fetch data
      const response = await fetch(STATUTES_URL);
      if (!response.ok) {
        throw new Error(`Failed to fetch navigation tree: ${response.statusText}`);
      }
      const rawNodes = await response.json();

      // 2. Sort nodes client-side to avoid MongoDB memory limits
      const sortedRawNodes = rawNodes.sort((a, b) => {
        // First priority: root nodes come first
        const aIsRoot = !a.parentId;
        const bIsRoot = !b.parentId;
        if (aIsRoot && !bIsRoot) return -1;
        if (!aIsRoot && bIsRoot) return 1;
        
        // Second priority: sort by order if available
        if (a.order !== undefined && b.order !== undefined) {
          return a.order - b.order;
        }
        
        // Fallback to nodeId comparison for consistent ordering
        return (a.nodeId || '').localeCompare(b.nodeId || '');
      });

      // 3. Create Node instances
      const nodes = sortedRawNodes.map(nodeData => new Node(nodeData));

      // 4. Build parent-child relationships
      const nodeMap = new Map(nodes.map(node => [node.nodeId, node]));

      nodes.forEach(node => {
        if (node.parentId) {
          const parent = nodeMap.get(node.parentId);
          if (parent) {
            node.parent = parent;
            parent.children = parent.children || [];
            parent.children.push(node);
          } else {
            console.warn(`Parent node not found for: ${node.nodeId} (parentId: ${node.parentId})`);
          }
        }
      });

      // Sort children for each node to maintain consistent ordering
      nodes.forEach(node => {
        if (node.children && node.children.length > 0) {
          node.children.sort((a, b) => {
            // First sort by order if available
            if (a.order !== undefined && b.order !== undefined) {
              return a.order - b.order;
            }
            // Fallback to nodeId comparison for consistent ordering
            return (a.nodeId || '').localeCompare(b.nodeId || '');
          });
        }
      });

      return nodes;
    } catch (error) {
      console.error('Error in StatuteService.getNavTree:', error);
      throw error;
    }
  }

  /**
   * Fetches a specific node and its associated data (hierarchy and content).
   *
   * @param {string} nodeId - The unique identifier of the node to fetch
   * @returns {Promise<Node>} A Node instance with hierarchy and content data
   * @throws {Error} If the nodeId is invalid or the API request fails
   */
  static async getNode(nodeId) {
    try {
      if (!nodeId || typeof nodeId !== 'string') {
        console.error('Invalid nodeId passed to getNode:', nodeId);
        throw new Error('Invalid nodeId: must be a non-empty string');
      }

      // Normalize the nodeId to ensure consistent format
      const normalizedNodeId = nodeId.startsWith('/') ? nodeId : '/' + nodeId;
      const path = normalizedNodeId.split('/').filter(Boolean).join('/');

      const response = await fetch(`${STATUTES_URL}/${path}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch node: ${response.statusText}`);
      }
      const data = await response.json();

      // Validate the response structure
      if (!data || typeof data !== 'object' || !data.nodeId || !data.type) {
        console.error('Invalid response data:', data);
        throw new Error('Invalid node data in response');
      }

      // Create the target node instance
      const targetNode = new Node(data);

      // Set hierarchy if present
      if (Array.isArray(data.hierarchy)) {
        const hierarchyNodes = data.hierarchy.map(nodeData => new Node(nodeData));
        targetNode.setHierarchy(hierarchyNodes);
      }

      // Set descendants if present
      if (Array.isArray(data.descendants)) {
        targetNode.descendants = data.descendants.map(nodeData => new Node(nodeData));
      }

      return targetNode;
    } catch (error) {
      console.error('Error in getNode:', error);
      throw error;
    }
  }

  /**
   * Fetches the list of available statute codes.
   *
   * @returns {Promise<Array>} Array of available code objects
   * @throws {Error} If the API request fails
   */
  static async getAvailableCodes() {
    try {
      const response = await fetch('/api/v1/codes');
      if (!response.ok) {
        throw new Error('Failed to fetch available codes');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching available codes:', error);
      throw error;
    }
  }

  /**
   * Fetches content for a specific node.
   * For the root node ('/'), returns a special root content structure.
   *
   * @param {string} nodeId - The ID of the node to fetch content for
   * @returns {Promise<Object>} Object containing selectedNode and content array
   * @throws {Error} If nodeId is invalid (null, undefined, or empty string)
   */
  static async getContent(nodeId) {
    try {
      // Validate nodeId
      if (nodeId === undefined || nodeId === null || nodeId === '') {
        throw new Error('Invalid nodeId: must be a non-empty string');
      }

      // Handle explicit root node case
      if (nodeId === '/') {
        // Get the full navigation tree to provide collection children
        const navNodes = await this.getNavTree();
        const rootNode = navNodes.find(node => node.nodeId === '/');
        
        if (rootNode) {
          return {
            selectedNode: '/',
            content: [rootNode]
          };
        } else {
          // Fallback if root node not found in nav tree
          return {
            selectedNode: '/',
            content: [new Node({
              nodeId: '/',
              type: 'root',
              text: 'Statute Browser',
              id: 'root'
            })]
          };
        }
      }

      // For all other nodes, proceed with normal content fetch
      // Normalize the nodeId to ensure consistent format
      const normalizedNodeId = nodeId.startsWith('/') ? nodeId : '/' + nodeId;
      const path = normalizedNodeId.split('/').filter(Boolean).join('/');

      const response = await fetch(`${CONTENT_URL}/${path}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch content: ${response.statusText}`);
      }

      const data = await response.json();

      // Validate response structure
      if (!data || !Array.isArray(data.content)) {
        console.error('Invalid content response:', data);
        throw new Error('Invalid content data in response');
      }

      // First pass: Create all nodes without relationships
      const nodeMap = new Map();
      data.content.forEach(nodeData => {
        const node = new Node({
          ...nodeData,
          code: nodeData.code,
          collection: nodeData.collection
        });
        nodeMap.set(nodeData.nodeId, node);
      });

      // Second pass: Set up parent-child relationships between main content nodes
      data.content.forEach(nodeData => {
        const node = nodeMap.get(nodeData.nodeId);

        // If this node has a parent in our nodeMap, establish the relationship
        if (nodeData.parentId && nodeMap.has(nodeData.parentId)) {
          const parent = nodeMap.get(nodeData.parentId);
          parent.addChild(node);
        }

        // Handle descendants if present (for chapters, subchapters, articles)
        if (Array.isArray(nodeData.descendants)) {
          const processDescendants = (descendants, parentNode) => {
            descendants.forEach(descendantData => {
              // Skip if we already processed this node
              if (nodeMap.has(descendantData.nodeId)) {
                return;
              }

              const descendantNode = new Node({
                ...descendantData,
                code: parentNode.code,
                collection: parentNode.collection
              });

              // Add to nodeMap to prevent duplicate processing
              nodeMap.set(descendantData.nodeId, descendantNode);

              // Use addChild to properly set up the relationship
              if (descendantData.parentId && nodeMap.has(descendantData.parentId)) {
                const parent = nodeMap.get(descendantData.parentId);
                parent.addChild(descendantNode);
              }

              // Process nested descendants recursively
              if (Array.isArray(descendantData.descendants)) {
                processDescendants(descendantData.descendants, descendantNode);
              }
            });
          };

          processDescendants(nodeData.descendants, node);
        }

        // Set hierarchy if available
        if (Array.isArray(nodeData.hierarchy)) {
          node.setHierarchy(nodeData.hierarchy);
        }
      });

      // Find the root nodes (nodes without parents in our set)
      const rootNodes = Array.from(nodeMap.values()).filter(node =>
        !node.parentId || !nodeMap.has(node.parentId)
      );

      const transformedResponse = {
        selectedNode: data.selectedNode,
        content: rootNodes
      };

      return transformedResponse;
    } catch (error) {
      console.error('Error in getContent:', error);
      throw error;
    }
  }

  /**
   * Finds a node by matching both the start and end of its path
   * This method is used by reference resolvers to find nodes based on partial path information
   *
   * @param {string} startPath - The beginning of the node path (e.g., "/collection/tx/code/gv")
   * @param {string} endPath - The end of the node path (e.g., "/section/411.087")
   * @returns {Promise<Node|null>} - The found node or null if not found
   */
  static async findNodeByStartEndPath(startPath, endPath) {
    try {
      if (!startPath || !endPath) {
        return null;
      }

      // Encode the path parameters
      const encodedStartPath = encodeURIComponent(startPath);
      const encodedEndPath = encodeURIComponent(endPath);

      // Make the API request
      const response = await fetch(`${FINDNODE_URL}?startPath=${encodedStartPath}&endPath=${encodedEndPath}`);

      if (!response.ok) {
        if (response.status === 404) {
          // Node not found is an expected case, return null
          return null;
        }
        throw new Error(`Failed to find node: ${response.statusText}`);
      }

      const data = await response.json();

      // Return null if no data
      if (!data || !data.nodeId) {
        return null;
      }

      // Create and return a Node instance
      return new Node(data);
    } catch (error) {
      console.error('Error in findNodeByStartEndPath:', error);
      return null;
    }
  }
}
