/**
 * Service for navigating between statute nodes using pre-computed flat navigation data.
 * Always uses flat nodes from MongoDB for optimal performance.
 */
export class NavigationService {
  /**
   * Gets the navigation context for a given node using flat navigation data from MongoDB.
   * The flat nodes are already filtered, sorted, and deduplicated at the database level.
   *
   * @param {string} currentNodeId - The nodeId of the current node
   * @param {Object[]} flatNodes - Array of navigation nodes (pre-processed by MongoDB)
   * @returns {Object} Navigation context with previous, current, and next nodes
   */
  static getNavigationContext(currentNodeId, flatNodes) {
    if (!currentNodeId || !Array.isArray(flatNodes) || flatNodes.length === 0) {
      return { previous: null, current: null, next: null };
    }

    // Sanitize nodeId to remove any trailing parentheses from CONTENT_TYPES to keep navigation at section level
    const sanitizedNodeId = currentNodeId.replace(/\(.*/, '');

    // Find current node index - flatNodes are already filtered, sorted, and deduplicated by MongoDB
    const currentIndex = flatNodes.findIndex(node => node.nodeId === sanitizedNodeId);

    if (currentIndex === -1) {
      return { previous: null, current: null, next: null };
    }

    return {
      previous: currentIndex > 0 ? flatNodes[currentIndex - 1] : null,
      current: flatNodes[currentIndex],
      next: currentIndex < flatNodes.length - 1 ? flatNodes[currentIndex + 1] : null
    };
  }

  /**
   * Generates abbreviated display text for a node based on its type and ID.
   * Examples: "Ch. 23", "Sec 1.02", "Art. 5", "Subch. A"
   *
   * @param {Node} node - The node to generate display text for
   * @returns {string} Abbreviated display text
   */
  static getAbbreviatedNodeText(node) {
    if (!node || !node.type || !node.id) return '';

    const typeAbbreviations = {
      'chapter': 'Ch.',
      'subchapter': 'Subch.',
      'section': 'Sec.',
      'subsection': 'Subsec.',
      'article': 'Art.',
      'subarticle': 'Subart.',
      'title': 'Title'
    };

    const abbreviation = typeAbbreviations[node.type];
    if (!abbreviation) {
      // For types without abbreviations, use the text as fallback
      return node.text || node.id;
    }

    // Use the node's ID directly for clean, consistent formatting
    return `${abbreviation} ${node.id}`;
  }
}
