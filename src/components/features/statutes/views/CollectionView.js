import { memo } from 'react';
import PropTypes from 'prop-types';
import { EmptyStateView } from '../components/shared/EmptyStateView';
import { NodeSummaryView } from '../components/shared/NodeSummaryView';
import { getCollectionStats } from '../utils';

export const CollectionView = memo(({ node }) => {
  const stats = getCollectionStats(node);

  return (
    <EmptyStateView node={node}>
      {stats.length > 0 && <NodeSummaryView stats={stats[0]} />}
      <h3 className="text-xl font-semibold mb-2">{node.text}.</h3>
      {node.description && (
        <p className="my-2 text-sm text-gray-500 max-w-md text-left leading-relaxed">
          {node.description}
        </p>
      )}
    </EmptyStateView>
  );
});

CollectionView.propTypes = {
  node: PropTypes.shape({
    nodeId: PropTypes.string.isRequired,
    type: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired,
    description: PropTypes.string
  }).isRequired
};

CollectionView.displayName = 'CollectionView';
