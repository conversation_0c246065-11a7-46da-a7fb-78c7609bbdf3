import { memo } from 'react';
import PropTypes from 'prop-types';
import { EmptyStateView } from '../components/shared/EmptyStateView';
import { NodeSummaryView } from '../components/shared/NodeSummaryView';
import { getCollectionStats } from '../utils';

export const RootView = memo(({ node }) => {
  const stats = getCollectionStats(node);

  return (
    <EmptyStateView node={node} title="Available Statutes">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-8 place-items-center">
        {stats.length === 1 ? (
          <div className="md:col-span-2 flex flex-col items-center">
            <NodeSummaryView stats={stats[0]} />
          </div>
        ) : (
          stats.map((stat, index) => (
            <div key={index} className="flex flex-col items-center">
              <NodeSummaryView stats={stat} />
            </div>
          ))
        )}
      </div>
    </EmptyStateView>
  );
});

RootView.propTypes = {
  node: PropTypes.shape({
    nodeId: PropTypes.string.isRequired,
    type: PropTypes.string.isRequired,
    text: PropTypes.string.isRequired
  }).isRequired
};

RootView.displayName = 'RootView';
