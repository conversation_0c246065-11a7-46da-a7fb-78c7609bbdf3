
// Helper function to find a node in the tree structure
export const findNodeInTree = (nodeId, nodes) => {
  if (!nodes || !Array.isArray(nodes)) return null;

  for (const node of nodes) {
    // Check current node
    if (node.nodeId === nodeId) return node;

    // Check children if they exist
    if (node.children) {
      const found = findNodeInTree(nodeId, node.children);
      if (found) return found;
    }

    // Check subsections if they exist
    if (node.subsections) {
      const found = findNodeInTree(nodeId, node.subsections);
      if (found) return found;
    }
  }

  return null;
};

/**
 * Gets the page title based on the node's properties
 * @param {Object} node - The node object containing text and code properties
 * @returns {string} The formatted page title
 */
export const getPageTitle = (node) => {
  if (!node) return 'Statute Browser';

  const parts = [];

  // Add section/chapter name if available
  if (node.text) {
    parts.push(node.text);
  }

  // Add code info if available
  if (node.code) {
    const codeDisplay = node.code.toUpperCase();
    parts.push(`${codeDisplay} CODE`);
  }

  return parts.join(' - ');
};

/**
 * Gets statistics for a collection node
 * @param {Object} node - The node to get stats for
 * @returns {Array} Array of stats objects for the node
 */
export const getCollectionStats = (node) => {
  if (!node) return [];

  // For root node, get stats for each top-level collection
  if (node.isRoot()) {
    return node.children.map(collection => ({
      id: collection.id?.toUpperCase(),
      type: 'COLLECTION',
      count: collection.children.length,
      label: 'STATUTES'
    }));
  }

  // For collections, show its codes
  if (node.isCollection()) {
    return [{
      id: node.id?.toUpperCase(),
      type: 'COLLECTION',
      count: node.children.length,
      label: 'STATUTES'
    }];
  }

  // For codes, show titles/chapters/articles stats
  if (node.isCode()) {
    const titles = node.getChildrenOfType('title');
    const chapters = node.getChildrenOfType('chapter');
    const articles = node.getChildrenOfType('article');
    const stats = [];

    if (titles.length > 0) {
      stats.push({
        id: node.id?.toUpperCase(),
        type: 'CODE',
        count: titles.length,
        label: 'TITLES'
      });
    }

    if (chapters.length > 0) {
      stats.push({
        id: node.id?.toUpperCase(),
        type: 'CODE',
        count: chapters.length,
        label: 'CHAPTERS'
      });
    }

    if (articles.length > 0) {
      stats.push({
        id: node.id?.toUpperCase(),
        type: 'CODE',
        count: articles.length,
        label: 'ARTICLES'
      });
    }

    return stats;
  }

  // For titles, show chapters/articles stats
  if (node.isTitle()) {
    const stats = [];

    if (node.children.length > 0) {
      stats.push({
        id: node.code.toUpperCase(),
        type: 'CODE',
        count: node.children.length,
        label: `${node.children[0].type.toUpperCase()+'S'}`
      });
    }

    return stats;
  }

  return [];
};
