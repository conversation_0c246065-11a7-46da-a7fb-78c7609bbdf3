export class BookmarkStore {
  static BOOKMARKS_KEY = 'bookmarks';

  static getBookmarks() {
    if (typeof window === 'undefined') return [];
    const stored = localStorage.getItem(BookmarkStore.BOOKMARKS_KEY);
    if (!stored) return [];
    try {
      return JSON.parse(stored);
    } catch (error) {
      console.error('Error parsing bookmarks from localStorage:', error);
      return [];
    }
  }

  static addBookmark(bookmark) {
    const bookmarks = this.getBookmarks();
    bookmarks.push(bookmark);
    this._saveBookmarks(bookmarks);
  }

  static removeBookmark(bookmarkId) {
    const bookmarks = this.getBookmarks();
    const filtered = bookmarks.filter(b => b.id !== bookmarkId);
    this._saveBookmarks(filtered);
  }

  static updateBookmarksOrder(bookmarks) {
    this._saveBookmarks(bookmarks);
  }

  static _saveBookmarks(bookmarks) {
    if (typeof window === 'undefined') return;
    localStorage.setItem(
      BookmarkStore.BOOKMARKS_KEY, 
      JSON.stringify(bookmarks)
    );
  }
}
