export const runtime = "nodejs";

import { mongoClient } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export class BookmarkStore {
  static BOOKMARKS_COLLECTION = 'bookmarks';

  /**
   * Retrieves the MongoDB collection for bookmarks.
   * @returns {Promise<Collection>} A promise that resolves to the bookmarks collection.
   * @private
   */
  static async bookmarksCollection() {
    const mongo = await mongoClient;
    return mongo.db().collection(this.BOOKMARKS_COLLECTION);
  }

  /**
   * Fetches all bookmarks for a specific user.
   * @param {string} userId - The ID of the user whose bookmarks are to be fetched.
   * @returns {Promise<Object[]>} A promise that resolves to an array of bookmark objects.
   * @throws Will throw an error if the fetch operation fails.
   */
  static async getBookmarksForUser(userId) {
    try {
      const bookmarksCollection = await this.bookmarksCollection();
      const bookmarks = await bookmarksCollection
        .find({ userId: new ObjectId(userId) })
        .sort({ order: 1 })
        .toArray();
      return bookmarks;
    }
    catch (error) {
      console.error('Error getting bookmarks:', error);
      throw error;
    }
  }

  /**
   * Adds a new bookmark for a specific user.
   * @param {Object} bookmark - The bookmark object to add.
   * @param {string} userId - The ID of the user to whom the bookmark belongs.
   * @returns {Promise<Object>} A promise that resolves to the added bookmark object.
   * @throws Will throw an error if the add operation fails.
   */
  static async addBookmark(bookmark, userId) {
    try {
      const bookmarksCollection = await this.bookmarksCollection();
      const lastBookmark = await bookmarksCollection
        .findOne({ userId: new ObjectId(userId) }, { sort: { order: -1 } });
      const order = lastBookmark ? lastBookmark.order + 1 : 0;

      const newBookmark = {
        userId: new ObjectId(userId),
        nodeId: bookmark.nodeId,
        text: bookmark.text,
        path: bookmark.path,
        order,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      const result = await bookmarksCollection.insertOne(newBookmark);
      newBookmark._id = result.insertedId;
      return newBookmark;
    }
    catch (error) {
      console.error('Error adding bookmark:', error);
      throw error;
    }
  }

  /**
   * Removes a bookmark for a specific user.
   * @param {string} bookmarkId - The ID of the bookmark to remove.
   * @param {string} userId - The ID of the user to whom the bookmark belongs.
   * @throws Will throw an error if the remove operation fails.
   */
  static async removeBookmark(bookmarkId, userId) {
    try {
      if (!ObjectId.isValid(bookmarkId)) {
        throw new Error('Invalid bookmark ID');
      }
      const bookmarksCollection = await this.bookmarksCollection();
      const result = await bookmarksCollection.deleteOne({ _id: new ObjectId(bookmarkId), userId: new ObjectId(userId) });
      return result;
    }
    catch (error) {
      console.error('Error removing bookmark:', error);
      throw error;
    }
  }

  /**
   * Updates the order of bookmarks for a specific user.
   * @param {Object[]} bookmarks - An array of bookmark objects with updated order.
   * @param {string} userId - The ID of the user to whom the bookmarks belong.
   * @returns {Promise<Object>} A promise that resolves to the result of the update operation.
   * @throws Will throw an error if the update operation fails.
   */
  static async updateBookmarksOrder(bookmarks, userId) {
    try {
      const bookmarksCollection = await this.bookmarksCollection();
      const bulkOps = bookmarks.map((bookmark) => ({
        updateOne: {
          filter: { _id: new ObjectId(bookmark._id), userId: new ObjectId(userId) },
          update: { $set: { order: bookmark.order, updatedAt: bookmark.updatedAt } },
        },
      }));
      const result = await bookmarksCollection.bulkWrite(bulkOps);
      return result;
    }
    catch (error) {
      console.error('Error updating bookmarks order:', error);
      throw error;
    }
  }
}
