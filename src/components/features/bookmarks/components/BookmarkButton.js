'use client';

import React, { useState, useCallback } from 'react';
import { Bookmark as BookmarkIcon, BookmarkX as BookmarkXIcon, BookmarkPlus as BookmarkPlusIcon, Loader2 } from 'lucide-react';
import { useBookmarks } from '@/components/features/bookmarks';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import '../Bookmark.css';

export function BookmarkButton({ node }) {
  const { bookmarks, addBookmark, removeBookmark, isLoading } = useBookmarks();
  const { data: session } = useSession();
  const [showSignInMessage, setShowSignInMessage] = useState(false);

  const isBookmarked = bookmarks?.some(b => b.nodeId === node.nodeId);

  const handleClick = useCallback(async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!session) {
      setShowSignInMessage(true);
      setTimeout(() => setShowSignInMessage(false), 3000);
      return;
    }

    try {
      if (isBookmarked) {
        const bookmark = bookmarks.find(b => b.nodeId === node.nodeId);
        if (bookmark) {
          await removeBookmark(bookmark.id);
        }
      } else {
        await addBookmark(node);
      }
    } catch (error) {
      console.error('Bookmark operation failed:', error);
    }
  }, [session, isBookmarked, node, bookmarks, addBookmark, removeBookmark]);

  const getBookmarkIcon = () => {
    if (isLoading) {
      return <Loader2 size="1em" className="bookmark-icon animate-spin" />;
    }
    if (isBookmarked) {
      return (
        <>
          <BookmarkIcon size="1em" className="bookmark-icon bookmark-icon-default" />
          <BookmarkXIcon size="1em" className="bookmark-icon bookmark-icon-hover" />
        </>
      );
    }
    return (
      <>
        <BookmarkIcon size="1em" className="bookmark-icon bookmark-icon-default" />
        <BookmarkPlusIcon size="1em" className="bookmark-icon bookmark-icon-hover" />
      </>
    );
  };

  return (
    <div className="relative">
      <button
        onClick={handleClick}
        className={`bookmark-button ${isBookmarked ? 'bookmarked' : ''}`}
        title={isBookmarked ? "Remove bookmark" : "Add bookmark"}
        disabled={isLoading}
      >
        {getBookmarkIcon()}
      </button>

      {showSignInMessage && (
        <div className="absolute left-8 top-1/2 -translate-y-1/2 z-50 bg-white p-3 rounded-lg shadow-lg border text-sm whitespace-nowrap">
          Please <Link href="/api/auth/signin" className="text-blue-600 hover:underline">sign in</Link> to save a bookmark.
          <div className="absolute left-0 top-1/2 -translate-x-1 -translate-y-1/2 w-2 h-2 bg-white border-l border-t transform rotate-45"></div>
        </div>
      )}
    </div>
  );
}
