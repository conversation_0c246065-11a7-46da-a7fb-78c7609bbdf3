'use client';

import React, { useCallback, memo, useState } from 'react';
import { Loader2 as Loader2Icon } from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  restrictToVerticalAxis,
  restrictToParentElement
} from '@dnd-kit/modifiers';
import { useBookmarks } from '@/components/features/bookmarks';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { BookmarkCell } from './BookmarkCell';

export const BookmarksList = memo(({ onSelect }) => {
  const { bookmarks, removeBookmark, updateOrder, isLoading } = useBookmarks();
  const { data: session, status } = useSession();
  const [isDragging, setIsDragging] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleRemoveBookmark = useCallback(async (e, bookmark) => {
    e.stopPropagation();
    e.preventDefault();
    await removeBookmark(bookmark.id);
  }, [removeBookmark]);

  const handleBookmarkSelect = useCallback((bookmark) => {
    onSelect?.(bookmark);
  }, [onSelect]);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(async (event) => {
    const { active: draggedItem, over: dropTarget } = event;
    if (!dropTarget || draggedItem.id === dropTarget.id) {
      setIsDragging(false);
      return;
    }

    const draggedIndex = bookmarks.findIndex(item => item.id === draggedItem.id);
    const droppedIndex = bookmarks.findIndex(item => item.id === dropTarget.id);

    const reorderedItems = arrayMove(bookmarks, draggedIndex, droppedIndex)
      .map((bookmark, index) => ({
        ...bookmark,
        order: index,
        updatedAt: new Date()
      }));

    await updateOrder(reorderedItems);
    setIsDragging(false);
  }, [bookmarks, updateOrder]);

  // Early return states
  if (status === 'loading' || isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2Icon className="animate-spin" size={16} />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-gray-600">
        <p className="text-center">
          Please <Link href="/api/auth/signin" className="text-blue-600 hover:underline">Sign In</Link> to view and save your bookmarks.
        </p>
      </div>
    );
  }

  if (!bookmarks?.length) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-gray-600">
        <p className="text-center">No bookmarks yet.</p>
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <div className="bookmark-list-container">
        <div className="bookmark-list h-full overflow-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
          <SortableContext
            items={bookmarks.map(b => b.id)}
            strategy={verticalListSortingStrategy}
          >
            {bookmarks.map((bookmark) => (
              <BookmarkCell
                key={`${bookmark.id}-${bookmark.order}`}
                bookmark={bookmark}
                onSelect={() => handleBookmarkSelect(bookmark)}
                onRemove={(e) => handleRemoveBookmark(e, bookmark)}
                isDisabled={isDragging}
              />
            ))}
          </SortableContext>
        </div>
      </div>
    </DndContext>
  );
});

BookmarksList.displayName = 'BookmarksList';
