import React from 'react';
import { Bookmark as BookmarkIcon, X as XIcon } from 'lucide-react';
import { BookmarksList } from '@/components/features/bookmarks';
import { useTreeState } from '@/components/features/navtree';
import { useLayout } from '@/app/LayoutContext';

export function BookmarksPanel({ isOpen, onClose }) {
  const { selectNode } = useTreeState();
  const { isMobile } = useLayout();

  const handleBookmarkSelect = (bookmarkData) => {
    if (!bookmarkData?.nodeId) return;
    selectNode(bookmarkData.nodeId);
    if (isMobile) {
      onClose();
    }
  };

  return (
    <div className={`fixed top-[60px] bg-white shadow-lg z-[9997] transition-transform duration-300 ease-in-out ${
      isMobile
        ? 'right-0 w-full h-[calc(100vh-60px)] border-l-0'
        : 'right-0 w-[400px] h-[calc(100vh-60px)] border-l border-gray-300'
    } ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}>
        {/* Panel Header */}
        <div className="border-b bg-gray-50 flex items-center justify-between px-4 py-3">
          <div className="w-6" /> {/* Spacer for centering */}
          <div className="flex items-center gap-2">
            <BookmarkIcon size={16} className="text-gray-600" />
            <h3 className="font-medium text-gray-700">Bookmarks</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
            aria-label="Close bookmarks"
          >
            <XIcon size={16} className="text-gray-600" />
          </button>
        </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto h-[calc(100%-48px)]">
        <BookmarksList onSelect={handleBookmarkSelect} />
      </div>
    </div>
  );
}
