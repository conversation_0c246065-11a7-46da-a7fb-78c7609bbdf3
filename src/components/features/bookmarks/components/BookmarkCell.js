'use client';

import React, { memo, useRef } from 'react';
import { BookmarkX as BookmarkXIcon, GripVertical as GripVerticalIcon } from 'lucide-react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

export const BookmarkCell = memo(({ bookmark, onSelect, onRemove, isDisabled }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: bookmark.id,
    disabled: isDisabled
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const clickTimeoutRef = useRef(null);

  const handleMouseDown = () => {
    clickTimeoutRef.current = setTimeout(() => {
      clickTimeoutRef.current = null;
    }, 200);
  };

  const handleMouseUp = (e) => {
    if (e.target.closest('.bookmarkx-icon')) {
      e.stopPropagation();
      e.preventDefault();
      onRemove(e, bookmark);
      return;
    }

    if (clickTimeoutRef.current) {
      clearTimeout(clickTimeoutRef.current);
      onSelect(bookmark);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bookmark-list-item relative flex flex-col p-4 hover:bg-gray-100 rounded cursor-grab active:cursor-grabbing select-none group
        ${isDragging ? 'bg-gray-50 shadow-lg' : ''}`}
      {...attributes}
      {...listeners}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
    >
      <div
        className="absolute left-2 inset-y-0 flex items-center opacity-0 group-hover:opacity-100 transition-opacity"
        title="Drag to reorder"
      >
        <GripVerticalIcon size={14} className="text-gray-400" />
      </div>

      <div className="pl-6">
        <div className="text-xs text-gray-500 mb-1">
          {bookmark.path}
        </div>

        <div className="flex items-center justify-between">
          <div className="bookmark-title flex-1 text-left text-sm text-gray-700">
            {bookmark.text}
          </div>
          <div
            className="bookmarkx-icon p-1 hover:bg-gray-200 rounded-full active-blue"
            title="Remove bookmark"
          >
            <BookmarkXIcon size={16} className="active-blue" />
          </div>
        </div>
      </div>
    </div>
  );
});

BookmarkCell.displayName = 'BookmarkCell';
