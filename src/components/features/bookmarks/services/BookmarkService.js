import { Bookmark } from '@/components/features/bookmarks/models/Bookmark';

export class BookmarkService {
  /**
   * Fetches all bookmarks from the server.
   * @returns {Promise<Bookmark[]>} A promise that resolves to an array of Bookmark objects.
   * @throws Will throw an error if the fetch operation fails.
   */
  async getBookmarks() {
    try {
      const response = await fetch('/api/v1/bookmarks');
      if (!response.ok) throw new Error('Failed to load bookmarks');
      const data = await response.json();
      return this._transformBookmarks(data);
    } catch (error) {
      console.error('Error loading bookmarks:', error);
      return [];
    }
  }

  /**
   * Adds a new bookmark to the server.
   * @param {Bookmark} bookmark - The bookmark object to add.
   * @returns {Promise<Bookmark>} A promise that resolves to the added Bookmark object.
   * @throws Will throw an error if the add operation fails.
   */
  async addBookmark(node) {
    try {
      const bookmark = Bookmark.createForNode(node);
      const response = await fetch('/api/v1/bookmarks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bookmark)
      });
      if (!response.ok) throw new Error('Failed to add bookmark');
      const bookmarkJSON = await response.json();
      return new Bookmark(bookmarkJSON);
    } catch (error) {
      console.error('Error adding bookmark:', error);
      throw error;
    }
  }

  /**
   * Removes a bookmark from the server.
   * @param {string} bookmarkId - The ID of the bookmark to remove.
   * @throws Will throw an error if the remove operation fails.
   */
  async removeBookmark(bookmarkId) {
    try {
      const response = await fetch(`/api/v1/bookmarks/${bookmarkId}`, {
        method: 'DELETE'
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to remove bookmark');
      }
    } catch (error) {
      console.error('Error removing bookmark:', error);
      throw error;
    }
  }

  /**
   * Updates the order of bookmarks on the server.
   * @param {Bookmark[]} bookmarks - An array of Bookmark objects with updated order.
   * @returns {Promise<Bookmark[]>} A promise that resolves to an array of updated Bookmark objects.
   * @throws Will throw an error if the update operation fails.
   */
  async updateBookmarksOrder(bookmarks) {
    try {
      const response = await fetch('/api/v1/bookmarks/reorder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ bookmarks: bookmarks.map(b => b.toJSON()) })
      });
      if (!response.ok) throw new Error('Failed to update bookmark order');
      const data = await response.json();
      return this._transformBookmarks(data);
    } catch (error) {
      console.error('Error updating bookmark order:', error);
      throw error;
    }
  }

  /**
   * Transforms raw data into Bookmark objects.
   * @param {Object[]} data - The raw data array from the server.
   * @returns {Bookmark[]} An array of Bookmark objects.
   * @private
   */
  _transformBookmarks(data) {
    if (!Array.isArray(data)) return [];
    return data.map(bookmarkJSON => new Bookmark(bookmarkJSON));
  }
}
