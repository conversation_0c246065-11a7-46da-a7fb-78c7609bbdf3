export class Bookmark {
  constructor({ _id, id, nodeId, text, path, order, createdAt, updatedAt }) {
    this.id = _id?.toString() || id?.toString(); 
    this.nodeId = nodeId;
    this.text = text;
    this.path = path;
    this.order = order;
    this.createdAt = createdAt ? new Date(createdAt) : new Date();
    this.updatedAt = updatedAt ? new Date(updatedAt) : new Date();
  }

  static fromJSON(json) {
    const bookmark = new Bookmark(json);
    return bookmark;
  }

  toJSON() {
    return {
      _id: this.id,
      nodeId: this.nodeId,
      text: this.text,
      path: this.path,
      order: this.order,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  static createForNode(node) {
    const path = node.getHierarchy()
      .filter(n => n.text)
      .slice(0, -1)  // Remove the current node from the path
      .map(n => n.text.toUpperCase())
      .join(' > ');

    return new Bookmark({
      nodeId: node.nodeId,
      text: node.text,
      path: path
    });
  }
}
