/* Default bookmark button styles */
.bookmark-button {
  position: relative;
  padding: 0.5rem;
  border-radius: 9999px;
  transition: all 0.2s;
  min-width: 36px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bookmark-button .bookmark-icon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  color: #9ca3af; /* Default gray */
  transition: all 0.2s;
}

/* Default icon visible, hover icon hidden */
.bookmark-button .bookmark-icon-default {
  display: block;
}

.bookmark-button .bookmark-icon-hover {
  display: none;
}

/* On hover: hide default icon, show hover icon */
.bookmark-button:hover .bookmark-icon-default {
  display: none;
}

.bookmark-button:hover .bookmark-icon-hover {
  display: block;
}

/* Not bookmarked + hover */
.bookmark-button:not(.bookmarked):hover .bookmark-icon-hover {
  color: #93c5fd; /* Blue on hover */
}

/* Bookmarked state (regular bookmark) */
.bookmark-button.bookmarked .bookmark-icon-default {
  color: var(--accent-color);
  fill: currentColor;
}

/* Bookmarked + hover (BookmarkX) */
.bookmark-button.bookmarked:hover .bookmark-icon-hover {
  color: #93c5fd;
  fill: none !important; /* Ensure X icon is not filled */
}

.selected-index-item .bookmark-button.bookmarked:hover .bookmark-icon-hover {
  color: white;
  fill: none !important; /* Ensure X icon is not filled */
}
.selected-index-item .bookmark-button:hover .bookmark-icon-hover {
  color: white;
  fill: none !important; /* Ensure X icon is not filled */
}

/* TreeNode specific bookmark styles */
.tree-node .bookmark-button {
  padding: 0.375rem;
  margin-right: 0.25rem;
  min-width: 36px;
  min-height: 36px;
  flex-shrink: 0;
}

/* Selected item states */
.selected-index-item .bookmark-button svg {
  color: white;
}

.selected-index-item .bookmark-button:hover svg {
  color: var(--text-color);
}

/* Chevron button in selected items */
.selected-index-item button svg {
  color: white;
}

/* Bookmark list styles */
.bookmark-list-item {
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
}

.bookmarkx-icon:hover {
  color: var(--primary-color);
}

.expand-all-buttons {
  display: flex;
  gap: 0.25rem;
  margin-left: 0.25rem;
}

/* Bookmarked + selected (solid white icon) */
.selected-index-item .bookmark-button.bookmarked .bookmark-icon-default {
  color: white;
  fill: white;
}
