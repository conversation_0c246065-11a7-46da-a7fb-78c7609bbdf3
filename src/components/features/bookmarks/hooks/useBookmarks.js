import { useContext } from 'react';
import { BookmarkContext } from '@/components/features/bookmarks';

export const useBookmarks = () => {
  const context = useContext(BookmarkContext);
  
  if (!context) {
    throw new Error('useBookmarks must be used within a BookmarkProvider');
  }
  
  const { bookmarks, getBookmarks, addBookmark, removeBookmark, updateOrder } = context;

  return {
    bookmarks,
    getBookmarks,
    addBookmark,
    removeBookmark,
    updateOrder
  };
}; 