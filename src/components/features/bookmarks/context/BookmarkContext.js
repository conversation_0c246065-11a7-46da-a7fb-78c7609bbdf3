'use client';

import { createContext } from 'react';
import { useSession } from 'next-auth/react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { BookmarkService } from '@/components/features/bookmarks/services/BookmarkService';
import { Bookmark } from '@/components/features/bookmarks/models/Bookmark';

const BookmarkContext = createContext(null);
const api = new BookmarkService();

const DEFAULT_STALE_TIME = 1000 * 60 * 5;  // 5 minutes
const DEFAULT_CACHE_TIME = 1000 * 60 * 10; // 10 minutes

export function BookmarkProvider({ children }) {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  const staleTime = parseInt(process.env.NEXT_PUBLIC_BOOKMARKS_STALE_TIME ?? DEFAULT_STALE_TIME);
  const gcTime = parseInt(process.env.NEXT_PUBLIC_BOOKMARKS_CACHE_TIME ?? DEFAULT_CACHE_TIME);

  // Main bookmarks query
  const { data: bookmarks = [], isLoading } = useQuery({
    queryKey: ['bookmarks'],
    queryFn: () => api.getBookmarks(),
    enabled: !!session,
    staleTime,
    gcTime
  });

  // Add bookmark mutation
  const addBookmarkMutation = useMutation({
    mutationFn: (node) => api.addBookmark(node),
    onMutate: async (newNode) => {
      await queryClient.cancelQueries(['bookmarks']);
      const previousBookmarks = queryClient.getQueryData(['bookmarks']);
      queryClient.setQueryData(['bookmarks'], old => [...old, newNode]);

      return { previousBookmarks };
    },
    onError: (err, newNode, context) => {
      queryClient.setQueryData(['bookmarks'], context.previousBookmarks);
    },
    onSettled: () => {
      queryClient.invalidateQueries(['bookmarks']);
    }
  });

  // Remove bookmark mutation
  const removeBookmarkMutation = useMutation({
    mutationFn: (bookmarkId) => api.removeBookmark(bookmarkId),
    onMutate: async (bookmarkId) => {
      await queryClient.cancelQueries(['bookmarks']);
      const previousBookmarks = queryClient.getQueryData(['bookmarks']);

      queryClient.setQueryData(['bookmarks'], old =>
        old.filter(b => b.id !== bookmarkId)
      );

      return { previousBookmarks };
    },
    onError: (err, bookmarkId, context) => {
      queryClient.setQueryData(['bookmarks'], context.previousBookmarks);
    },
    onSettled: () => {
      queryClient.invalidateQueries(['bookmarks']);
    }
  });

  // Update order mutation
  const updateOrderMutation = useMutation({
    mutationFn: (newOrder) => {
      // Transform raw objects into Bookmark instances
      const bookmarkInstances = newOrder.map(b => new Bookmark(b));
      return api.updateBookmarksOrder(bookmarkInstances);
    },
    onMutate: async (newOrder) => {
      await queryClient.cancelQueries(['bookmarks']);
      const previousBookmarks = queryClient.getQueryData(['bookmarks']);

      // Transform the newOrder into Bookmark instances for the optimistic update
      const bookmarkInstances = newOrder.map(b => new Bookmark(b));
      queryClient.setQueryData(['bookmarks'], bookmarkInstances);

      return { previousBookmarks };
    },
    onError: (err, newOrder, context) => {
      queryClient.setQueryData(['bookmarks'], context.previousBookmarks);
    }
  });

  const contextValue = {
    bookmarks,
    isLoading,
    addBookmark: addBookmarkMutation.mutate,
    removeBookmark: removeBookmarkMutation.mutate,
    updateOrder: updateOrderMutation.mutate
  };

  return (
    <BookmarkContext.Provider value={contextValue}>
      {children}
    </BookmarkContext.Provider>
  );
}

export { BookmarkContext };
