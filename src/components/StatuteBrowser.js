'use client';

import React, { memo } from 'react';
import './StatuteBrowser.css';

const StatuteBrowserContent = memo(({ children }) => {
  return (
    <div className="h-full flex bg-white">
      {/* Main content area - full width now */}
      <div className="main-container flex-1 flex flex-col overflow-hidden bg-white">
        {children}
      </div>
    </div>
  );
});

StatuteBrowserContent.displayName = 'StatuteBrowserContent';

const StatuteBrowser = ({ children }) => {
  return (
    <StatuteBrowserContent>
      {children}
    </StatuteBrowserContent>
  );
};

export default StatuteBrowser;
