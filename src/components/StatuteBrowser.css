/* Base styles and typography */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #34495e;
  --accent-color: #3498db;
  --background-color: #ffffff;
  --text-color: #1f2937;
  --gray-text-color: #9ca3af;
  --border-color: #d8d4d4;
  --error-color: #ff0000;
  --section-spacing: 1rem;
  --subsection-spacing: 1rem;
}

.active-blue {
  color: var(--accent-color);
}
.bg-active-blue {
  background-color: var(--accent-color);
}

.left-panel-container {
  position: relative;
  line-height: 1.6;
  transition: transform 0.3s cubic-bezier(0.4,0,0.2,1);
  will-change: transform;
  z-index: 50;
}

@media (max-width: 767px) {
  .left-panel-container {
    position: fixed;
    top: 60px;
    left: 0;
    width: 100vw;
    height: calc(100vh - 60px);
    max-width: 100vw;
    min-width: 0;
    box-shadow: 0 4px 24px rgba(0,0,0,0.10);
    background: #fff;
    z-index: 999;
    pointer-events: auto;
  }
}

.left-panel-container[style*='pointer-events: none'] {
  pointer-events: none !important;
}

.left-panel-content {
  overflow-y: auto;
  height: 100%;
}

.statute-viewer {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
}

.statute-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.statute-container {
  @apply max-w-4xl mx-auto p-8;
}

.statute-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 2rem 2rem 2rem;
  position: relative;
  line-height: 1.65;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.statute-content > * {
  width: 100%;
  max-width: 800px;
}
.statute-content > div:last-child .section-text:last-child {
  margin-bottom: 0;
}

.statute-section {
  padding: 0 0.5rem 0.75rem 0.5rem;
}
.statute-section:last-child {
  padding-bottom: 0;
}

.section-container {
  margin: 2rem 0;
  padding-bottom: 0;
}

.section-header {
  @apply ml-4;
}

.section {
  margin: var(--section-spacing) 0 2rem 0;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border-radius: 5px;
  transition: background-color 0.2s ease;
  max-width: 800px;
}

.section:hover {
  background-color: #f0f0f0;
}

.section-title {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0.5rem 0;
  margin-bottom: 0.5rem;
  position: relative;
}

.section-title h4 {
  font-size: 1.2rem;
  margin: 0.5rem 0;
  flex: 1;
}

.selected-section {
  border-left: 4px solid var(--accent-color);
  margin-left: -4px; /* Negative margin to offset the border width */
}

.highlighted-section {
  background-color: rgba(52, 152, 219, 0.2);  /* Light blue highlight */
  transition: background-color 0.5s ease;
}

.chevron-icon {
  margin-left: auto;
}

.section-content {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
  padding-left: 0;
  color: var(--text-color);
}

.amendment-history {
  padding: 1rem 0.5rem;
  border-top: 1px dashed var(--border-color);
  font-size: 0.9rem;
}

.amendment-entry {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.amendment-entry:last-child {
  margin-bottom: 0;
}

/* ================================================ */
/* Fixed header and tab styling */
/* ================================================ */

.fixed-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
}

.content-area {
  height: calc(100vh - 40px);
  overflow-y: auto;
}

.tabs {
  height: 4.0rem;
}

.active-tab {
  color: var(--accent-color);
  border-bottom: 4px solid var(--accent-color);
}

/* ================================================ */
/* Scrollbar styling */
/* ================================================ */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219); /* gray-300 */
  border-radius: 9999px;
  visibility: hidden;
}

.scrollbar-thin:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}

/* For Firefox */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(209 213 219) transparent;
}

/* Remove any overflow settings from parent containers */
.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* ================================================ */
/* Content styles  */
/* ================================================ */

/* Update specific sizes while maintaining the color */
.collection,
.code,
.title,
.subtitle,
.chapter,
.article,
.subarticle,
.subchapter,
.section-title {
  font-weight: bold;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.collection {
  @apply mt-5;
  font-size: 1rem;
  @apply text-gray-500;
}

.code {
  font-size: 2.4rem;
}

.title,
.article {
  font-size: 2rem;
}

.subtitle,
.subarticle {
  font-size: 1.8rem;
}

.chapter {
  font-size: 1.6rem;
}

.subchapter {
  font-size: 1.4rem;
}

.section-title {
  font-size: 1.2rem;
  cursor: pointer;
  justify-content: space-between;
  padding: 0.5rem 0;
  margin-bottom: 0;
  border-bottom: 0 !important;
}

.section-text {
  padding-left: 0;
  margin-bottom: 1rem;
  color: var(--text-color);
  line-height: 1.7;
  font-size: 1rem;
}

/* Improve paragraph spacing and list formatting */
.statute-content p {
  margin-bottom: 1.25rem;
  line-height: 1.7;
  max-width: 75ch;
}

/* Improve article title styling */
.statute-content h1,
.statute-content h2,
.statute-content h3 {
  margin-bottom: 1.5rem;
  margin-top: 0.5rem;
  font-weight: 700;
  color: var(--text-color);
  line-height: 1.3;
}

/* Section and subsection indents */

p[style*="text-indent:7ex"],
.subsection-level-1 {
  padding-left: 0rem;
}

p[style*="text-indent:13ex"],
.subsection-level-2 {
  padding-left: 1rem;
}

p[style*="text-indent:19ex"],
.subsection-level-3 {
  padding-left: 2rem;
}

p[style*="text-indent:25ex"],
.subsection-level-4 {
  padding-left: 3rem;
}

p[style*="text-indent:31ex"],
.subsection-level-5 {
  padding-left: 4rem;
}

.loading-state {
  @apply p-8 text-center text-gray-500;
}

/* Repealed section styling */
.repealed {
  text-decoration: line-through !important;
  color: #b6b6b6 !important;
}

.highlight-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.highlight-wrapper .highlight-container {
  pointer-events: auto;
}

/* Update ResizablePanel height */
.h-screen {
  height: calc(100vh - 60px);
  margin-top: 60px;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  .statute-container {
    padding: 1rem;
    margin: 0;
    box-shadow: none;
    border-radius: 0;
  }

  .statute-content {
    padding: 0;
    line-height: 1.6;
    align-items: stretch;
  }
  
  .statute-content > * {
    max-width: none;
  }

  .section {
    padding: 0.5rem 0.75rem;
    margin: 1rem 0;
    border-radius: 0;
  }
  
  .section-text,
  .statute-content p {
    font-size: 1.1rem;
    max-width: none;
  }
  
  .section {
    max-width: none;
  }
  
  div.section:first-child {
    margin-top: 0;
  }

  div.section:last-child {
    margin-bottom: 0;
  }

  .section-title h4 {
    font-size: 1.1rem;
  }

  .code {
    font-size: 2rem;
  }

  .title,
  .article {
    font-size: 1.8rem;
  }

  .subtitle,
  .subarticle {
    font-size: 1.6rem;
  }

  .chapter {
    font-size: 1.4rem;
  }

  .subchapter {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .statute-container {
    padding: 0.75rem;
  }

  .statute-content {
    padding: 0 0.75rem 2rem 0.25rem;
    line-height: 1.6;
    align-items: stretch;
  }
  
  .statute-content > * {
    max-width: none;
  }

  .section {
    padding: 0.4rem 0.6rem;
    margin: 0.75rem 0;
    border-radius: 0;
  }
  
  .section-text,
  .statute-content p {
    font-size: 1.125rem;
    max-width: none;
  }
  
  .section {
    max-width: none;
  }

  div.section:first-child {
    margin-top: 0;
  }

  div.section:last-child {
    margin-bottom: 0;
  }

  .section-title h4 {
    font-size: 1rem;
  }

  .code {
    font-size: 1.8rem;
  }

  .title,
  .article {
    font-size: 1.6rem;
  }

  .subtitle,
  .subarticle {
    font-size: 1.4rem;
  }

  .chapter {
    font-size: 1.2rem;
  }

  .subchapter {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 1rem;
  }

  /* Reduce nested indentation on small screens */
  .subsection-level-2 {
    padding-left: 0.5rem;
  }

  .subsection-level-3 {
    padding-left: 1rem;
  }

  .subsection-level-4 {
    padding-left: 1.5rem;
  }

  .subsection-level-5 {
    padding-left: 2rem;
  }
}
