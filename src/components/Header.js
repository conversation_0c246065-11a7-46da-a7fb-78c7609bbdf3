import { Scale, Loader2, Bookmark, LogOut, Search } from 'lucide-react';
import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { useState } from 'react';
import { useSearch } from '@/components/features/search';

export function Header({ showBookmarksList, setShowBookmarksList, showSearchPanel, setShowSearchPanel }) {
  const { data: session, status } = useSession();
  const [showDropdown, setShowDropdown] = useState(false);
  const { searchQuery } = useSearch();

  return (
    <header className="h-[60px] border-b bg-white fixed top-0 left-0 right-0 z-50 shadow-sm">
      {/* Main Header Row */}
      <div className="h-[60px] flex items-center px-4 gap-2 md:gap-4">
        {/* Left Section - Search */}
        <div className="flex justify-start">
          <button
            onClick={() => setShowSearchPanel(!showSearchPanel)}
            className="w-10 h-10 pt-2.5 flex items-center justify-center hover:bg-gray-100 rounded-md transition-colors relative"
            title="Search"
            aria-label="Open search"
          >
            <Search size={20} className={showSearchPanel ? "active-blue" : "text-gray-600"} />
            {/* Search indicator dot */}
            {searchQuery && searchQuery.length >= 3 && (
              <div className="absolute top-1 right-1 w-3 h-3 bg-yellow-300 rounded-full border-2 border-white"></div>
            )}
          </button>
        </div>

        {/* Center Section - Logo */}
        <div className="flex-1 flex justify-center px-2 md:px-4">
          <Link href="/" className="flex items-center gap-1 md:gap-2 text-xl font-semibold active-blue">
            <Scale size={28} className="active-blue md:w-10 md:h-10" />
            <span className="text-lg md:text-2xl font-bold ml-1 md:ml-2 truncate">LegalCodes</span>
          </Link>
        </div>

        {/* Right Section - User Tools */}
        <div className="flex justify-end relative">
          <div className="flex items-center bg-gray-50 rounded-md h-10 overflow-hidden">
            {/* Bookmarks Button */}
            {session && (
              <>
                <button
                  onClick={() => setShowBookmarksList(!showBookmarksList)}
                  className="flex-1 h-full flex items-center justify-center hover:bg-gray-200 transition-colors px-2"
                  title="My bookmarks"
                  aria-label="View mybookmarks"
                >
                  <Bookmark size={18} className={showBookmarksList ? "active-blue" : "text-gray-600"} />
                </button>

                {/* Separator */}
                <div className="w-px h-6 bg-gray-300"></div>
              </>
            )}

            {/* Auth Section */}
            {status === "loading" ? (
              <div className="flex-1 h-full flex items-center justify-center px-2">
                <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
              </div>
            ) : session ? (
              <div className="flex-1 h-full">
                <button
                  onClick={() => setShowDropdown(!showDropdown)}
                  className="w-full h-full flex items-center justify-center hover:bg-gray-200 transition-colors px-2"
                  title="Your account"
                  aria-label="View your account"
                >
                  <div className="w-7 h-7 bg-active-blue rounded-full flex items-center justify-center text-white text-sm">
                    {(session.user?.name?.[0] || session.user?.email?.[0] || '?').toUpperCase()}
                  </div>
                </button>
              </div>
            ) : (
              <div className="flex-1 h-full flex items-center justify-center gap-1 px-1">
                <Link
                  href="/api/auth/signin"
                  className="px-1 md:px-2 py-1 hover:bg-gray-200 rounded text-gray-700 text-xs"
                >
                  <span className="hidden sm:inline">Sign In</span>
                  <span className="sm:hidden">In</span>
                </Link>
                <Link
                  href="/api/auth/signin"
                  className="px-1 md:px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs"
                >
                  <span className="hidden sm:inline">Sign Up</span>
                  <span className="sm:hidden">Up</span>
                </Link>
              </div>
            )}
          </div>

          {/* Dropdown positioned outside container */}
          {showDropdown && session && (
            <div
              className="absolute right-0 top-full mt-2 w-56 bg-white rounded-md shadow-lg border"
              style={{ zIndex: 10001 }}
            >
              <div className="px-4 py-3 border-b bg-gray-50">
                <div className="text-sm font-medium text-gray-700 pr-4">
                  {session.user?.name || session.user?.email}
                </div>
              </div>
              <button
                onClick={() => signOut()}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 text-gray-700 flex items-center gap-2 group transition-colors"
              >
                <LogOut size={16} className="text-gray-500 group-hover:text-red-500 transition-colors" />
                <span>Sign Out</span>
              </button>
            </div>
          )}
        </div>
      </div>

    </header>
  );
}
