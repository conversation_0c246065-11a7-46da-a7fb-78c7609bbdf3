import { LoadingSpinnerIcon } from './LoadingSpinnerIcon';

export function LoadingButton({
  loading,
  icon: Icon,
  text,
  iconPosition = 'left',
  onClick,
  title,
  disabled,
  className = "hover:bg-gray-200 rounded text-gray-500",
  size = 14
}) {
  const iconElement = <Icon size={size} />;
  const loadingElement = loading && <LoadingSpinnerIcon loading={true} size={size} />;
  const textElement = text && <span className="mx-1">{text}</span>;

  return (
    <button
      onClick={onClick}
      className={`${className} flex items-center gap-1`}
      title={title}
      disabled={disabled || loading}
    >
      {iconPosition === 'left' && (loadingElement || iconElement)}
      {textElement}
      {iconPosition === 'right' && (loadingElement || iconElement)}
    </button>
  );
}
