'use client';

import React, { useState, useEffect, useCallback } from 'react';

const ResizablePanel = ({
  children,
  initialWidth = 400,
  minWidth = 330,
  maxWidth = 800,
  resizableClassPattern = '.resizable',
  isPanelOpen = true
}) => {
  const [width, setWidth] = useState(initialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const [shouldAnimate, setShouldAnimate] = useState(false);

  // Load saved width on mount
  useEffect(() => {
    const savedWidth = localStorage.getItem('leftPanelWidth');
    if (savedWidth) {
      setWidth(parseInt(savedWidth));
    }
  }, []);

  // When toggling open/close, enable animation
  useEffect(() => {
    setShouldAnimate(true);
  }, [isPanelOpen]);

  // When resizing, disable animation
  useEffect(() => {
    if (isResizing) {
      setShouldAnimate(false);
    }
  }, [isResizing]);

  const updateNestedElements = useCallback((newWidth) => {
    document.querySelectorAll(resizableClassPattern).forEach(element => {
      element.style.width = `${newWidth}px`;
    });
  }, [resizableClassPattern]);

  const saveWidth = useCallback((newWidth) => {
    localStorage.setItem('leftPanelWidth', newWidth.toString());
    setWidth(newWidth);
    updateNestedElements(newWidth);
  }, [updateNestedElements]);

  useEffect(() => {
    const handleMouseMove = (e) => {
      if (isResizing) {
        e.preventDefault();
        const newWidth = Math.max(minWidth, Math.min(e.clientX, maxWidth));
        saveWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = 'default';
    };

    if (isResizing) {
      document.body.style.cursor = 'col-resize';
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.body.style.cursor = 'default';
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, minWidth, maxWidth, saveWidth]);

  useEffect(() => {
    updateNestedElements(width);
  }, [width, updateNestedElements]);

  return (
    <div className="flex h-screen fixed inset-0 bg-white">
      <div
        style={{
          width: `${width}px`,
          minWidth: 0,
          maxWidth: `${maxWidth}px`,
          transform: isPanelOpen ? 'translateX(0)' : `translateX(-${width}px)`,
          transition: shouldAnimate ? 'transform 0.3s cubic-bezier(0.4,0,0.2,1)' : 'none',
          display: 'flex',
        }}
        className="resizable flex-shrink-0 h-full"
      >
        {/* Always render children[0] */}
        <div style={{ flex: '1 1 auto', minWidth: 0, height: '100%' }}>
          {children[0]}
        </div>
        {/* Only show handle when open */}
        {isPanelOpen && (
          <div
            className="z-[9999] w-1 bg-gray-200 hover:bg-blue-300 cursor-col-resize transition-colors h-full"
            onMouseDown={(e) => {
              e.preventDefault();
              setIsResizing(true);
            }}
          />
        )}
      </div>
      <div
        id="main-panel"
        className="flex-1 overflow-auto"
        style={{
          marginLeft: isPanelOpen ? 0 : `-${width}px`,
          transition: shouldAnimate ? 'margin-left 0.3s cubic-bezier(0.4,0,0.2,1)' : 'none',
        }}
      >
        {children[1]}
      </div>
    </div>
  );
};

export default ResizablePanel;
