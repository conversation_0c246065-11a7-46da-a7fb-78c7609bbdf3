import React from 'react';
import '@/components/StatuteBrowser.css';

const TabButton = ({ isActive, onClick, icon: Icon, children }) => (
  <button
    className={`flex items-center gap-2 flex-1 justify-center py-2 text-lg border-b-2 transition-colors
      ${isActive 
        ? 'active-tab font-semibold' 
        : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300'}`}
    onClick={onClick}
  >
    <Icon size={18} />
    <span>{children}</span>
  </button>
);


export default TabButton;