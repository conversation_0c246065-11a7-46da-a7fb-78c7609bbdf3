'use client';

import { useState, useEffect, memo } from 'react';
import { LoadingSpinnerIcon } from './LoadingSpinnerIcon';

const LoadingSpinner = memo(({
  message = 'Loading content...',
  loading = true,
  size = 24,
  className = 'text-blue-500',
  showDelay = 200,
  minDisplayTime = 300,
  spinnerPosition = 'above' // 'above' | 'below' | 'left' | 'right'
}) => {
  const [shouldShow, setShouldShow] = useState(false);
  const [showStartTime, setShowStartTime] = useState(0);

  useEffect(() => {
    let showTimer, hideTimer;

    if (loading && !shouldShow) {
      showTimer = setTimeout(() => {
        setShouldShow(true);
        setShowStartTime(Date.now());
      }, showDelay);
    } else if (!loading && shouldShow) {
      const currentDisplayTime = Date.now() - showStartTime;
      const remainingTime = Math.max(0, minDisplayTime - currentDisplayTime);

      hideTimer = setTimeout(() => {
        setShouldShow(false);
      }, remainingTime);
    }

    return () => {
      clearTimeout(showTimer);
      clearTimeout(hideTimer);
    };
  }, [loading, showDelay, minDisplayTime, shouldShow, showStartTime]);

  if (!shouldShow) return null;

  const isVertical = spinnerPosition === 'above' || spinnerPosition === 'below';
  const flexDirection = isVertical
    ? 'flex-col'
    : spinnerPosition === 'left'
      ? 'flex-row'
      : 'flex-row-reverse';

  const messageOrder = spinnerPosition === 'above' || spinnerPosition === 'left' ? 'order-2' : 'order-1';
  const spinnerOrder = spinnerPosition === 'above' || spinnerPosition === 'left' ? 'order-1' : 'order-2';
  const gapClass = isVertical ? 'gap-3' : 'gap-2';

  return (
    <div className="flex items-center justify-center p-4">
      <div className={`flex items-center ${flexDirection} ${gapClass}`}>
        <div className={spinnerOrder}>
          <LoadingSpinnerIcon
            size={size}
            className={className}
          />
        </div>
        {loading && message && (
          <div className={`${messageOrder} text-gray-600`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
});

LoadingSpinner.displayName = 'LoadingSpinner';

export { LoadingSpinner };
