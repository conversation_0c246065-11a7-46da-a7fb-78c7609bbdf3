/**
 * ModalPositioning utility
 * Provides consistent positioning logic for modals, popovers, and pickers
 * that need to be positioned relative to the .statute-content panel
 */

export class ModalPositioning {

  /**
   * Calculate horizontal position for fixed positioning with viewport bounds
   * @private
   */
  static #getFixedLeftPosition(centerX, width, margin, panelRect, hasCaret) {
    let modalLeft = hasCaret ? centerX - (width / 2) - (margin * 2) : centerX - (width / 2);
    let caretLeft = hasCaret ? width / 2 : undefined;

    // Constrain to panel bounds within viewport
    const panelLeft = Math.max(0, panelRect.left);
    const panelRight = Math.min(window.innerWidth, panelRect.right);

    // Handle horizontal clamping with optional caret adjustment
    const isClampedRight = (modalLeft + width > panelRight - margin);
    const isClampedLeft = (modalLeft < panelLeft + margin);

    if (hasCaret) {
      if (isClampedRight) {
        const newModalLeft = panelRight - margin - width;
        const caretOffset = newModalLeft - modalLeft;
        modalLeft = newModalLeft;
        caretLeft -= caretOffset;
      }

      if (isClampedLeft) {
        const newModalLeft = panelLeft + margin;
        const caretOffset = modalLeft - newModalLeft;
        modalLeft = newModalLeft;
        caretLeft += caretOffset;
      }
    } else {
      // Standard clamping for regular modals
      if (isClampedRight) {
        modalLeft = panelRight - margin - width;
      }
      if (isClampedLeft) {
        modalLeft = panelLeft + margin;
      }
    }

    return { modalLeft, caretLeft };
  }

  /**
   * Calculate vertical position for fixed positioning with viewport bounds
   * @private
   */
  static #getFixedTopPosition(targetTop, targetHeight, height, margin, _panelRect, hasCaret, preferAbove, caretOffset) {
    // Detect fixed header height
    let headerHeight = 0;
    const headerElement = document.querySelector('header');
    if (headerElement) {
      const headerRect = headerElement.getBoundingClientRect();
      // Only count as header if it's fixed at the top
      if (headerRect.top === 0) {
        headerHeight = headerRect.height;
      }
    }

    // Detect breadcrumb height - only count if fixed at expected position below header
    let breadcrumbHeight = 0;
    const breadcrumbElement = document.querySelector('#breadcrumb-bar');
    if (breadcrumbElement) {
      const breadcrumbRect = breadcrumbElement.getBoundingClientRect();
      // Only count breadcrumb if it's positioned right after the header (fixed positioning)
      if (breadcrumbRect.top === headerHeight) {
        breadcrumbHeight = breadcrumbRect.height;
      }
    }

    // For viewport positioning, we need to respect both header and breadcrumb
    const fixedAreaHeight = headerHeight + breadcrumbHeight; // Total fixed area above scrollable content
    const viewportTop = fixedAreaHeight; // Minimum position is below fixed elements
    const viewportBottom = window.innerHeight;
    
    // For fixed positioning, we only care about the actual viewport bounds, not panel scroll position
    const effectiveTop = viewportTop;
    const effectiveBottom = viewportBottom;

    let modalTop;
    let shouldFlip = false;

    if (hasCaret) {
      // HighlightPicker positioning logic - prefers above by default
      modalTop = targetTop - height - margin;

      if (modalTop < effectiveTop + margin) {
        // Picker would be above visible viewport area, flip below selection
        modalTop = targetTop + targetHeight + margin;
        shouldFlip = true;
      }
    } else {
      // Regular modal positioning logic - for ReferenceEditModal
      modalTop = preferAbove ? targetTop - height - margin : targetTop + targetHeight + margin;

      if (preferAbove && modalTop < effectiveTop) {
        // Modal would be above visible viewport area, flip below target
        modalTop = targetTop + targetHeight + margin + caretOffset;
        shouldFlip = true;
      } else if (!preferAbove && modalTop + height > effectiveBottom - margin) {
        // Modal would be below visible viewport area, flip above target
        modalTop = targetTop - height - margin - caretOffset;
        shouldFlip = true;
      }
    }

    // Final clamp to ensure modal stays within visible viewport bounds
    // Prioritize staying within viewport over header avoidance if space is very constrained
    const minTop = Math.max(viewportTop + margin, effectiveTop + margin);
    const maxTop = effectiveBottom - margin - height;
    
    if (maxTop < minTop) {
      // Very constrained space - prioritize viewport bounds
      modalTop = Math.max(0, Math.min(modalTop, window.innerHeight - height - margin));
    } else {
      // Normal clamping within available space
      modalTop = Math.max(minTop, Math.min(modalTop, maxTop));
    }

    return { modalTop, shouldFlip };
  }

  /**
   * Calculate horizontal position with caret handling
   * @private
   */
  static #getLeftPosition(centerX, width, margin, panel, hasCaret) {
    let modalLeft = hasCaret ? centerX - (width / 2) - (margin * 2) : centerX - (width / 2);
    let caretLeft = hasCaret ? width / 2 : undefined;

    // Handle horizontal clamping with optional caret adjustment
    const isClampedRight = (modalLeft + width > panel.offsetWidth - margin);
    const isClampedLeft = (modalLeft < margin);

    if (hasCaret) {
      if (isClampedRight) {
        const newModalLeft = panel.offsetWidth - margin - width;
        const caretOffset = newModalLeft - modalLeft;
        modalLeft = newModalLeft;
        caretLeft -= caretOffset;
      }

      if (isClampedLeft) {
        const newModalLeft = margin;
        const caretOffset = modalLeft - newModalLeft;
        modalLeft = newModalLeft;
        caretLeft += caretOffset;
      }
    } else {
      // Standard clamping for regular modals
      if (isClampedRight) {
        modalLeft = panel.offsetWidth - margin - width;
      }
      if (isClampedLeft) {
        modalLeft = margin;
      }
    }

    return { modalLeft, caretLeft };
  }

  /**
   * Calculate vertical position with flip logic
   * @private
   */
  static #getTopPosition(top, height, margin, panel, panelRelativeRect, hasCaret, preferAbove, caretOffset) {
    const viewportTop = panel.scrollTop + margin;
    const viewportBottom = panel.scrollTop + panel.clientHeight - margin;

    let modalTop;
    let shouldFlip = false;

    if (hasCaret) {
      // HighlightPicker positioning logic - prefers above by default
      modalTop = top - height;

      if (modalTop < viewportTop) {
        // Picker would be above visible area, flip below selection
        modalTop = top + panelRelativeRect.height + margin;
        shouldFlip = true;

        // If flipping below goes outside visible viewport, clamp within bounds
        if (modalTop + height > viewportBottom) {
          modalTop = Math.max(viewportTop, viewportBottom - height);
        }
      }
    } else {
      // Regular modal positioning logic
      modalTop = preferAbove ? top - height - margin : top + panelRelativeRect.height + margin;

      if (preferAbove && modalTop < viewportTop) {
        // Modal would be above visible area, flip below target
        modalTop = top + panelRelativeRect.height + margin + caretOffset;
        shouldFlip = true;

        // If flipping below goes outside visible viewport, clamp within bounds
        if (modalTop + height > viewportBottom) {
          modalTop = Math.max(viewportTop, viewportBottom - height);
        }
      } else if (!preferAbove && modalTop + height > viewportBottom) {
        // Modal would be below visible area, flip above target
        modalTop = top - height - margin - caretOffset;
        shouldFlip = true;

        // If flipping above goes outside visible viewport, clamp within bounds
        if (modalTop < viewportTop) {
          modalTop = Math.max(viewportTop, Math.min(modalTop, viewportBottom - height));
        }
      }
    }

    // Final clamp to ensure modal stays within visible viewport
    modalTop = Math.max(viewportTop, Math.min(modalTop, viewportBottom - height));

    return { modalTop, shouldFlip };
  }

  /**
   * Calculate position for a modal/popover relative to a target rect (viewport coordinates)
   * @param {DOMRect} targetRect - The bounding rect of the target element (viewport coordinates from getBoundingClientRect)
   * @param {Object} options - Configuration options
   * @param {number} options.width - Width of the modal/popover
   * @param {number} options.height - Height of the modal/popover
   * @param {number} [options.margin=8] - Margin from panel edges
   * @param {string} [options.panel='.statute-content'] - CSS selector for the container panel
   * @param {boolean} [options.preferAbove=true] - Whether to prefer positioning above the target
   * @param {boolean} [options.hasCaret=false] - Whether to include caret positioning
   * @param {number} [options.caretOffset=0] - Additional offset for caret height (used by HighlightPicker)
   * @param {boolean} [options.useFixedPosition=false] - Whether to use fixed positioning (viewport coordinates) instead of absolute (panel-relative)
   * @returns {Object} Position object with { left, top, shouldFlip } or { left, top, caretLeft, shouldFlip } if hasCaret=true
   */
  static calculatePosition(targetRect, options) {
    const {
      width,
      height,
      margin = 8,
      panel: panelSelector = '.statute-content',
      preferAbove = true,
      hasCaret = false,
      caretOffset = 0,
      useFixedPosition = false
    } = options;

    const panel = document.querySelector(panelSelector);
    if (!panel || !targetRect) {
      const fallback = { left: 0, top: 0, shouldFlip: false };
      if (hasCaret) fallback.caretLeft = 0;
      return fallback;
    }

    if (useFixedPosition) {
      // For fixed positioning, use viewport coordinates directly
      const centerX = targetRect.left + (targetRect.width / 2);
      const panelRect = panel.getBoundingClientRect();
      
      // Calculate horizontal position with viewport bounds
      const { modalLeft, caretLeft } = this.#getFixedLeftPosition(centerX, width, margin, panelRect, hasCaret);
      
      // Calculate vertical position with viewport bounds
      const { modalTop, shouldFlip } = this.#getFixedTopPosition(targetRect.top, targetRect.height, height, margin, panelRect, hasCaret, preferAbove, caretOffset);
      
      
      const result = {
        left: modalLeft,
        top: modalTop,
        shouldFlip
      };
      
      if (hasCaret) {
        result.caretLeft = caretLeft;
      }
      
      return result;
    }

    // Convert viewport coordinates to panel-relative coordinates
    const panelRect = panel.getBoundingClientRect();
    const panelRelativeRect = {
      left: targetRect.left - panelRect.left,
      top: targetRect.top - panelRect.top,
      right: targetRect.right - panelRect.left,
      bottom: targetRect.bottom - panelRect.top,
      width: targetRect.width,
      height: targetRect.height
    };

    // Add scroll offset to handle scrolled content
    const scrollTop = panel.scrollTop;
    const scrollLeft = panel.scrollLeft;
    const left = panelRelativeRect.left + scrollLeft;
    const top = panelRelativeRect.top + scrollTop;
    const centerX = left + (panelRelativeRect.width / 2);

    // Calculate horizontal position with caret handling
    const { modalLeft, caretLeft } = this.#getLeftPosition(centerX, width, margin, panel, hasCaret);

    // Calculate vertical position with flip logic
    const { modalTop, shouldFlip } = this.#getTopPosition(top, height, margin, panel, panelRelativeRect, hasCaret, preferAbove, caretOffset);

    const result = {
      left: modalLeft,
      top: modalTop,
      shouldFlip
    };

    // Include caret position if requested
    if (hasCaret) {
      result.caretLeft = caretLeft;
    }

    return result;
  }


  /**
   * Get fallback position when panel or target rect is not available
   * @param {string} [panelSelector='.statute-content'] - CSS selector for the container panel
   * @param {number} [width=400] - Width of the modal for centering
   * @param {number} [height=200] - Height of the modal for centering
   * @returns {Object} Fallback position object
   */
  static getFallbackPosition(panelSelector = '.statute-content', width = 400, height = 200) {
    const panel = document.querySelector(panelSelector);

    if (panel) {
      return {
        left: panel.offsetWidth / 2 - width / 2,
        top: panel.offsetHeight / 2 - height / 2,
        shouldFlip: false
      };
    }

    // Ultimate fallback
    return {
      left: 200,
      top: 200,
      shouldFlip: false
    };
  }
}
