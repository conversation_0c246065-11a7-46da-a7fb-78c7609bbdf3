export const runtime = "nodejs";

import { MongoClient } from 'mongodb'

if (!process.env.MONGODB_URI) {
  throw new Error('Invalid/Missing environment variable: "MONGODB_URI"')
}

const uri = process.env.MONGODB_URI
const options = {
  appName: "LegalCodes",
  maxPoolSize: 10,
  minPoolSize: 2,
  connectTimeoutMS: 30000, // 30 seconds for initial connection
  socketTimeoutMS: 45000,  // 45 seconds for socket operations
  serverSelectionTimeoutMS: 30000, // 30 seconds to select server
  retryWrites: true,
  retryReads: true,
  // Stability options for production
  heartbeatFrequencyMS: 10000,
  maxIdleTimeMS: 30000,
  // SSL/TLS options for production stability
  tls: true,
  tlsAllowInvalidHostnames: false,
}


// Enhanced error handling for SSL and connection issues
async function createConnectionWithRetry() {
  const maxRetries = 3;
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`MongoDB connection attempt ${attempt}/${maxRetries}`);
      const client = new MongoClient(uri, options);
      await client.connect();
      console.log('MongoDB connection successful');
      return client;
    } catch (error) {
      lastError = error;
      console.error(`MongoDB connection attempt ${attempt} failed:`, error.message);
      
      // Special handling for SSL errors - log but don't modify options
      if (error.message.includes('SSL') || error.message.includes('TLS')) {
        console.error('SSL/TLS error detected:', error.message);
      }
      
      if (attempt < maxRetries) {
        const delay = attempt * 2000; // Progressive delay: 2s, 4s, 6s
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('All MongoDB connection attempts failed');
  throw lastError;
}

// Ensure single connection reuse in both dev and production
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable for HMR
  if (!global._mongoClientPromise) {
    global._mongoClientPromise = createConnectionWithRetry();
  }
  clientPromise = global._mongoClientPromise;
} else {
  // In production, create singleton connection
  if (!clientPromise) {
    clientPromise = createConnectionWithRetry();
  }
}

export const mongoClient = clientPromise;
