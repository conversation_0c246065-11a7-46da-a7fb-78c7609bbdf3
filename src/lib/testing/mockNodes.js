export class MockNode {
    constructor(nodeId, type, code = null, collection = null) {
        this.id = nodeId.split('/').pop() || '/';
        this.code = code || 'mockCode';
        this.collection = collection || 'mockCollection';
        this.parent = undefined;
        this.children = [];
        this.nodeId = nodeId;
        this.type = type;
        this._hierarchy = [];
    }

    addChild(node) {
        this.children.push(node);
        node.parent = this;
        node._hierarchy = [...this._hierarchy, node];
    }

    getRootNode() {
        return this._hierarchy[0] || this;
    }

    getHierarchy() {
        return this._hierarchy;
    }

    findNode(nodeId) {
        if (!nodeId) return null;
        if (this.nodeId === nodeId) return this;

        for (const child of this.children) {
            const found = child.findNode(nodeId);
            if (found) return found;
        }
        return null;
    }

    getChildrenOfType(type) {
        return this.children.filter(child => child.type === type);
    }

    findNodeByStartEndPath(startPath, endPath) {
        if (!startPath || !endPath) return null;
        if (this.nodeId.startsWith(startPath) && this.nodeId.endsWith(endPath)) return this;
        for (const child of this.children) {
            const found = child.findNodeByStartEndPath(startPath, endPath);
            if (found) return found;
        }
        return null;
    }

    isRoot() { return this.type === 'root'; }
    isCollection() { return this.type === 'collection'; }
    isCode() { return this.type === 'code'; }
    isTitle() { return this.type === 'title'; }
    isSubtitle() { return this.type === 'subtitle'; }
    isChapter() { return this.type === 'chapter'; }
    isSubchapter() { return this.type === 'subchapter'; }
    isSection() { return this.type === 'section'; }
    isSubsection() { return this.type === 'subsection'; }
    isArticle() { return this.type === 'article'; }
    isSubarticle() { return this.type === 'subarticle'; }
    isSubdivision() { return this.type === 'subdivision'; }
    isSubparagraph() { return this.type === 'subparagraph'; }
    isSubpart() { return this.type === 'subpart'; }

    isContentNode() {
        return this.isSubsection() ||
            this.isSubdivision() ||
            this.isSubparagraph() ||
            this.isSubpart();
    }
}

export class MockStatute {
    constructor(code = 'mockCode', collection = 'mockCollection') {
        // Root setup
        this.rootNode = new MockNode('/', 'root');
        this.rootNode._hierarchy = [this.rootNode];

        // Collection setup
        this.collectionNode = new MockNode(`/collection/${collection}`, 'collection', null, collection);
        this.rootNode.addChild(this.collectionNode);

        // Code setup
        this.codeNode = new MockNode(`/collection/${collection}/code/${code}`, 'code', code, collection);
        this.collectionNode.addChild(this.codeNode);

        // Title setup
        this.titleNode = new MockNode(`/collection/${collection}/code/${code}/title/1`, 'title', code, collection);
        this.codeNode.addChild(this.titleNode);

        // Subtitle setup
        this.subtitleNode = new MockNode(`/collection/${collection}/code/${code}/title/1/subtitle/A`, 'subtitle', code, collection);
        this.titleNode.addChild(this.subtitleNode);

        // Chapter setup
        this.chapterNode = new MockNode(`/collection/${collection}/code/${code}/title/1/subtitle/A/chapter/1`, 'chapter', code, collection);
        this.subtitleNode.addChild(this.chapterNode);

        // Subchapter setup
        this.subchapterNode = new MockNode(`/collection/${collection}/code/${code}/title/1/subtitle/A/chapter/1/subchapter/A`, 'subchapter', code, collection);
        this.chapterNode.addChild(this.subchapterNode);

        // Section setup
        this.sectionNode = new MockNode(`/collection/${collection}/code/${code}/title/1/subtitle/A/chapter/1/subchapter/A/section/1`, 'section', code, collection);
        this.subchapterNode.addChild(this.sectionNode);

        // Subsection setup
        this.subsectionNode = new MockNode(`/collection/${collection}/code/${code}/title/1/subtitle/A/chapter/1/subchapter/A/section/1(a)`, 'subsection', code, collection);
        this.sectionNode.addChild(this.subsectionNode);
    }
}

export class MockConstitution {
    constructor(collection = 'tx') {
        // Root setup
        this.rootNode = new MockNode('/', 'root');
        this.rootNode._hierarchy = [this.rootNode];

        // Collection setup
        this.collectionNode = new MockNode(`/collection/${collection}`, 'collection', null, collection);
        this.rootNode.addChild(this.collectionNode);

        // Constitution specific hierarchy
        this.codeNode = new MockNode(`/collection/${collection}/code/cn`, 'code', 'cn', collection);
        this.collectionNode.addChild(this.codeNode);

        // Article setup
        this.articleNode = new MockNode(`/collection/${collection}/code/cn/article/1`, 'article', 'cn', collection);
        this.codeNode.addChild(this.articleNode);

        this.article3Node = new MockNode(`/collection/${collection}/code/cn/article/3`, 'article', 'cn', collection);
        this.codeNode.addChild(this.article3Node);

        this.article8Node = new MockNode(`/collection/${collection}/code/cn/article/8`, 'article', 'cn', collection);
        this.codeNode.addChild(this.article8Node);

        // Add specific constitution sections for testing
        this.sectionNode = new MockNode('/collection/tx/code/cn/article/1/section/1', 'section', 'cn', 'tx');
        this.articleNode.addChild(this.sectionNode);

        this.subsectionNode = new MockNode(`/collection/${collection}/code/cn/article/1/section/1(a)`, 'subsection', 'cn', collection);
        this.sectionNode.addChild(this.subsectionNode);

        // Subarticle setup
        this.subarticleNode = new MockNode(`/collection/${collection}/code/cn/article/1/subarticle/1-1`, 'subarticle', 'cn', collection);
        this.articleNode.addChild(this.subarticleNode);

        // Section setup
        this.section2Node = new MockNode(`/collection/${collection}/code/cn/article/1/subarticle/1-1/section/2`, 'section', 'cn', collection);
        this.subarticleNode.addChild(this.section2Node);

        // Subsection setup
        this.subsection2Node = new MockNode(`/collection/${collection}/code/cn/article/1/subarticle/1-1/section/2(a)`, 'subsection', 'cn', collection);
        this.section2Node.addChild(this.subsection2Node);
    }
}
