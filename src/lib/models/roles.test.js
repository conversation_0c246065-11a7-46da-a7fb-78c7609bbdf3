import { isSystemAdmin } from './roles';

describe('isSystemAdmin', () => {
  it('returns false if session is null', () => {
    expect(isSystemAdmin(null)).toBe(false);
  });
  it('returns false if session.user is missing', () => {
    expect(isSystemAdmin({})).toBe(false);
  });
  it('returns false if roles is not an array', () => {
    expect(isSystemAdmin({ user: { roles: null } })).toBe(false);
    expect(isSystemAdmin({ user: { roles: undefined } })).toBe(false);
    expect(isSystemAdmin({ user: { roles: 'systemAdmin' } })).toBe(false);
  });
  it('returns false if roles array is empty', () => {
    expect(isSystemAdmin({ user: { roles: [] } })).toBe(false);
  });
  it('returns false if roles does not include systemAdmin', () => {
    expect(isSystemAdmin({ user: { roles: ['user', 'editor'] } })).toBe(false);
  });
  it('returns true if roles includes systemAdmin', () => {
    expect(isSystemAdmin({ user: { roles: ['systemAdmin'] } })).toBe(true);
    expect(isSystemAdmin({ user: { roles: ['user', 'systemAdmin'] } })).toBe(true);
  });
  it('returns true if roles includes systemAdmin among other roles', () => {
    expect(isSystemAdmin({ user: { roles: ['user', 'systemAdmin', 'editor'] } })).toBe(true);
  });
});
