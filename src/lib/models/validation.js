export class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.field = field;
  }
}

export function validate(data, schema) {
  if (!data || !schema) return;

  Object.entries(schema).forEach(([field, rules]) => {
    // Skip validation if field is not required and value is undefined
    if (!rules.required && data[field] === undefined) {
      return;
    }

    // Required field check
    if (rules.required && (data[field] === undefined || data[field] === null)) {
      throw new Error(`Missing required field: ${field}`);
    }

    // Type checking with dataType
    if (rules.dataType) {
      if (rules.dataType === 'array') {
        if (!Array.isArray(data[field])) {
          throw new Error(`Invalid type for ${field}: expected array, got ${typeof data[field]}`);
        }
      } else if (typeof data[field] !== rules.dataType) {
        throw new Error(`Invalid type for ${field}: expected ${rules.dataType}, got ${typeof data[field]}`);
      }
    }

    // Value validation
    if (rules.values && !rules.values.includes(data[field])) {
      throw new Error(`Invalid value for ${field}: ${data[field]}`);
    }

    // Custom validation function
    if (typeof rules === 'function' && !rules(data[field])) {
      throw new Error(`Validation failed for ${field}`);
    }
  });

  return data;
}

export const schemas = {
  collection: {
    id: { dataType: 'string', required: true },
    text: { dataType: 'string', required: true }
  },
  
  code: {
    id: { dataType: 'string', required: true },
    text: { dataType: 'string', required: true },
    code: { dataType: 'string', required: true }
  },
  
  node: {
    id: { dataType: 'string', required: true },
    type: { 
      dataType: 'string', 
      required: true,
      values: [
        'root',
        'collection',
        'code',
        'title',
        'subtitle',
        'chapter',
        'subchapter',
        'section',
        'subsection',
        'article',
        'subarticle',
        'paragraph',
        'subparagraph',
        'clause',
        'subclause',
        'item',
        'subitem',
        'part',
        'subpart',
        'division',
        'subdivision'
      ]
    },
    text: { dataType: 'string', required: true }
  },
  section: {
    id: { dataType: 'string', required: true },
    text: { dataType: 'string', required: true },
    type: { 
      dataType: 'string', 
      required: true,
      values: ['section']  // Section type must be 'section'
    }
  },
  subsection: {
    id: { dataType: 'string', required: true },
    text: { dataType: 'string', required: true },
    type: { 
      dataType: 'string', 
      required: true,
      values: ['subsection']
    },
    level: { dataType: 'number', required: true }
  },
  highlight: {
    matches: {
      dataType: 'array',
      required: true,
      validate: matches => 
        matches.length > 0 && 
        matches.every(match => {
          const validColor = ['yellow', 'pink', 'blue', 'orange', 'green', 'purple'].includes(match.color);
          const validOffsets = 
            typeof match.startOffset === 'number' &&
            typeof match.endOffset === 'number' &&
            match.startOffset < match.endOffset;
          const validNodeId = typeof match.nodeId === 'string' && match.nodeId.length > 0;
          
          return validColor && validOffsets && validNodeId;
        })
    }
  },
  searchMatch: {
    text: t => typeof t === 'string' && t.length > 0,
    index: i => typeof i === 'number' && i >= 0,
    length: l => typeof l === 'number' && l > 0
  },
  searchResult: {
    node: {
      required: true,
      dataType: 'object'
    },
    matches: {
      required: true,
      dataType: 'array'
    },
    score: {
      required: true,
      dataType: 'number'
    }
  }
}; 