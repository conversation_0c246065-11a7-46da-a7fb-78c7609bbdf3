import { TexasCodeReferenceResolver } from './TexasCodeReferenceResolver';
import { TexasConstitutionReferenceResolver } from './TexasConstitutionReferenceResolver';

export class ReferenceResolverFactory {
  static createResolver(node) {
    // Handle null/undefined nodes
    if (!node) {
      console.warn('ReferenceResolverFactory: Node is null or undefined');
      return null;
    }

    // Get collection and code, using getters as fallback
    const collection = node.collection || (typeof node.getCollection === 'function' ? node.getCollection() : null);
    const code = node.code || (typeof node.getCode === 'function' ? node.getCode() : null);

    let resolver = null;

    if (collection === 'tx' && code === 'cn') {
      resolver = new TexasConstitutionReferenceResolver(node);
    }
    else if (collection === 'tx' && !!code) {
      resolver = new TexasCodeReferenceResolver(node);
    } else {
      // Log warning for unsupported collection/code combinations
      console.warn('ReferenceResolverFactory: Unsupported collection/code combination', {
        nodeId: node.nodeId || 'unknown',
        type: node.type || 'unknown',
        collection,
        code
      });
    }

    return resolver;
  }
}
