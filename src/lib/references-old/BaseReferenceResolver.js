import { ReferencePatterns } from './ReferencePatterns';

export class BaseReferenceResolver {
  constructor(node) {
    this.node = node || {};
    this.rootNode = node?.getRootNode ? node.getRootNode() : node;
    this.referencePatterns = new ReferencePatterns();
    this.patterns = [];

    // Validate that we have a valid node
    if (!node) {
      console.warn(`[${this._instanceId}] BaseReferenceResolver: Node is null or undefined`);
    }
  }

  async findReferences(text) {
    const referencePromises = [];

    if (!text) {
      return [];
    }

    for (let i = 0; i < this.patterns.length; i++) {
      const { pattern, buildLink, name } = this.patterns[i];
      const matches = Array.from(text.matchAll(pattern));

      for (const match of matches) {
        try {
          // Store the promise from buildLink
          const referencePromise = Promise.resolve(buildLink(...match, match.index))
            .then(reference => {
              if (reference) {
                // Add pattern name to the reference
                reference.pattern = name || `pattern_${i}`;
                return reference;
              }
              return null;
            })
            .catch(error => {
              console.error('Error building link for match:', { name, match: match[0], error });
              return null;
            });

          referencePromises.push(referencePromise);
        } catch (error) {
          console.error('Error building link for match:', { name, match: match[0], error });
        }
      }
    }

    // Wait for all reference promises to resolve
    const resolvedReferences = await Promise.all(referencePromises);
    const references = resolvedReferences.filter(ref => ref !== null);

    console.log(`Before filtering ${this.node.nodeId}:`, references.map(ref => ({
      text: ref.text,
      startOffset: ref.startOffset,
      endOffset: ref.endOffset,
      pattern: ref.pattern,
      matches: ref.matches?.map(m => ({
        text: m.text,
        target: m.target,
        startOffset: m.startOffset,
        endOffset: m.endOffset,
        status: m.status
      })),
    })));

    // Filter out references with invalid offsets or no matches
    let filtered = references.filter(ref =>
      typeof ref.startOffset === 'number' &&
      !isNaN(ref.startOffset) &&
      typeof ref.endOffset === 'number' &&
      !isNaN(ref.endOffset) &&
      ref.matches?.length > 0 &&
      // Ensure all matches have valid offsets
      ref.matches.every(match =>
        typeof match.startOffset === 'number' &&
        !isNaN(match.startOffset) &&
        typeof match.endOffset === 'number' &&
        !isNaN(match.endOffset)
      )
    );

    // Sort by length of matched text (longest first) and position
    filtered.sort((a, b) => {
      // First by number of matches
      const matchDiff = (b.matches?.length || 0) - (a.matches?.length || 0);
      if (matchDiff !== 0) return matchDiff;

      // Then by total match text length (prioritize patterns that capture more text across all matches)
      const aTotalMatchLength = a.matches?.reduce((sum, m) => sum + m.text.length, 0) || 0;
      const bTotalMatchLength = b.matches?.reduce((sum, m) => sum + m.text.length, 0) || 0;
      const totalMatchLengthDiff = bTotalMatchLength - aTotalMatchLength;
      if (totalMatchLengthDiff !== 0) return totalMatchLengthDiff;

      // Then by overall text length
      const lengthDiff = b.text.length - a.text.length;
      if (lengthDiff !== 0) return lengthDiff;

      // Then by number of resolved matches (matches without "unresolved" status or undefined targets)
      const aResolvedMatches = a.matches?.filter(m => m.target && m.status !== "unresolved").length || 0;
      const bResolvedMatches = b.matches?.filter(m => m.target && m.status !== "unresolved").length || 0;
      const resolvedMatchDiff = bResolvedMatches - aResolvedMatches;
      if (resolvedMatchDiff !== 0) return resolvedMatchDiff;

      // Then by position
      return a.startOffset - b.startOffset;
    });

    // Filter out overlapping references, keeping the longer/earlier ones
    filtered = filtered.filter((ref, index) => {
      const hasOverlap = filtered.slice(0, index).some(prevRef =>
        ref.startOffset < prevRef.endOffset && prevRef.startOffset < ref.endOffset
      );
      return !hasOverlap;
    });

    // Resort by position for rendering
    filtered.sort((a, b) => a.startOffset - b.startOffset);

    console.log('After filtering:', filtered.map(ref => ({
      text: ref.text,
      startOffset: ref.startOffset,
      endOffset: ref.endOffset,
      pattern: ref.pattern,
      matches: ref.matches?.map(m => ({
        text: m.text,
        target: m.target,
        startOffset: m.startOffset,
        endOffset: m.endOffset,
        status: m.status
      })),
    })));

    return filtered;
  }

  // Add new patterns at runtime
  addPattern(pattern, buildLink) {
    this.patterns.push({ pattern, buildLink });
  }

  async processText(text) {
    if (!text) {
      return [];
    }

    // Only process references if we're in a content node
    if (!this.node.isContentNode()) {
      return [];
    }

    // Get all references and add display properties
    try {
      const references = await this.findReferences(text);
      return references;
    } catch (error) {
      console.error('Error in BaseReferenceResolver.processText:', error);
      return [];
    }
  }
}
