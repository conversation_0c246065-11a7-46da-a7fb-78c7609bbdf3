import { MockNode, MockConstitution } from '@/lib/testing/mockNodes';
import { TexasConstitutionReferenceResolver } from './TexasConstitutionReferenceResolver';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';

// Mock StatuteService.findNodeByStartEndPath
jest.mock('@/components/features/statutes/services/StatuteService', () => {
  return {
    StatuteService: {
      findNodeByStartEndPath: jest.fn(() => {
        // This will be set in each test to use the current rootNode
        return null;
      })
    }
  };
});

describe('TexasConstitutionReferenceResolver', () => {
    let resolver;
    let mockConstitution;

    beforeEach(() => {
        mockConstitution = new MockConstitution('tx');
        resolver = new TexasConstitutionReferenceResolver(mockConstitution.subsectionNode);
        resolver.rootNode = mockConstitution.rootNode;

        // Add specific constitution sections for testing
        const section1A = new MockNode('/collection/tx/code/cn/article/1/section/1-A', 'section', 'cn', 'tx');
        mockConstitution.articleNode.addChild(section1A);

        // Override isContentNode to return true for testing
        resolver.node.isContentNode = () => true;

        // Set up the mock for StatuteService.findNodeByStartEndPath to use the rootNode
        StatuteService.findNodeByStartEndPath.mockImplementation((startPath, endPath) => {
            return mockConstitution.rootNode.findNodeByStartEndPath(startPath, endPath);
        });
    });

    describe('section and article references', () => {
        test('should match basic format (Section 1, Article 1)', async () => {
            const numericSection = new MockNode('/collection/tx/code/cn/article/1/section/1', 'section', 'cn', 'tx');
            mockConstitution.articleNode.addChild(numericSection);

            const result = await resolver.processText('Section 1, Article 1');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 1, Article 1',
                target: numericSection.nodeId
            });
        });

        test('should match basic format (Section 1, Article I)', async () => {
            const numericSection = new MockNode('/collection/tx/code/cn/article/1/section/1', 'section', 'cn', 'tx');
            mockConstitution.articleNode.addChild(numericSection);

            const result = await resolver.processText('Section 1, Article I');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 1, Article I',
                target: numericSection.nodeId
            });
        });

        test('should match basic format (Section 1 of Article I)', async () => {
            const numericSection = new MockNode('/collection/tx/code/cn/article/1/section/1', 'section', 'cn', 'tx');
            mockConstitution.articleNode.addChild(numericSection);

            const result = await resolver.processText('Section 1 of Article I');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 1 of Article I',
                target: numericSection.nodeId
            });
        });

        test('should match hyphenated section (Section 49-c, Article III)', async () => {
            const hyphenatedSection = new MockNode('/collection/tx/code/cn/article/3/section/49-c', 'section', 'cn', 'tx');
            mockConstitution.rootNode.addChild(hyphenatedSection);

            const result = await resolver.processText('Section 49-c, Article III');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 49-c, Article III',
                target: hyphenatedSection.nodeId
            });
        });

        test('should match double-hyphenated section (Section 49-c-1, Article III)', async () => {
            const doubleHyphenSection = new MockNode('/collection/tx/code/cn/article/3/section/49-c-1', 'section', 'cn', 'tx');
            mockConstitution.rootNode.addChild(doubleHyphenSection);

            const result = await resolver.processText('Section 49-c-1, Article III');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 49-c-1, Article III',
                target: doubleHyphenSection.nodeId
            });
        });

        test('should handle extra spaces (Section 1-A , Article I)', async () => {
            const result = await resolver.processText('Section 1 , Article I');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0].text).toBe('Section 1 , Article I');
        });

        test('should not match invalid section numbers', async () => {
            const result = await resolver.processText('Section 999, Article I');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 999, Article I',
                status: 'unresolved'
            });
        });

        test('should not match invalid article numbers', async () => {
            const result = await resolver.processText('Section 1, Article XXX');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 1, Article XXX',
                status: 'unresolved'
            });
        });
    });

    describe('article and section references', () => {
        test('should match basic format (Article 1, Section 1)', async () => {
            const result = await resolver.processText('Article 1, Section 1');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article 1, Section 1',
                target: '/collection/tx/code/cn/article/1/section/1'
            });
        });

        test('should match basic format (Article I, Section 1)', async () => {
            const result = await resolver.processText('Article I, Section 1');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article I, Section 1',
                target: '/collection/tx/code/cn/article/1/section/1'
            });
        });

        test('should match hyphenated section (Article III, Section 49-c)', async () => {
            const hyphenatedSection = new MockNode('/collection/tx/code/cn/article/3/section/49-c', 'section', 'cn', 'tx');
            mockConstitution.rootNode.addChild(hyphenatedSection);

            const result = await resolver.processText('Article III, Section 49-c');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article III, Section 49-c',
                target: hyphenatedSection.nodeId
            });
        });

        test('should match double-hyphenated section (Article III, Section 49-c-1)', async () => {
            const doubleHyphenSection = new MockNode('/collection/tx/code/cn/article/3/section/49-c-1', 'section', 'cn', 'tx');
            mockConstitution.rootNode.addChild(doubleHyphenSection);

            const result = await resolver.processText('Article III, Section 49-c-1');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article III, Section 49-c-1',
                target: doubleHyphenSection.nodeId
            });
        });

        test('should handle extra spaces (Article I , Section 1-A)', async () => {
            const result = await resolver.processText('Article I , Section 1-A');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0].text).toBe('Article I , Section 1-A');
        });

        test('should not match invalid section numbers', async () => {
            const result = await resolver.processText('Article I, Section 999-Z');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article I, Section 999-Z',
                status: 'unresolved'
            });
        });

        test('should not match invalid article numbers', async () => {
            const result = await resolver.processText('Article XXX, Section 1');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article XXX, Section 1',
                status: 'unresolved'
            });
        });

        test('should match with "of this constitution" suffix', async () => {
            const section = new MockNode('/collection/tx/code/cn/article/1/section/1', 'section', 'cn', 'tx');
            mockConstitution.rootNode.addChild(section);

            const result = await resolver.processText('Article I, Section 1 of this constitution');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Article I, Section 1',
                target: section.nodeId
            });
        });
    });

    describe('section list references', () => {
        beforeEach(() => {
            // Use a section node in article 3 for these tests
            const sectionNode = new MockNode('/collection/tx/code/cn/article/3/section/1', 'section', 'cn', 'tx');
            mockConstitution.article3Node.addChild(sectionNode);

            resolver = new TexasConstitutionReferenceResolver(sectionNode);
            resolver.rootNode = mockConstitution.rootNode;

            // Override isContentNode to return true for testing purposes
            resolver.node.isContentNode = () => true;
        });

        test('should match comma-separated list in other article (Sections 7-a, 7-b, Article VIII)', async () => {
            const sections = ['7-a', '7-b'].map(id =>
                new MockNode(`/collection/tx/code/cn/article/8/section/${id}`, 'section', 'cn', 'tx')
            );
            sections.forEach(section => mockConstitution.article8Node.addChild(section));

            const result = await resolver.processText('Sections 7-a, 7-b, Article VIII');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Sections 7-a',
                target: sections[0].nodeId,
                startOffset: 0,
                endOffset: 12
            });
            expect(result[0].matches[1]).toMatchObject({
                text: '7-b',
                target: sections[1].nodeId,
                startOffset: 14,
                endOffset: 17
            });
        });

        test('should match comma-separated list (Sections 49-c, 49-d)', async () => {
            const sections = ['49-c', '49-d'].map(id =>
                new MockNode(`/collection/tx/code/cn/article/3/section/${id}`, 'section', 'cn', 'tx')
            );
            sections.forEach(section => mockConstitution.article3Node.addChild(section));

            const result = await resolver.processText('Sections 49-c, 49-d');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Sections 49-c',
                target: sections[0].nodeId,
                startOffset: 0,
                endOffset: 13
            });
            expect(result[0].matches[1]).toMatchObject({
                text: '49-d',
                target: sections[1].nodeId,
                startOffset: 15,
                endOffset: 19
            });
        });

        test('should match list with "and" (Sections 49-c and 49-d)', async () => {
            const sections = ['49-c', '49-d'].map(id =>
                new MockNode(`/collection/tx/code/cn/article/3/section/${id}`, 'section', 'cn', 'tx')
            );
            sections.forEach(section => mockConstitution.article3Node.addChild(section));

            const result = await resolver.processText('Sections 49-c and 49-d');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Sections 49-c',
                target: sections[0].nodeId,
                startOffset: 0,
                endOffset: 13
            });
            expect(result[0].matches[1]).toMatchObject({
                text: '49-d',
                target: sections[1].nodeId,
                startOffset: 18,
                endOffset: 22
            });
        });

        test('should match list with "or" (Sections 49-c or 49-d)', async () => {
            const sections = ['49-c', '49-d'].map(id =>
                new MockNode(`/collection/tx/code/cn/article/3/section/${id}`, 'section', 'cn', 'tx')
            );
            sections.forEach(section => mockConstitution.article3Node.addChild(section));

            const result = await resolver.processText('Sections 49-c or 49-d');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Sections 49-c',
                target: sections[0].nodeId,
                startOffset: 0,
                endOffset: 13
            });
            expect(result[0].matches[1]).toMatchObject({
                text: '49-d',
                target: sections[1].nodeId,
                startOffset: 17,
                endOffset: 21
            });
        });

        test('should match long list (Sections 49-c, 49-d, 49-d-1, 49-d-2, 49-d-5, 49-d-6, and 49-d-7)', async () => {
            const sections = ['49-c', '49-d', '49-d-1', '49-d-2', '49-d-5', '49-d-6', '49-d-7'].map(id =>
                new MockNode(`/collection/tx/code/cn/article/3/section/${id}`, 'section', 'cn', 'tx')
            );
            sections.forEach(section => mockConstitution.article3Node.addChild(section));

            const result = await resolver.processText('Sections 49-c, 49-d, 49-d-1, 49-d-2, 49-d-5, 49-d-6, and 49-d-7 of this article');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(7);
            result[0].matches.forEach((ref, i) => {
                expect(ref).toMatchObject({
                    target: sections[i].nodeId
                });
            });
        });

        test('should handle extra spaces in list', async () => {
            const sections = ['49-c', '49-d'].map(id =>
                new MockNode(`/collection/tx/code/cn/article/3/section/${id}`, 'section', 'cn', 'tx')
            );
            sections.forEach(section => mockConstitution.article3Node.addChild(section));

            const result = await resolver.processText('Sections 49-c ,  49-d  of  this  article');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            result[0].matches.forEach((ref, i) => {
                expect(ref).toMatchObject({
                    target: sections[i].nodeId
                });
            });
        });

        test('should mark unresolved sections in list', async () => {
            const article3 = mockConstitution.codeNode.findNodeByStartEndPath('/collection/tx/code/cn', '/article/3');
            const section = new MockNode('/collection/tx/code/cn/article/3/section/49-c', 'section', 'cn', 'tx');
            article3.addChild(section);

            const result = await resolver.processText('Sections 49-c, 999-Z');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Sections 49-c',
                target: section.nodeId,
                startOffset: 0,
                endOffset: 13
            });
            expect(result[0].matches[1]).toMatchObject({
                text: '999-Z',
                status: 'unresolved',
                startOffset: 15,
                endOffset: 20
            });
        });
    });

    describe('single section references', () => {
        beforeEach(() => {
            // Create a section node within article 3 for the resolver context
            const section1 = new MockNode('/collection/tx/code/cn/article/3/section/1', 'section', 'cn', 'tx');
            mockConstitution.article3Node.addChild(section1);

            // Update resolver to use the section
            resolver = new TexasConstitutionReferenceResolver(section1);
            resolver.rootNode = mockConstitution.rootNode;

            // Override isContentNode to return true for testing purposes
            resolver.node.isContentNode = () => true;
        });

        test('should match basic section reference (Section 49-d)', async () => {
            const section = new MockNode('/collection/tx/code/cn/article/3/section/49-d', 'section', 'cn', 'tx');
            mockConstitution.article3Node.addChild(section);

            const result = await resolver.processText('Section 49-d');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 49-d',
                target: section.nodeId
            });
        });

        test('should match section with "of this article" (Section 49-d of this article)', async () => {
            const section = new MockNode('/collection/tx/code/cn/article/3/section/49-d', 'section', 'cn', 'tx');
            mockConstitution.article3Node.addChild(section);

            const result = await resolver.processText('Section 49-d of this article');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 49-d',
                target: section.nodeId
            });
        });

        test('should handle extra spaces (Section  49-d  of  this  article)', async () => {
            const section = new MockNode('/collection/tx/code/cn/article/3/section/49-d', 'section', 'cn', 'tx');
            mockConstitution.article3Node.addChild(section);

            const result = await resolver.processText('Section  49-d  of  this  article');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section  49-d',
                target: section.nodeId
            });
        });

        test('should mark unresolved section', async () => {
            const result = await resolver.processText('Section 999-Z of this article');

            expect(result).toHaveLength(1);
            expect(result[0].matches[0]).toMatchObject({
                text: 'Section 999-Z',
                status: 'unresolved'
            });
        });
    });

    describe('subsection references', () => {
        describe('single subsection references', () => {

            test('should match section with subsection (Section 49-d-8(e))', async () => {

                const section = new MockNode('/collection/tx/code/cn/article/3/section/49-d-8', 'section', 'cn', 'tx');
                const subsection = new MockNode('/collection/tx/code/cn/article/3/section/49-d-8(e)', 'subsection', 'cn', 'tx');
                section.addChild(subsection);
                mockConstitution.article3Node.addChild(section);

                // Update resolver to use the section
                resolver = new TexasConstitutionReferenceResolver(section);
                resolver.rootNode = mockConstitution.rootNode;

                // Override isContentNode to return true for testing purposes
                resolver.node.isContentNode = () => true;

                const result = await resolver.processText('Section 49-d-8(e)');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Section 49-d-8(e)',
                    target: subsection.nodeId
                });
            });

            test('should match basic subsection reference (Subsection (a))', async () => {
                const result = await resolver.processText('Subsection (a)');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (a)',
                    target: '/collection/tx/code/cn/article/1/section/1(a)'
                });
            });

            test('should match hyphenated subsection (Subsection (a-1))', async () => {
                const subsection = new MockNode('/collection/tx/code/cn/article/1/subarticle/a/section/1(a-1)', 'subsection', 'cn', 'tx');
                mockConstitution.sectionNode.addChild(subsection);

                const result = await resolver.processText('Subsection (a-1)');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (a-1)',
                    target: subsection.nodeId
                });
            });

            test('should mark unresolved subsection', async () => {
                const result = await resolver.processText('Subsection (z)');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (z)',
                    status: 'unresolved'
                });
            });

            test('should match subsection of specific section (Subsection (b) of Section 49-d-7)', async () => {
                const section = new MockNode('/collection/tx/code/cn/article/3/section/49-d-7', 'section', 'cn', 'tx');
                const subsection = new MockNode('/collection/tx/code/cn/article/3/section/49-d-7(b)', 'subsection', 'cn', 'tx');
                section.addChild(subsection);
                mockConstitution.article3Node.addChild(section);

                // Update resolver to use the section
                resolver = new TexasConstitutionReferenceResolver(section);
                resolver.rootNode = mockConstitution.rootNode;

                // Override isContentNode to return true for testing purposes
                resolver.node.isContentNode = () => true;

                const result = await resolver.processText('Subsection (b) of Section 49-d-7');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (b) of Section 49-d-7',
                    target: subsection.nodeId
                });
            });
        });

        describe('subsection list references', () => {
            test('should match subsection list (Subsections (d) and (e))', async () => {
                const section = new MockNode('/collection/tx/code/cn/article/3/section/49-d-8', 'section', 'cn', 'tx');
                const subsectionD = new MockNode('/collection/tx/code/cn/article/3/section/49-d-8(d)', 'subsection', 'cn', 'tx');
                const subsectionE = new MockNode('/collection/tx/code/cn/article/3/section/49-d-8(e)', 'subsection', 'cn', 'tx');
                section.addChild(subsectionD);
                section.addChild(subsectionE);
                mockConstitution.article3Node.addChild(section);

                // Update resolver to use the section
                resolver = new TexasConstitutionReferenceResolver(section);
                resolver.rootNode = mockConstitution.rootNode;

                // Override isContentNode to return true for testing purposes
                resolver.node.isContentNode = () => true;

                const result = await resolver.processText('Subsections (d) and (e)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsections (d)',
                    target: subsectionD.nodeId
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(e)',
                    target: subsectionE.nodeId
                });
            });
        });
    });

    describe('subdivision references', () => {
        describe('single subdivision references', () => {
            test('should match subdivision reference (Subdivision (1))', async () => {
                const subdivision = new MockNode('/collection/tx/code/cn/article/1/section/1(a)(1)', 'subdivision', 'cn', 'tx');
                mockConstitution.subsectionNode.addChild(subdivision);

                resolver = new TexasConstitutionReferenceResolver(mockConstitution.subsectionNode);
                resolver.rootNode = mockConstitution.rootNode;

                const result = await resolver.processText('Subdivision (1)');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subdivision (1)',
                    target: '/collection/tx/code/cn/article/1/section/1(a)(1)'
                });
            });
        });

        describe('subdivision list references', () => {
            beforeEach(() => {
                // Create section under article 3
                const subsection = new MockNode('/collection/tx/code/cn/article/1/section/1(b)', 'subsection', 'cn', 'tx');
                mockConstitution.sectionNode.addChild(subsection);

                // Create subdivision under the section
                const subdivision1 = new MockNode('/collection/tx/code/cn/article/1/section/1(b)(1)', 'subdivision', 'cn', 'tx');
                subsection.addChild(subdivision1);

                // Create subdivision under the section
                const subdivision2 = new MockNode('/collection/tx/code/cn/article/1/section/1(b)(2)', 'subdivision', 'cn', 'tx');
                subsection.addChild(subdivision2);

                // Update resolver to use section context
                resolver = new TexasConstitutionReferenceResolver(subsection);
                resolver.rootNode = mockConstitution.rootNode;
            });

            test('should match subdivision list (Subdivisions (1) and (2))', async () => {
                const result = await resolver.processText('Subdivisions (1) and (2)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subdivisions (1)',
                    target: '/collection/tx/code/cn/article/1/section/1(b)(1)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(2)',
                    target: '/collection/tx/code/cn/article/1/section/1(b)(2)'
                });
            });

            test('should match subdivision list (Subdivisions (1) and (2) of Subsection (b))', async () => {
                resolver = new TexasConstitutionReferenceResolver(mockConstitution.subsectionNode);
                resolver.rootNode = mockConstitution.rootNode;

                const result = await resolver.processText('Subdivisions (1) and (2) of Subsection (b)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subdivisions (1)',
                    target: '/collection/tx/code/cn/article/1/section/1(b)(1)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(2)',
                    target: '/collection/tx/code/cn/article/1/section/1(b)(2)'
                });
            });
        });
    });
});
