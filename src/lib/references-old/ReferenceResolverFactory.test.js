import { ReferenceResolverFactory } from './ReferenceResolverFactory';
import { TexasCodeReferenceResolver } from './TexasCodeReferenceResolver';
import { TexasConstitutionReferenceResolver } from './TexasConstitutionReferenceResolver';
import { MockNode } from '@/lib/testing/mockNodes';

describe('ReferenceResolverFactory', () => {
    const originalWarn = console.warn;

    beforeAll(() => {
        console.warn = jest.fn();
    });

    afterAll(() => {
        console.warn = originalWarn;
    });

    describe('createResolver', () => {
        test('should create TexasConstitutionReferenceResolver for Texas Constitution nodes', () => {
            const node = new MockNode('/collection/tx/code/cn', 'code', 'cn', 'tx');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeInstanceOf(TexasConstitutionReferenceResolver);
        });

        test('should create TexasCodeReferenceResolver for Texas Code nodes', () => {
            const node = new MockNode('/collection/tx/code/gv', 'code', 'gv', 'tx');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeInstanceOf(TexasCodeReferenceResolver);
        });

        test('should return null for non-Texas nodes', () => {
            const node = new MockNode('/collection/ca/code/gv', 'code', 'gv', 'ca');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeNull();
        });

        test('should create correct resolver for Texas Constitution article node', () => {
            const node = new MockNode('/collection/tx/code/cn/article/1', 'article', 'cn', 'tx');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeInstanceOf(TexasConstitutionReferenceResolver);
        });

        test('should create correct resolver for Texas Constitution section node', () => {
            const node = new MockNode('/collection/tx/code/cn/article/1/section/1', 'section', 'cn', 'tx');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeInstanceOf(TexasConstitutionReferenceResolver);
        });

        test('should create correct resolver for Texas Government Code node', () => {
            const node = new MockNode('/collection/tx/code/gv/section/411.087', 'section', 'gv', 'tx');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeInstanceOf(TexasCodeReferenceResolver);
        });

        test('should create correct resolver for Texas Tax Code node', () => {
            const node = new MockNode('/collection/tx/code/tx/section/11.13', 'section', 'tx', 'tx');
            const resolver = ReferenceResolverFactory.createResolver(node);

            expect(resolver).toBeInstanceOf(TexasCodeReferenceResolver);
        });

        test('should handle undefined node', () => {
            const resolver = ReferenceResolverFactory.createResolver(undefined);
            expect(resolver).toBeNull();
        });

        test('should handle null node', () => {
            const resolver = ReferenceResolverFactory.createResolver(null);
            expect(resolver).toBeNull();
        });

        test('should handle node without collection property', () => {
            const node = { code: 'gv' };
            const resolver = ReferenceResolverFactory.createResolver(node);
            expect(resolver).toBeNull();
        });

        test('should handle node without code property', () => {
            const node = { collection: 'tx' };
            const resolver = ReferenceResolverFactory.createResolver(node);
            expect(resolver).toBeNull();
        });

        test('should handle node with empty code property', () => {
            const node = { collection: 'tx', code: '' };
            const resolver = ReferenceResolverFactory.createResolver(node);
            expect(resolver).toBeNull();
        });

        test('should handle node with undefined code property', () => {
            const node = { collection: 'tx', code: undefined };
            const resolver = ReferenceResolverFactory.createResolver(node);
            expect(resolver).toBeNull();
        });

        test('should handle node with null code property', () => {
            const node = { collection: 'tx', code: null };
            const resolver = ReferenceResolverFactory.createResolver(node);
            expect(resolver).toBeNull();
        });
    });
});
