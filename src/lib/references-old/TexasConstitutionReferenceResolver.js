import { BaseReferenceResolver } from './BaseReferenceResolver';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';

export class TexasConstitutionReferenceResolver extends BaseReferenceResolver {
  constructor(node) {
    super(node);
    this.initializePatterns();
  }

  // Helper methods to find a section node
  async findArticleNode(articleNumber) {
    const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
    const endPath = `/article/${articleNumber}`;
    return await StatuteService.findNodeByStartEndPath(startPath, endPath);
  }

  async findSectionNode(sectionNumber, articleNumber = null) {
    // If we have a specific article number, try that first
    if (articleNumber) {
      const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
      const endPath = `/article/${articleNumber}/section/${sectionNumber}`;
      const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
      if (node) {
        return node;
      }

      // Try looking in subarticles
      const articleNode = await this.findArticleNode(articleNumber);
      if (articleNode) {
        // Get all subarticles
        const subarticles = articleNode.children.filter(n => n.isSubarticle());

        // Try each subarticle path
        for (const subarticle of subarticles) {
          const endPath = `/article/${articleNumber}/subarticle/${subarticle.id}/section/${sectionNumber}`;
          const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          if (node) {
            return node;
          }
        }
      }
    }

    // If no article number or not found in specific article, try all articles
    const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;

    // First try direct article/section paths
    const articles = this.rootNode.getChildrenOfType('article');
    for (const article of articles) {
      const endPath = `/article/${article.id}/section/${sectionNumber}`;
      const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
      if (node) {
        return node;
      }

      // Try subarticles
      const subarticles = article.getChildrenOfType('subarticle');
      for (const subarticle of subarticles) {
        const endPath = `/article/${article.id}/subarticle/${subarticle.id}/section/${sectionNumber}`;
        const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
        if (node) {
          return node;
        }
      }
    }

    return null;
  }

  async findSubsectionNode(sectionNumber, subsection, articleNumber = null) {
    // If we have a specific article number, try that first
    if (articleNumber) {
      const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
      const endPath = `/article/${articleNumber}/section/${sectionNumber}${subsection}`;
      const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
      if (node) {
        return node;
      }

      // Try looking in subarticles
      const articleNode = await this.findArticleNode(articleNumber);
      if (articleNode) {
        // Get all subarticles
        const subarticles = articleNode.children.filter(n => n.isSubarticle());

        // Try each subarticle path
        for (const subarticle of subarticles) {
          const endPath = `/article/${articleNumber}/subarticle/${subarticle.id}/section/${sectionNumber}${subsection}`;
          const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          if (node) {
            return node;
          }
        }
      }
    }

    // If no article number or not found in specific article, try all articles
    const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;

    // First try direct article/section paths
    const articles = this.rootNode.getChildrenOfType('article');
    for (const article of articles) {
      const endPath = `/article/${article.id}/section/${sectionNumber}${subsection}`;
      const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
      if (node) {
        return node;
      }

      // Try subarticles
      const subarticles = article.getChildrenOfType('subarticle');
      for (const subarticle of subarticles) {
        const endPath = `/article/${article.id}/subarticle/${subarticle.id}/section/${sectionNumber}${subsection}`;
        const node = await StatuteService.findNodeByStartEndPath(startPath, endPath);
        if (node) {
          return node;
        }
      }
    }

    return null;
  }

  async findSubsectionInCurrentSection(subsection) {
    const contextNode = this.node.getHierarchy().find(n => n.isSection());
    if (!contextNode) {
      return null;
    }

    const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
    const endPath = `/section/${contextNode.id}${subsection}`;
    const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

    if (!targetNode) {
      return null;
    }

    return targetNode;
  }

  async findSubdivisionInCurrentSubsection(subdivision) {
    const contextNode = this.node.getHierarchy().find(n => n.isSubsection());
    if (!contextNode) {
      return null;
    }

    const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
    const endPath = `${contextNode.nodeId.split('/').slice(-1)[0]}${subdivision}`;
    const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

    if (!targetNode) {
      return null;
    }

    return targetNode;
  }

  async findSubdivisionInSubsection(subdivision, subsection) {
    const contextNode = this.node.getHierarchy().find(n => n.isSection());
    if (!contextNode) {
      return null;
    }

    const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
    const endPath = `/section/${contextNode.id}${subsection}${subdivision}`;
    const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

    if (!targetNode) {
      return null;
    }

    return targetNode;
  }

  initializePatterns() {
    // Patterns ordered from most specific (most parts) to least specific (least parts)
    this.patterns = [
      // List of sections reference
      // Example: Sections 49-c, 49-d, 49-d-1, 49-d-2, 49-d-5, 49-d-6, and 49-d-7
      {
        name: 'section_list',
        pattern: this.referencePatterns.LIST_OF(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER
        ),
        buildLink: async (matchedText, firstRef, section1, restOfRefs, lastSection, offset) => {
          console.log('section_list:', {
            matchedText,
            firstRef,
            section1,
            restOfRefs,
            lastSection,
            offset
          });

          // Get the current article context
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          if (!articleId) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the first section node
          const targetNode1 = await this.findSectionNode(section1, articleId);

          // Create matches array with the first section
          let matches = [];
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          // Extract all section numbers from the rest of the text
          const additionalSections = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.CN_SECTION_NUMBER)] : [];

          // If there's no rest of text, we're done
          if (!additionalSections || additionalSections.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Calculate the starting position of restOfRefs within the full matchedText
          const restOfRefsPos = matchedText.indexOf(restOfRefs);

          // Process each additional section
          for (const match of additionalSections) {
            const section = match[0]; // Get the captured section number
            const sectionPos = offset + restOfRefsPos + match.index;

            // Find the section node
            const targetNode = await this.findSectionNode(section, articleId);

            matches.push({
              text: section,
              target: targetNode?.nodeId,
              startOffset: sectionPos,
              endOffset: sectionPos + section.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Section and article reference
      // Example: Section 49-d, Article III
      {
        name: 'section_and_article',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.ARTICLE_PREFIX,
          this.referencePatterns.ARTICLE_NUMBER
        ),
        buildLink: async (matchedText, section, article, offset) => {
          // Convert article roman numeral to arabic
          const articleNumber = this.referencePatterns.getArticleNumber(article);
          const targetNode = await this.findSectionNode(section, articleNumber);
          console.log('section_and_article: ', {
            matchedText,
            section,
            article,
            articleNumber,
            offset,
            node: this.node.nodeId,
            targetNode: targetNode?.nodeId
          });

          const match = {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };

          console.log('section_and_article match:', match);

          return match;
        }
      },

      // Article and section reference
      // Example: Article 1, Section 19
      // Example: Article 16, Section 31
      {
        name: 'article_and_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.ARTICLE_PREFIX,
          this.referencePatterns.ARTICLE_NUMBER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER,
        ),
        buildLink: async (matchedText, article, section, offset) => {
          console.log('article_and_section:', {
            matchedText,
            article,
            section,
            offset
          });

          const articleNumber = this.referencePatterns.getArticleNumber(article);
          const targetNode = await this.findSectionNode(section, articleNumber);
          console.log('article_and_section targetNode:', {
            articleNumber,
            targetNode: targetNode?.nodeId,
          });

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Article and section reference
      // Example: Article I, Section 19
      // Example: Article XVI, Section 31
      {
        name: 'article_and_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.ARTICLE_PREFIX,
          this.referencePatterns.ROMAN_NUMERALS,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER,
        ),
        buildLink: async (matchedText, article, section, offset) => {
          console.log('article_and_section:', {
            matchedText,
            article,
            section,
            offset
          });

          const articleNumber = this.referencePatterns.getArticleNumber(article);
          const targetNode = await this.findSectionNode(section, articleNumber);
          console.log('article_and_section targetNode:', {
            articleNumber,
            targetNode: targetNode?.nodeId,
          });

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Multiple sections reference
      // Example: Sections 49-c and 49-d
      // Example: Sections 49-c or 49-d
      // Example: Sections 49-c through 49-f
      {
        name: 'multiple_sections_with_and_or_through',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SECTION_PREFIX, this.referencePatterns.CN_SECTION_NUMBER],
          this.referencePatterns.AND_OR_THROUGH,
          this.referencePatterns.CN_SECTION_NUMBER
        ),
        buildLink: async (matchedText, firstRef, section1, section2, offset) => {
          // Get the current article context or extract it from the node ID
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          const targetNode1 = await this.findSectionNode(section1, articleId);
          const targetNode2 = await this.findSectionNode(section2, articleId);
          const offset2 = offset + matchedText.indexOf(section2);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset,
                endOffset: offset + firstRef.length,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: section2,
                target: targetNode2?.nodeId,
                startOffset: offset2,
                endOffset: offset2 + section2.length,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // List of sections reference with commas
      // Example: Sections 49-c, 49-d
      {
        name: 'two_sections_with_comma',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER],
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.CN_SECTION_NUMBER
        ),
        buildLink: async (matchedText, firstRef, section1, section2, offset) => {
          // Get the current article context
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          if (!articleId) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          const targetNode1 = await this.findSectionNode(section1, articleId);
          const targetNode2 = await this.findSectionNode(section2, articleId);

          // Find position of second section in original text
          const secondSectionPos = matchedText.indexOf(section2);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset,
                endOffset: offset + firstRef.length,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: section2,
                target: targetNode2?.nodeId,
                startOffset: offset + secondSectionPos,
                endOffset: offset + secondSectionPos + section2.length,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // List of sections, article reference
      // Example: Sections 7-a, 7-b, Article VIII
      {
        name: 'section_article_list',
        pattern: this.referencePatterns.LIST_OF_WITH_SUFFIX(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.combine(
            this.referencePatterns.ARTICLE_PREFIX,
            this.referencePatterns.ARTICLE_NUMBER
          )
        ),
        buildLink: async (matchedText, firstRef, section1, restOfRefs, lastSection, article, offset) => {
          console.log('section_article_list', {
            matchedText,
            firstRef,
            section1,
            restOfRefs,
            lastSection,
            article,
            offset
          });

          // Get the article number directly
          const articleNumber = this.referencePatterns.getArticleNumber(article);

          const contextNode = await this.findArticleNode(articleNumber);
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the first section node
          const targetNode1 = await this.findSectionNode(section1, articleNumber);

          // Create matches array with the first section
          let matches = [];
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          // Extract all section numbers from the rest of the text
          const additionalSections = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.CN_SECTION_NUMBER)] : [];

          // If there's no rest of text, we're done
          if (!additionalSections || additionalSections.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Calculate the starting position of restOfRefs within the full matchedText
          const restOfRefsPos = matchedText.indexOf(restOfRefs);

          // Process each additional section
          for (const match of additionalSections) {
            const section = match[0]; // Get the captured section number
            const sectionPos = offset + restOfRefsPos + match.index;

            // Find the section node
            const targetNode = await this.findSectionNode(section, articleNumber);

            matches.push({
              text: section,
              target: targetNode?.nodeId,
              startOffset: sectionPos,
              endOffset: sectionPos + section.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Single section reference
      // Example: Section 49-d
      {
        name: 'internal_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER
        ),
        buildLink: async (matchedText, section, offset) => {
          console.log('internal_section:', {
            matchedText,
            section,
            offset
          });

          // Get the current article context
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          if (!articleId) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the section node
          const targetNode = await this.findSectionNode(section, articleId);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Single section reference with "of this article"
      // Example: Section 49-d of this article
      {
        name: 'internal_section_with_article',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER,
          this.referencePatterns.OF_THE_THIS,
          this.referencePatterns.ARTICLE_PREFIX
        ),
        buildLink: async (matchedText, section, offset) => {
          console.log('internal_section_with_article:', {
            matchedText,
            section,
            offset
          });

          // Get the current article context
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          if (!articleId) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the section node
          const targetNode = await this.findSectionNode(section, articleId);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Section with subsection reference
      // Example: Section 49-d-8(e)
      {
        name: 'section_with_subsection',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, section, subsection, offset) => {
          console.log('section_with_subsection:', {
            matchedText,
            section,
            subsection,
            offset
          });

          // Get the current article context
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          if (!articleId) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the subsection node
          const targetNode = await this.findSubsectionNode(section, subsection, articleId);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Subsection reference with specific section
      // Example: Subsection (b) of Section 49-d-7
      {
        name: 'subsection_with_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBSECTION_PREFIX,
          this.referencePatterns.SUBSECTION,
          this.referencePatterns.OF_THE_THIS,
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.CN_SECTION_NUMBER
        ),
        buildLink: async (matchedText, subsection, section, offset) => {
          console.log('subsection_with_section:', {
            matchedText,
            subsection,
            section,
            offset
          });

          // Get the current article context
          const contextNode = this.node.getHierarchy().find(n => n.isArticle());

          // If we can't find an article in the hierarchy, try to get it from the node ID
          let articleId;
          if (contextNode) {
            articleId = contextNode.id;
          } else {
            // Extract article ID from the node ID
            const match = this.node.nodeId.match(/\/article\/([^\/]+)/);
            articleId = match ? match[1] : null;
          }

          if (!articleId) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the subsection node
          const targetNode = await this.findSubsectionNode(section, subsection, articleId);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Subsection list reference
      // Example: Subsections (d) and (e)
      {
        name: 'subsection_list',
        pattern: this.referencePatterns.LIST_OF(
          this.referencePatterns.SUBSECTION_PREFIX,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, firstRef, subsection1, restOfRefs, lastSubsection, offset) => {
          console.log('subsection_list:', {
            matchedText,
            firstRef,
            subsection1,
            restOfRefs,
            lastSubsection,
            offset
          });

          // Get the current section context
          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: 'unresolved'
              }]
            };
          }

          // Find the first subsection node
          const targetNode1 = await this.findSubsectionInCurrentSection(subsection1);

          // Create matches array with the first subsection
          let matches = [];
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          // Extract all subsection numbers from the rest of the text
          const additionalSubsections = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SUBSECTION)] : [];

          // If there's no rest of text, we're done
          if (!additionalSubsections || additionalSubsections.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Calculate the starting position of restOfRefs within the full matchedText
          const restOfRefsPos = matchedText.indexOf(restOfRefs);

          // Process each additional subsection
          for (const match of additionalSubsections) {
            const subsection = match[0]; // Get the captured subsection
            const subsectionPos = offset + restOfRefsPos + match.index;

            // Find the subsection node
            const targetNode = await this.findSubsectionInCurrentSection(subsection);

            matches.push({
              text: subsection,
              target: targetNode?.nodeId,
              startOffset: subsectionPos,
              endOffset: subsectionPos + subsection.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Internal subsection reference
      // Example: Subsection (a)
      {
        name: 'internal_subsection',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBSECTION_PREFIX,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, subsection, offset) => {
          // Use the new helper method to find the subsection node
          const targetNode = await this.findSubsectionInCurrentSection(subsection);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Subdivision, subsection list
      // Example: Subdivisions (1) and (2) of Subsection (b)
      {
        name: 'subdivision_subsection_list',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.LIST_OF(
            this.referencePatterns.SUBDIVISION_PREFIX,
            this.referencePatterns.SUBDIVISION
          ),
          this.referencePatterns.OF_THE_THIS,
          this.referencePatterns.SUBSECTION_PREFIX,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, firstRef, subdivision1, restOfRefs, lastSubdivision, subsection, offset) => {
          console.log('subdivision_subsection_list:', {
            matchedText,
            firstRef,
            subdivision1,
            restOfRefs,
            lastSubdivision,
            subsection,
            offset
          });

          const targetNode1 = await this.findSubdivisionInSubsection(subdivision1, subsection);
          console.log('subdivision_subsection_list targetNode1:', targetNode1?.nodeId);

          let matches = [];
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          const subdivisions = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SUBDIVISION)] : [];
          if (!subdivisions || subdivisions.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Calculate the base position of restOfRefs within the original text
          const restOfRefsPos = matchedText.indexOf(restOfRefs);

          // Process all subdivisions after the first one
          for (const match of subdivisions) {
            const subdivision = match[0]; // Get the captured subdivision
            const subdivisionPos = offset + restOfRefsPos + match.index;
            const targetNode = await this.findSubdivisionInSubsection(subdivision, subsection);

            matches.push({
              text: subdivision,
              target: targetNode?.nodeId,
              startOffset: subdivisionPos,
              endOffset: subdivisionPos + subdivision.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Subdivision list
      // Example: Subdivisions (1) and (2)
      {
        name: 'subdivision_list',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.LIST_OF(
            this.referencePatterns.SUBDIVISION_PREFIX,
            this.referencePatterns.SUBDIVISION
          ),
        ),
        buildLink: async (matchedText, firstRef, subdivision1, restOfRefs, lastSection, offset) => {
          console.log('subdivision_list:', {
            matchedText,
            firstRef,
            subdivision1,
            restOfRefs,
            lastSection,
            offset
          });

          const targetNode1 = await this.findSubdivisionInCurrentSubsection(subdivision1);
          console.log('subdivision_list targetNode1:', targetNode1?.nodeId);

          let matches = [];
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          const subdivisions = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SUBDIVISION)] : [];
          if (!subdivisions || subdivisions.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Calculate the base position of restOfRefs within the original text
          const restOfRefsPos = matchedText.indexOf(restOfRefs);

          // Process all subdivisions after the first one
          for (const match of subdivisions) {
            const subdivision = match[0]; // Get the captured subdivision
            const subdivisionPos = offset + restOfRefsPos + match.index;
            const targetNode = await this.findSubdivisionInCurrentSubsection(subdivision);

            matches.push({
              text: subdivision,
              target: targetNode?.nodeId,
              startOffset: subdivisionPos,
              endOffset: subdivisionPos + subdivision.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Subdivision reference
      // Example: Subdivision (1)
      {
        name: 'subdivision',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBDIVISION_PREFIX,
          this.referencePatterns.SUBDIVISION
        ),
        buildLink: async (matchedText, subdivision, offset) => {
          console.log('subdivision:', {
            matchedText,
            subdivision,
            offset
          });

          const targetNode = await this.findSubdivisionInCurrentSubsection(subdivision);
          console.log('subdivision targetNode', targetNode);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      }
    ];
  }
}
