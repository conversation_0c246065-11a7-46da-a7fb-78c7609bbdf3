export class ReferencePatterns {
  static CODE_MAP = {
    'Alcoholic Beverage Code': 'al',
    'Agriculture Code': 'ag',
    'Auxillary Water Laws': 'wl',
    'Business and Commerce Code': 'bc',
    'Business Organizations Code': 'bo',
    'Civil Practice and Remedies Code': 'cp',
    'Code of Criminal Procedure': 'cr',
    'Education Code': 'ed',
    'Election Code': 'el',
    'Estates Code': 'es',
    'Family Code': 'fa',
    'Finance Code': 'fi',
    'Government Code': 'gv',
    'Health and Safety Code': 'hs',
    'Human Resources Code': 'hr',
    'Insurance Code': 'in',
    'Labor Code': 'la',
    'Local Government Code': 'lg',
    'Natural Resources Code': 'nr',
    'Occupations Code': 'oc',
    'Parks and Wildlife Code': 'pw',
    'Penal Code': 'pe',
    'Property Code': 'pr',
    'Special District Local Laws Code': 'sd',
    'Tax Code': 'tx',
    'Transportation Code': 'tn',
    'Utilities Code': 'ut',
    'Water Code': 'wa',
    'Texas Constitution': 'cn',
  };

  static ROMAN_NUMERALS = {
    'I': 1, 'II': 2, 'III': 3, 'IV': 4, 'V': 5, 'VI': 6, 'VII': 7, 'VIII': 8, 'IX': 9, 'X': 10,
    'XI': 11, 'XII': 12, 'XIII': 13, 'XIV': 14, 'XV': 15, 'XVI': 16, 'XVII': 17, 'XVIII': 18, 'XIX': 19, 'XX': 20,
    'XXI': 21, 'XXII': 22, 'XXIII': 23, 'XXIV': 24, 'XXV': 25, 'XXVI': 26, 'XXVII': 27, 'XXVIII': 28, 'XXIX': 29, 'XXX': 30,
    'XXXI': 31, 'XXXII': 32, 'XXXIII': 33, 'XXXIV': 34, 'XXXV': 35, 'XXXVI': 36, 'XXXVII': 37, 'XXXVIII': 38, 'XXXIX': 39, 'XL': 40,
    'XLI': 41, 'XLII': 42, 'XLIII': 43, 'XLIV': 44, 'XLV': 45, 'XLVI': 46, 'XLVII': 47, 'XLVIII': 48, 'XLIX': 49, 'L': 50,
    'LI': 51, 'LII': 52, 'LIII': 53, 'LIV': 54, 'LV': 55, 'LVI': 56, 'LVII': 57, 'LVIII': 58, 'LIX': 59, 'LX': 60,
    'LXI': 61, 'LXII': 62, 'LXIII': 63, 'LXIV': 64, 'LXV': 65, 'LXVI': 66, 'LXVII': 67, 'LXVIII': 68, 'LXIX': 69, 'LXX': 70,
    'LXXI': 71, 'LXXII': 72, 'LXXIII': 73, 'LXXIV': 74, 'LXXV': 75, 'LXXVI': 76, 'LXXVII': 77, 'LXXVIII': 78, 'LXXIX': 79, 'LXXX': 80,
    'LXXXI': 81, 'LXXXII': 82, 'LXXXIII': 83, 'LXXXIV': 84, 'LXXXV': 85, 'LXXXVI': 86, 'LXXXVII': 87, 'LXXXVIII': 88, 'LXXXIX': 89, 'XC': 90,
    'XCI': 91, 'XCII': 92, 'XCIII': 93, 'XCIV': 94, 'XCV': 95, 'XCVI': 96, 'XCVII': 97, 'XCVIII': 98, 'XCIX': 99, 'C': 100,
  };

  constructor() {
    const codeNames = Object.keys(ReferencePatterns.CODE_MAP)
      .map(name => name.replace(/\s+/g, '\\s+'))
      .join('|');

    const romanNumerals = Object.keys(ReferencePatterns.ROMAN_NUMERALS).join('|');

    // Code name patterns
    this.CODE_NAME = new RegExp(`\\s*(${codeNames})`, 'i');

    // Article number patterns
    // Match either Roman numerals or Arabic numerals for article numbers
    this.POSITIVE_INTEGER = /(?<![.\da-zA-Z])(?<!\d-)[1-9]\d*(?![.\da-zA-Z,])/g;
    this.ROMAN_NUMERALS = new RegExp(`\\b(${romanNumerals})\\b`, 'ig');

    // Remove the global flag and improve the pattern structure
    this.ARTICLE_NUMBER = new RegExp(`([1-9]\\d*|\\b(?:${romanNumerals})\\b)`);

    // Enhanced number patterns
    this.SECTION_NUMBER = /(?<!\.)\b(\d+(?:[a-zA-Z])?(?:\.\d+|-[a-zA-Z](?:-\d+)?)*)\b(?!\.|\.\d)/g;
    this.SECTION_NUMBER_WITH_SUBSECTIONS = /\b(\d+[A-Z]?(?:\.\d+)?(?:(?:-[a-z])?(?:-\d+)?)*(?:\([a-z0-9](?:-\d+)?\)(?:\([a-z0-9]+\))*)?)\b(?![\s.]*\.)/g;
    this.CN_SECTION_NUMBER = /(?<!\.|-)\b(\d+[A-Za-z]?(?:-[a-zA-Z0-9]+)*)\b(?!-|\.|\.|\.\d)/g;

    // Subsection patterns
    this.SUBSECTION = /(\([a-z](?:-\d+)?\))/g;
    this.SUBSECTION_WITH_SUBDIVISIONS = /(\([a-z0-9](?:-\d+)?\)(?:\([a-z0-9A-Z]+\))*)/g;
    this.SUBDIVISION = /(\((?!0+\d+)\d+\))/g;
    this.SUBPARAGRAPH = /(\([A-Z]\))/g;

    // Chapter and Title patterns
    this.CHAPTER_NUMBER = /\b(\d+(?:[A-Z]|-\d+\/\d+)?)\b/;
    this.SUBCHAPTER_LETTER = /\b([A-Z](?:-\d+)?)\b/;
    this.TITLE_NUMBER = /\b(\d+(?:[A-Z]|-[A-Z])?)\b/g;
    this.SUBTITLE_LETTER = /\b([A-Z](?:\d+|-\d+)?)\b/g;

    // Connectors and separators
    this.AND_OR_THROUGH = /(?:\s*,\s*|\s+)(?:and|or|through)\s+/;
    this.OPTIONAL_COMMA = /\s*,?\s*/;
    this.COMMA_AND_SPACE = /\s*,\s*/;
    // Ex: " of ", " of the ", " of this "
    this.OF_THE_THIS = /(?:\s+of\s+(?:(?:the|this)\s+)?)/;
    // Ex: " of ", " of the ", " of this ", ", "
    this.COMMA_OF_THE_THIS = /(?:\s*,?\s*|\s+of\s+(?:(?:the|this)\s+)?)/;

    // Common prefixes with word boundaries
    this.CONSTITUTION_PREFIX = /\b(?:[Cc]onstitution(?:\s+[Oo]f\s+[Tt]exas|\s+[Tt]exas)?|[Tt]exas\s+[Cc]onstitution)\b/;
    this.ARTICLE_PREFIX = /\b(?:[Aa]rticles?|[Aa]rt\.)\s+/;
    this.TITLE_PREFIX = /\b[Tt]itles?\s+/;
    this.SUBTITLE_PREFIX = /\b[Ss]ubtitles?\s+/;
    this.CHAPTER_PREFIX = /\b[Cc]hapters?\s+/;
    this.SUBCHAPTER_PREFIX = /\b[Ss]ubchapters?\s+/;
    this.SECTION_PREFIX = /\b(?:[Ss]ections?|[Ss]ec\.)\s+/;
    this.SUBSECTION_PREFIX = /\b[Ss]ubsections?\s+/;
    this.SUBDIVISION_PREFIX = /\b[Ss]ubdivisions?\s+/;
    this.SUBPARAGRAPH_PREFIX = /\b[Ss]ubparagraphs?\s+/;

    // List patterns
    this.LIST_SEPARATOR = /(?:\s*,\s*|\s*,?\s+(?:and|or)\s+)/;
    this.COMMA_SEPARATED_LIST = /(?:\s*,\s*[^,]+)*/;
    this.COMMA_AND_OR_THROUGH = /\s*(?:,\s*|(?:\s+(?:and|or|through)\s+))/;

    // Negative lookahead/behind
    this.NOT_FOLLOWED_BY_CODE = /(?!\s*,?\s*(?:[A-Za-z\s]+\s+Code))/;
  }

  LIST_OF(prefix, pattern) {
    // This pattern captures a list of items with a common prefix
    // For example: "Sections 49-c, 49-d, 49-e and 49-f"
    // We're only capturing the first section with its prefix, and the rest will be handled in buildLink
    return new RegExp(
      `(${prefix.source}\\s*${pattern.source})` +   // Capture first item with prefix and section
      `((?:${this.LIST_SEPARATOR.source}${pattern.source})*)`,  // Capture the rest of the list as a single group
      'gi'  // Global and case insensitive
    );
  }

  LIST_OF_WITH_SUFFIX(prefix, pattern, connector, suffix) {
    // This pattern captures a list that ends with a specific suffix
    // For example: "Sections 7-a, 7-b, Article VIII"
    // We're capturing the first item with prefix, the rest of the list, and the suffix
    return new RegExp(
      `(${prefix.source}\\s*${pattern.source})` +   // Capture first item with prefix and pattern
      `((?:${this.LIST_SEPARATOR.source}${pattern.source})*)` +  // Capture the rest of the list
      `${connector.source}` +  // Connector between list and suffix (e.g., comma)
      `${suffix.source}`,  // Capture the suffix
      'gi'  // Global and case insensitive
    );
  }

  SHARED_BASE_LIST(basePattern, repeatingPattern) {
    // This pattern captures a list where only the first item has the full pattern
    // and subsequent items share the base but only include the repeating part
    // For example: "Section 501.035(b)(7), (8), or (9)"
    return new RegExp(
      `(${basePattern.source})` +                      // Capture the full first item
      `((?:${this.LIST_SEPARATOR.source}${repeatingPattern.source})*)`,  // Capture additional items
      'gi'  // Global and case insensitive
    );
  }

  // Helper method to combine patterns
  combine(...patterns) {
    const combinedPatterns = patterns.map((pattern, index) => {
      if (Array.isArray(pattern)) {
        // For arrays, combine the patterns and wrap in a capture group
        const combinedArray = pattern.map((p, arrayIndex) => {
          if (!p) {
            throw new Error(`Undefined pattern found in array at index ${index}[${arrayIndex}]`);
          }
          return this._getPatternSource(p);
        }).join('');
        return `(${combinedArray})`;
      }
      if (!pattern) {
        throw new Error(`Undefined pattern found at index ${index}`);
      }
      // For non-arrays, just get the pattern source
      return this._getPatternSource(pattern);
    });

    // Join with optional whitespace
    const source = combinedPatterns.join('');
    return new RegExp(source, 'g');
  }

  // Helper method to convert article number to arabic
  getArticleNumber(articleNumber) {
    // If it's already a number, return it as is
    if (typeof articleNumber === 'number') {
      return articleNumber;
    }

    if (typeof articleNumber === 'string') {
      const number = ReferencePatterns.ROMAN_NUMERALS[articleNumber];
      if (number) {
        return number;
      }

      const parsed = parseInt(articleNumber, 10);
      return isNaN(parsed) ? articleNumber : parsed;
    }

    throw new Error(`Invalid article number: ${articleNumber}`);
  }

  // Helper methods
  getCodeMap() {
    return ReferencePatterns.CODE_MAP;
  }

  getCodeFromName(codeName) {
    return ReferencePatterns.CODE_MAP[codeName.trim()];
  }

  isValidCode(codeName) {
    return codeName in ReferencePatterns.CODE_MAP;
  }

  getAllCodes() {
    return Object.keys(ReferencePatterns.CODE_MAP);
  }

  // Helper function to escape special regex characters
  _escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // Helper to get pattern source, preserving capture groups
  _getPatternSource(pattern) {
    if (!pattern) {
      throw new Error('Undefined or null pattern passed to _getPatternSource');
    }
    if (pattern instanceof RegExp) {
      // For RegExp, return source as is to preserve capture groups
      return pattern.source;
    }
    // For strings, escape special characters and wrap in non-capturing group
    // to avoid interfering with capture group numbering
    return `(?:${this._escapeRegExp(pattern)})`;
  }
}
