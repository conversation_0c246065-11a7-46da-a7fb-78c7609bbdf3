import { MockNode, MockStatute, MockConstitution } from '@/lib/testing/mockNodes';
import { TexasCodeReferenceResolver } from './TexasCodeReferenceResolver';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';

// Mock StatuteService.findNodeByStartEndPath
jest.mock('@/components/features/statutes/services/StatuteService', () => {
  return {
    StatuteService: {
      findNodeByStartEndPath: jest.fn(() => {
        // This will be set in each test to use the current rootNode
        return null;
      })
    }
  };
});

describe('TexasCodeReferenceResolver', () => {
    let resolver;
    let mockStatute;

    beforeEach(() => {
        // Create a mock statute for Government Code
        mockStatute = new MockStatute('gv', 'tx');

        // Create chapter 411 for Government Code
        const chapter411 = new MockNode('/collection/tx/code/gv/title/1/chapter/411', 'chapter', 'gv', 'tx');

        // Create section and its subsections for basic tests
        const section087 = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087', 'section', 'gv', 'tx');
        const subsectionA = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)', 'subsection', 'gv', 'tx');
        const subsectionA1 = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(1)', 'subsection', 'gv', 'tx');
        const subsectionA2 = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(2)', 'subsection', 'gv', 'tx');
        const subsectionA3 = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(3)', 'subsection', 'gv', 'tx');
        // Add subsections (b) and (c) for multiple subsection tests
        const section087SubB = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087(b)', 'subsection', 'gv', 'tx');
        const section087SubC = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087(c)', 'subsection', 'gv', 'tx');
        [
            new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.001', 'section', 'gv', 'tx'),
            new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.002', 'section', 'gv', 'tx'),
            new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.003', 'section', 'gv', 'tx')
        ].forEach(node => chapter411.addChild(node));

        // Set up hierarchy
        chapter411.addChild(section087);
        section087.addChild(subsectionA);
        section087.addChild(section087SubB);
        section087.addChild(section087SubC);
        subsectionA.addChild(subsectionA1);
        subsectionA.addChild(subsectionA2);
        subsectionA.addChild(subsectionA3);

        // Create sections for multiple section tests
        const section122 = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.122', 'section', 'gv', 'tx');
        const section1405 = new MockNode('/collection/tx/code/gv/title/1/chapter/411/section/411.1405', 'section', 'gv', 'tx');
        chapter411.addChild(section122);
        chapter411.addChild(section1405);
        mockStatute.titleNode.addChild(chapter411);

        // Create Election Code sections
        const electionCode = new MockStatute('el', 'tx');
        const elChapter501 = new MockNode('/collection/tx/code/el/title/1/chapter/501', 'chapter', 'el', 'tx');
        electionCode.titleNode.addChild(elChapter501);
        const section035 = new MockNode('/collection/tx/code/el/title/1/chapter/501/section/501.035', 'section', 'el', 'tx');
        elChapter501.addChild(section035);
        const subsectionB = new MockNode('/collection/tx/code/el/title/1/chapter/501/section/501.035(b)', 'subsection', 'el', 'tx');
        section035.addChild(subsectionB);
        const subsectionB7 = new MockNode('/collection/tx/code/el/title/1/chapter/501/section/501.035(b)(7)', 'subsection', 'el', 'tx');
        const subsectionB8 = new MockNode('/collection/tx/code/el/title/1/chapter/501/section/501.035(b)(8)', 'subsection', 'el', 'tx');
        const subsectionB9 = new MockNode('/collection/tx/code/el/title/1/chapter/501/section/501.035(b)(9)', 'subsection', 'el', 'tx');
        subsectionB.addChild(subsectionB7);
        subsectionB.addChild(subsectionB8);
        subsectionB.addChild(subsectionB9);
        mockStatute.collectionNode.addChild(electionCode.codeNode);

        // Create Criminal Procedure Code sections
        const crimProCode = new MockStatute('cr', 'tx');
        const crChapter411 = new MockNode('/collection/tx/code/cr/title/1/chapter/411', 'chapter', 'cr', 'tx');
        crimProCode.titleNode.addChild(crChapter411);
        const crSection122 = new MockNode('/collection/tx/code/cr/title/1/chapter/411/section/411.122', 'section', 'cr', 'tx');
        crChapter411.addChild(crSection122);
        const crSection1405 = new MockNode('/collection/tx/code/cr/title/1/chapter/411/section/411.1405', 'section', 'cr', 'tx');
        crChapter411.addChild(crSection1405);
        mockStatute.collectionNode.addChild(crimProCode.codeNode);

        // Create Tax Code subchapter
        const taxCode = new MockStatute('tx', 'tx');
        const taxChapter23 = new MockNode('/collection/tx/code/tx/chapter/23', 'chapter', 'tx', 'tx');
        taxCode.titleNode.addChild(taxChapter23);
        const subchapterC = new MockNode('/collection/tx/code/tx/chapter/23/subchapter/C', 'subchapter', 'tx', 'tx');
        taxChapter23.addChild(subchapterC);
        mockStatute.collectionNode.addChild(taxCode.codeNode);

        // Create Government Code subchapter
        const govChapter404 = new MockNode('/collection/tx/code/gv/title/1/chapter/404', 'chapter', 'gv', 'tx');
        const subchapterF = new MockNode('/collection/tx/code/gv/title/1/chapter/404/subchapter/F', 'subchapter', 'gv', 'tx');
        govChapter404.addChild(subchapterF);
        mockStatute.titleNode.addChild(govChapter404);

        // Create Constitution article
        const constitution = new MockConstitution();
        mockStatute.collectionNode.addChild(constitution.codeNode);

        // Initialize resolver with the Government Code node
        resolver = new TexasCodeReferenceResolver(mockStatute.codeNode);

        // Override isContentNode to return true for testing
        resolver.node.isContentNode = () => true;

        // Set up the mock for StatuteService.findNodeByStartEndPath to use the rootNode
        StatuteService.findNodeByStartEndPath.mockImplementation((startPath, endPath) => {
            return mockStatute.rootNode.findNodeByStartEndPath(startPath, endPath);
        });
    });

    describe('external references', () => {
        describe('section references', () => {
            test('should match basic section reference (Section 411.087, Government Code)', async () => {
                const result = await resolver.processText('Section 411.087, Government Code');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Section 411.087, Government Code',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087',
                    startOffset: 0,
                    endOffset: 32
                });
            });

            test('should match with extra spaces before comma (Section 411.087 , Government Code)', async () => {
                const result = await resolver.processText('Section 411.087 , Government Code');

                expect(result.length).toBe(1);
                expect(result[0].matches[0].text).toBe('Section 411.087 , Government Code');
                expect(result[0].matches[0].target).toBe('/collection/tx/code/gv/title/1/chapter/411/section/411.087');
            });

            test('should match with subsection (Section 411.087(a), Government Code)', async () => {
                const result = await resolver.processText('Section 411.087(a), Government Code');

                expect(result.length).toBe(1);
                expect(result[0].matches[0].text).toBe('Section 411.087(a), Government Code');
                expect(result[0].matches[0].target).toBe('/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)');
            });

            test('should match with subdivision (Section 411.087(a)(1), Government Code)', async () => {
                const result = await resolver.processText('Section 411.087(a)(1), Government Code');

                expect(result.length).toBe(1);
                expect(result[0].matches[0].text).toBe('Section 411.087(a)(1), Government Code');
                expect(result[0].matches[0].target).toBe('/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(1)');
            });
        });

        describe('multiple section references', () => {
            test('should match multiple sections with code (Sections 411.122 and 411.1405, Government Code)', async () => {
                const result = await resolver.processText('Sections 411.122 and 411.1405, Government Code');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Sections 411.122',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.122'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '411.1405',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.1405'
                });
            });

            test('should match multiple sections with Criminal Procedure Code', async () => {
                const result = await resolver.processText('Section 411.122 and 411.1405, Code of Criminal Procedure');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Section 411.122',
                    target: '/collection/tx/code/cr/title/1/chapter/411/section/411.122'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '411.1405',
                    target: '/collection/tx/code/cr/title/1/chapter/411/section/411.1405'
                });
            });
        });

        describe('section list references', () => {
            test('should match section list with subsections (Section 501.035(b)(7), (8), or (9), Election Code)', async () => {
                const result = await resolver.processText('Section 501.035(b)(7), (8), or (9), Election Code');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(3);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Section 501.035(b)(7)',
                    target: '/collection/tx/code/el/title/1/chapter/501/section/501.035(b)(7)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(8)',
                    target: '/collection/tx/code/el/title/1/chapter/501/section/501.035(b)(8)'
                });
                expect(result[0].matches[2]).toMatchObject({
                    text: '(9)',
                    target: '/collection/tx/code/el/title/1/chapter/501/section/501.035(b)(9)'
                });
            });
        });

        describe('constitution references', () => {
            test('should match constitution reference (Article I, Section 1, Texas Constitution)', async () => {
                const result = await resolver.processText('Article I, Section 1, Texas Constitution');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Article I, Section 1, Texas Constitution',
                    target: '/collection/tx/code/cn/article/1/section/1'
                });
            });

            test('should match constitution reference with subarticle', async () => {
                const result = await resolver.processText('Article I, Section 2, Texas Constitution');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Article I, Section 2, Texas Constitution',
                    target: '/collection/tx/code/cn/article/1/subarticle/1-1/section/2'
                });
            });
        });

        describe('subchapter references', () => {
            test('should match subchapter with chapter and code (Subchapter C, Chapter 23, Tax Code)', async () => {
                const result = await resolver.processText('Subchapter C, Chapter 23, Tax Code');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subchapter C, Chapter 23, Tax Code',
                    target: '/collection/tx/code/tx/chapter/23/subchapter/C'
                });
            });

            test('should match subchapter with chapter and Government Code', async () => {
                const result = await resolver.processText('Subchapter F, Chapter 404, Government Code');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subchapter F, Chapter 404, Government Code',
                    target: '/collection/tx/code/gv/title/1/chapter/404/subchapter/F'
                });
            });
        });
    });

    describe('internal references', () => {
        describe('section references', () => {
            beforeEach(() => {
                // Initialize resolver with the section node
                const code = mockStatute.rootNode.findNode('/collection/tx/code/gv');
                resolver = new TexasCodeReferenceResolver(code);
            });
            test('should match single section (Section 411.001)', async () => {
                const result = await resolver.processText('Section 411.001');

                expect(result).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Section 411.001',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.001'
                });
            });

            test('should match multiple sections (Sections 411.001 and 411.002)', async () => {
                const result = await resolver.processText('Sections 411.001 and 411.002');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Sections 411.001',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.001'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '411.002',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.002'
                });
            });

            test('should match section list (Sections 411.001, 411.002, and 411.003)', async () => {
                const result = await resolver.processText('Sections 411.001, 411.002, and 411.003');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(3);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Sections 411.001',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.001'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '411.002',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.002'
                });
                expect(result[0].matches[2]).toMatchObject({
                    text: '411.003',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.003'
                });
            });
        });

        describe('subsection references', () => {
            beforeEach(() => {
                // Initialize resolver with the section node
                const section = mockStatute.rootNode.findNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087');
                resolver = new TexasCodeReferenceResolver(section);
                resolver.rootNode = mockStatute.rootNode;

                // Override isContentNode to return true for testing
                resolver.node.isContentNode = () => true;
            });

            test('should match single subsection reference (Subsection (a))', async () => {
                const result = await resolver.processText('Subsection (a)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(1);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (a)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)'
                });
            });

            test('should match multiple subsections (Subsections (a) and (b))', async () => {
                const result = await resolver.processText('Subsections (a) and (b)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsections (a)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(b)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(b)'
                });
            });

            test('should match subsection list (Subsections (a), (b), and (c))', async () => {
                const result = await resolver.processText('Subsections (a), (b), and (c)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(3);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsections (a)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(b)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(b)'
                });
                expect(result[0].matches[2]).toMatchObject({
                    text: '(c)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(c)'
                });
            });
        });

        describe('subdivisions references', () => {
            beforeEach(() => {
                const section = mockStatute.rootNode.findNode('/collection/tx/code/gv/title/1/chapter/411/section/411.087');
                resolver = new TexasCodeReferenceResolver(section);
                resolver.rootNode = mockStatute.rootNode;

                // Override isContentNode to return true for testing
                resolver.node.isContentNode = () => true;
            });

            test('should match subdivisions with shared parent (Subsection (a)(1) or (2))', async () => {
                const result = await resolver.processText('Subsection (a)(1) or (2)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(2);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (a)(1)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(1)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(2)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(2)'
                });
            });

            test('should match with shared parent list (Subsection (a)(1), (2), or (3))', async () => {
                const result = await resolver.processText('Subsection (a)(1), (2), or (3)');

                expect(result).toHaveLength(1);
                expect(result[0].matches).toHaveLength(3);
                expect(result[0].matches[0]).toMatchObject({
                    text: 'Subsection (a)(1)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(1)'
                });
                expect(result[0].matches[1]).toMatchObject({
                    text: '(2)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(2)'
                });
                expect(result[0].matches[2]).toMatchObject({
                    text: '(3)',
                    target: '/collection/tx/code/gv/title/1/chapter/411/section/411.087(a)(3)'
                });
            });
        });
    });
});
