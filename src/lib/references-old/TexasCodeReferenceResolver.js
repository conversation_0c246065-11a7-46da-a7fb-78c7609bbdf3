import { BaseReferenceResolver } from './BaseReferenceResolver';
import { ReferencePatterns } from './ReferencePatterns';
import { StatuteService } from '@/components/features/statutes/services/StatuteService';

export class TexasCodeReferenceResolver extends BaseReferenceResolver {
  constructor(node) {
    super(node);
    this.initializePatterns();
  }

  initializePatterns() {
    // Patterns ordered from most specific (most parts) to least specific (least parts)
    this.patterns = [
      // External section (without subsections)
      // Example: Section 411.087, Government Code
      {
        name: 'external_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.SECTION_NUMBER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, section, codeName, offset) => {
          let targetNode;
          const code = this.referencePatterns.getCodeFromName(codeName);
          if (code) {
            const startPath = `/collection/${this.node.collection}/code/${code}`;
            const endPath = `/section/${section}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External section with subsection
      // Example: Section 411.087(a), Government Code
      {
        name: 'external_section_with_subsection',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.SECTION_NUMBER,
          this.referencePatterns.SUBSECTION,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, section, subsection, codeName, offset) => {
          let targetNode;
          const code = this.referencePatterns.getCodeFromName(codeName);
          if (code) {
            const startPath = `/collection/${this.node.collection}/code/${code}`;
            const endPath = `/section/${section}${subsection}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External section list
      // Example: Sections 411.087 and 411.088, Government Code
      {
        name: 'external_section_list',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.LIST_OF(
            this.referencePatterns.SECTION_PREFIX,
            this.referencePatterns.SECTION_NUMBER
          ),
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, firstRef, section1, restOfRefs, lastSection, codeName, offset) => {
          const code = this.referencePatterns.getCodeFromName(codeName);
          if (!code) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                status: "unresolved",
                startOffset: offset,
                endOffset: offset + matchedText.length
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${code}`;
          const endPath1 = `/section/${section1}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);

          const matches = [];
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          const sectionMatches = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SECTION_NUMBER)] : [];
          if (!sectionMatches || sectionMatches?.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Process all sections with a single approach
          let currentPosition = offset + matchedText.indexOf(restOfRefs);

          for (const section of sectionMatches) {
            const sectionNumber = section[0];
            const endPath = `/section/${sectionNumber}`;
            const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

            // Find position in original text
            const sectionPos = currentPosition + section.index;

            // TODO: add the code name to the last match text. text would be start of last section to end of codeName
            matches.push({
              text: sectionNumber,
              target: targetNode?.nodeId,
              startOffset: offset + sectionPos,
              endOffset: offset + sectionPos + sectionNumber.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });

            // Update current position
            currentPosition = sectionPos + sectionNumber.length;
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Internal section
      // Example: Section 411.087
      {
        name: 'internal_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.SECTION_NUMBER
        ),
        buildLink: async (matchedText, section, offset) => {
          let targetNode;
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/section/${section}`;
          targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External section list with shared base section number
      // Example: Section 501.035(b)(7), (8), or (9), Election Code
      {
        name: 'external_section_list_with_shared_base_section_number',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SHARED_BASE_LIST(
            this.referencePatterns.combine(
              this.referencePatterns.SECTION_PREFIX,
              this.referencePatterns.SECTION_NUMBER,
              this.referencePatterns.SUBSECTION,
              this.referencePatterns.SUBDIVISION
            ),
            this.referencePatterns.SUBDIVISION
          ),
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, firstRef, section, subsection, subdivision1, restOfRefs, lastSubdivision, codeName, offset) => {
          console.log('External section list with shared base section number match:', {
            matchedText,
            firstRef,
            section,
            subsection,
            subdivision1,
            restOfRefs,
            lastSubdivision,
            codeName,
            offset
          });

          const code = this.referencePatterns.getCodeFromName(codeName);
          console.log('external_section_list_with_shared_base_section_number Code:', code);
          if (!code) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                status: "unresolved",
                startOffset: offset,
                endOffset: offset + matchedText.length
              }]
            };
          }

          const matches = [];

          // 1. Handle the first match - we already have all parts
          const startPath = `/collection/${this.node.collection}/code/${code}`;
          const fullFirstSection = `${section}${subsection}${subdivision1}`;
          const endPath1 = `/section/${fullFirstSection}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          console.log('Target node for first section:', {
            startPath,
            endPath1,
            targetNode1: targetNode1?.nodeId
          });

          // Create the first match
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          // 2. Check for additional subdivisions
          // Use a more direct regex to match the subdivision numbers
          const additionalMatches = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SUBDIVISION)] : [];

          // If there are no additional matches, return early with just the first match
          if (!additionalMatches || additionalMatches.length === 0) {
            console.log('No additional matches found');
            console.log('Matches:', matches);
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Calculate the starting position of restOfRefs within the full matchedText
          let currentPosition = offset + matchedText.indexOf(restOfRefs);

          // 3. Process additional subdivisions
          for (const match of additionalMatches) {
            const subdivision = match[0]; // Get the captured subdivision
            const fullSection = `${section}${subsection}${subdivision}`;
            const endPath = `/section/${fullSection}`;
            const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
            console.log('Target node for subdivision:', {
              startPath,
              endPath,
              targetNode: targetNode?.nodeId
            });
            const subdivisionPos = currentPosition + match.index;

            // Add this match to our results
            matches.push({
              text: subdivision,
              target: targetNode?.nodeId,
              startOffset: subdivisionPos,
              endOffset: subdivisionPos + subdivision.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
          }

          console.log('Final matches array:', matches);
          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // External section with optional subsections
      // Example: Section 411.087(a)(1), Government Code
      {
        name: 'external_section_with_subsections',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX,
          this.referencePatterns.SECTION_NUMBER,
          this.referencePatterns.SUBSECTION,
          this.referencePatterns.SUBDIVISION,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, section, subsection, subdivision, codeName, offset) => {
          const code = this.referencePatterns.getCodeFromName(codeName);
          if (!code) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                status: "unresolved",
                startOffset: offset,
                endOffset: offset + matchedText.length
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${code}`;
          const sectionPath = `${section}${(subsection || '')}${(subdivision || '')}`;
          const endPath = `/section/${sectionPath}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          let matches = [];
          matches.push({
            text: matchedText,
            target: targetNode?.nodeId,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            ...(!targetNode ? { status: "unresolved" } : {})
          });

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Multiple external sections
      // Example: Sections 411.122 and 411.1405, Government Code
      {
        name: 'multiple_external_sections',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SECTION_PREFIX, this.referencePatterns.SECTION_NUMBER],
          this.referencePatterns.AND_OR_THROUGH,
          this.referencePatterns.COMMA_OF_THE_THIS,
          [this.referencePatterns.SECTION_NUMBER, this.referencePatterns.CODE_NAME]
        ),
        buildLink: async (matchedText, firstRef, section1, secondRef, section2, codeName, offset) => {
          let targetNode1, targetNode2;
          const code = ReferencePatterns.CODE_MAP[codeName.trim()];
          if (code) {
            const startPath = `/collection/tx/code/${code}`;
            const endPath1 = `/section/${section1}`;
            const endPath2 = `/section/${section2}`;
            targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
            targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, endPath2);
          }

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.indexOf(secondRef);
          const secondRefEnd = secondRefStart + secondRef.length;

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset + firstRefStart,
                endOffset: offset + firstRefEnd,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: secondRef,
                target: targetNode2?.nodeId,
                startOffset: offset + secondRefStart,
                endOffset: offset + secondRefEnd,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // External constitution section
      // Example: Article I, Section 1, Texas Constitution
      {
        name: 'external_constitution_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.ARTICLE_PREFIX, this.referencePatterns.ARTICLE_NUMBER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.SECTION_PREFIX, this.referencePatterns.SECTION_NUMBER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CONSTITUTION_PREFIX
        ),
        buildLink: async (matchedText, article, section, offset) => {
          let targetNode;

          const startPath = '/collection/tx/code/cn';
          const articleNumber = this.referencePatterns.getArticleNumber(article);
          let endPath = `/article/${articleNumber}/section/${section}`;
          targetNode = StatuteService.findNodeByStartEndPath(startPath, endPath);

          // If not found, try looking through subarticles
          if (!targetNode) {
            const articleNode = await StatuteService.findNodeByStartEndPath(startPath, `/article/${articleNumber}`);
            if (articleNode) {
              // Get all subarticles
              const subarticles = articleNode.children.filter(n => n.isSubarticle());

              // Try each subarticle path
              for (const subarticle of subarticles) {
                endPath = `/article/${articleNumber}/subarticle/${subarticle.id}/section/${section}`;
                targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
                if (targetNode) break;
              }
            }
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External subchapter, chapter, and code reference
      // Example: Subchapter C, Chapter 23, Tax Code
      {
        name: 'external_subchapter_chapter_code',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBCHAPTER_PREFIX, this.referencePatterns.SUBCHAPTER_LETTER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CHAPTER_PREFIX, this.referencePatterns.CHAPTER_NUMBER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, subchapter, chapter, codeName, offset) => {
          let targetNode;
          const code = ReferencePatterns.CODE_MAP[codeName.trim()];
          if (code) {
            const startPath = `/collection/tx/code/${code}`;
            const endPath = `/chapter/${chapter}/subchapter/${subchapter}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Multiple internal subchapters with chapter reference
      // Example: Subchapters C and D, Chapter 102
      // Example: Subchapter C or D, Chapter 102
      // Example: Subchapter C through F, Chapter 102
      {
        name: 'multiple_internal_subchapters_with_chapter',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SUBCHAPTER_PREFIX, this.referencePatterns.SUBCHAPTER_LETTER],
          this.referencePatterns.AND_OR_THROUGH,
          [this.referencePatterns.SUBCHAPTER_LETTER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CHAPTER_PREFIX, this.referencePatterns.CHAPTER_NUMBER]
        ),
        buildLink: async (matchedText, firstRef, subchapter1, secondRef,subchapter2, chapter, offset) => {
          let targetNode1, targetNode2;

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/chapter/${chapter}/subchapter/${subchapter1}`;
          const endPath2 = `/chapter/${chapter}/subchapter/${subchapter2}`;
          targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, endPath2);

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.indexOf(secondRef);
          const secondRefEnd = secondRefStart + secondRef.length;

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset + firstRefStart,
                endOffset: offset + firstRefEnd,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: secondRef,
                target: targetNode2?.nodeId,
                startOffset: offset + secondRefStart,
                endOffset: offset + secondRefEnd,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // Multiple internal subchapter references
      // Example: Subchapter A or B
      // Example: Subchapters A and B
      // Example: Subchapters A through D
      {
        name: 'multiple_internal_subchapters',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SUBCHAPTER_PREFIX, this.referencePatterns.SUBCHAPTER_LETTER],
          this.referencePatterns.AND_OR_THROUGH,
          this.referencePatterns.SUBCHAPTER_LETTER
        ),
        buildLink: async (matchedText, firstRef, subchapter1, subchapter2, offset) => {
          let targetNode1, targetNode2;

          const chapterNode = this.node.getHierarchy().find(n => n.isChapter());
          if (chapterNode) {
            const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
            const endPath1 = `/chapter/${chapterNode.id}/subchapter/${subchapter1}`;
            const endPath2 = `/chapter/${chapterNode.id}/subchapter/${subchapter2}`;
            targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
            targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, endPath2);
          }

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.lastIndexOf(subchapter2);
          const secondRefEnd = secondRefStart + subchapter2.length;

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset + firstRefStart,
                endOffset: offset + firstRefEnd,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: subchapter2,
                target: targetNode2?.nodeId,
                startOffset: offset + secondRefStart,
                endOffset: offset + secondRefEnd,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // Internal subchapter chapter reference
      // Example: Subchapter A, Chapter 5
      {
        name: 'internal_subchapter_chapter',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBCHAPTER_PREFIX, this.referencePatterns.SUBCHAPTER_LETTER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CHAPTER_PREFIX, this.referencePatterns.CHAPTER_NUMBER
        ),
        buildLink: async (matchedText, subchapter, chapter, offset) => {
          let targetNode;

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/chapter/${chapter}/subchapter/${subchapter}`;
          targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal subchapter reference
      // Example: Subchapter A
      {
        name: 'internal_subchapter',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBCHAPTER_PREFIX, this.referencePatterns.SUBCHAPTER_LETTER
        ),
        buildLink: async (matchedText, subchapter, offset) => {
          let targetNode;

          const chapterNode = this.node.getHierarchy().find(n => n.isChapter());
          if (chapterNode) {
            const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
            const endPath = `/chapter/${chapterNode.id}/subchapter/${subchapter}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External chapter code reference
      // Example: Chapter 1, Agriculture Code
      // Example: Chapter 311, Government Code
      // Example: Chapter 411 of the Government Code
      {
        name: 'external_chapter_code',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.CHAPTER_PREFIX, this.referencePatterns.CHAPTER_NUMBER,
          this.referencePatterns.COMMA_OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, chapter, codeName, offset) => {
          let targetNode;

          const code = ReferencePatterns.CODE_MAP[codeName.trim()];
          if (code) {
            const startPath = `/collection/${this.node.collection}/code/${code}`;
            const endPath = `/chapter/${chapter}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal chapter reference
      // Example: Chapter 1
      {
        name: 'internal_chapter',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.CHAPTER_PREFIX, this.referencePatterns.CHAPTER_NUMBER
        ),
        buildLink: async (matchedText, chapter, offset) => {
          let targetNode;

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/chapter/${chapter}`;
          targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Shared parent subsections list references
      // Example: Subsection (a)(1) or (2)
      // Example: Subsection (a)(1), (2), or (3)
      {
        name: 'internal_subsections_with_shared_parent_list',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SHARED_BASE_LIST(
            this.referencePatterns.combine(
              this.referencePatterns.SUBSECTION_PREFIX,
              this.referencePatterns.SUBSECTION,
              this.referencePatterns.SUBDIVISION
            ),
            this.referencePatterns.SUBDIVISION
          ),
        ),
        buildLink: async (matchedText, prefixAndSubsection, subsection, subdivision1, restOfRefs, lastSubdivision, offset) => {
          const matches = [];

          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                target: undefined,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: "unresolved"
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/section/${contextNode.id}${subsection}${subdivision1}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          matches.push({
            text: `${prefixAndSubsection}`,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + prefixAndSubsection.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          const subdivisions = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SUBDIVISION)] : [];
          if (!subdivisions || subdivisions.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Initialize current position tracker
          let currentPosition = offset + matchedText.indexOf(restOfRefs);

          // Process all subdivisions after the first one
          for (const match of subdivisions) {
            const subdivision = match[0];
            const endPath = `/section/${contextNode.id}${subsection}${subdivision}`;
            const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

            // Find position in original text starting from current position
            const subdivisionPos = currentPosition + match.index;

            matches.push({
              text: subdivision,
              target: targetNode?.nodeId,
              startOffset: offset + subdivisionPos,
              endOffset: offset + subdivisionPos + subdivision.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });

            // Update current position for next search
            currentPosition = subdivisionPos + subdivision.length;
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Internal subsection list
      // Example: Subsections (a), (b), and (c)
      // Example: Subsection (a), (b), or (c)
      {
        name: 'internal_subsection_list',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.LIST_OF(
            this.referencePatterns.SUBSECTION_PREFIX,
            this.referencePatterns.SUBSECTION_WITH_SUBDIVISIONS
          )
        ),
        buildLink: async (matchedText, firstRef, subsection1, restOfRefs, lastSubsection, offset) => {
          console.log('Internal subsection list match:', {
            matchedText,
            firstRef,
            subsection1,
            restOfRefs,
            lastSubsection,
            offset
          });

          const matches = [];

          // Get the current section context from the node hierarchy
          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          console.log('internal_subsection_list Context node:', contextNode?.id);
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                target: undefined,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: "unresolved"
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/section/${contextNode.id}${subsection1}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          const restOfSubsections = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SUBSECTION_WITH_SUBDIVISIONS)] : [];
          if (!restOfSubsections || restOfSubsections.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Initialize current position tracker
          let currentPosition = offset + matchedText.indexOf(restOfRefs);

          // Process all subsections
          for (const match of restOfSubsections) {
            const subsection = match[0];
            const endPath = `/section/${contextNode.id}${subsection}`;
            const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
            console.log('Internal subsection list target node:', {
              startPath,
              endPath,
              targetNode: targetNode?.nodeId
            });

            // Find position in original text starting from current position
            const subsectionPos = currentPosition + match.index;

            matches.push({
              text: subsection,
              target: targetNode?.nodeId,
              startOffset: offset + subsectionPos,
              endOffset: offset + subsectionPos + subsection.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });

            // Update current position for next search
            currentPosition = subsectionPos + subsection.length;
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Multiple internal complex subsection references with full paths
      // Example: Subsection (a-1)(3)(A)(ii) or (a-1)(3)(B)(ii)
      {
        name: 'multiple_internal_complex_subsections',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SUBSECTION_PREFIX, this.referencePatterns.SUBSECTION_WITH_SUBDIVISIONS],
          this.referencePatterns.AND_OR_THROUGH,
          this.referencePatterns.SUBSECTION_WITH_SUBDIVISIONS
        ),
        buildLink: async (matchedText, firstRef, subsection1, subsection2, offset) => {
          let targetNode1, targetNode2;

          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                status: "unresolved",
                startOffset: offset,
                endOffset: offset + matchedText.length
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/section/${contextNode.id}${subsection1}`;
          const endPath2 = `/section/${contextNode.id}${subsection2}`;
          targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, endPath2);

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.indexOf(subsection2);
          const secondRefEnd = secondRefStart + subsection2.length;

          const matches = [
            {
              text: firstRef,
              target: targetNode1?.nodeId,
              startOffset: offset + firstRefStart,
              endOffset: offset + firstRefEnd,
              ...(!targetNode1 ? { status: "unresolved" } : {})
            },
            {
              text: subsection2,
              target: targetNode2?.nodeId,
              startOffset: offset + secondRefStart,
              endOffset: offset + secondRefEnd,
              ...(!targetNode2 ? { status: "unresolved" } : {})
            }
          ];

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Multiple subsection references
      // Example: Subsection (a) or (b)
      // Example: Subsections (a) and (b)
      // Example: Subsections (a) through (d)
      {
        name: 'internal_multiple_subsections',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SUBSECTION_PREFIX, this.referencePatterns.SUBSECTION],
          this.referencePatterns.AND_OR_THROUGH,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, firstRef, subsection1, subsection2, offset) => {
          let targetNode1, targetNode2;

          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                target: undefined,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: "unresolved"
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/section/${contextNode.id}${subsection1}`;
          const endPath2 = `/section/${contextNode.id}${subsection2}`;
          targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, endPath2);

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.lastIndexOf(subsection2);
          const secondRefEnd = secondRefStart + subsection2.length;

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset + firstRefStart,
                endOffset: offset + firstRefEnd,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: subsection2,
                target: targetNode2?.nodeId,
                startOffset: offset + secondRefStart,
                endOffset: offset + secondRefEnd,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // Internal subsection with subdivision references
      // Example: Subsection (a)(1)
      // Example: Subsection (a)(1)(A)(i)
      {
        name: 'internal_subsection_with_subdivisions',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBSECTION_PREFIX,
          this.referencePatterns.SUBSECTION_WITH_SUBDIVISIONS
        ),
        buildLink: async (matchedText, subsection, offset) => {
          let targetNode;

          // Get the current section context from the node hierarchy
          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          if (contextNode) {
            const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
            const endPath = `/section/${contextNode.id}${subsection}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal single subsection reference
      // Example: Subsection (a)
      {
        name: 'internal_single_subsection',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBSECTION_PREFIX,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, subsection, offset) => {
          const contextNode = this.node.getHierarchy().find(n => n.isSection());
          if (!contextNode) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches: [{
                text: matchedText,
                target: undefined,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                status: "unresolved"
              }]
            };
          }

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/section/${contextNode.id}${subsection}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          }
        }
      },

      // Multiple internal subsections within same section
      // Example: Sections 11.39(a) and (b)
      {
        name: 'multiple_internal_subsections_within_same_section',
        pattern: this.referencePatterns.combine(
          [this.referencePatterns.SECTION_PREFIX, this.referencePatterns.SECTION_NUMBER],
          this.referencePatterns.AND_OR_THROUGH,
          this.referencePatterns.SUBSECTION
        ),
        buildLink: async (matchedText, firstRef, section, firstSubsection, secondSubsection, offset) => {
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, `/section/${section}${firstSubsection}`);
          const targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, `/section/${section}${secondSubsection}`);

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.indexOf(secondSubsection);
          const secondRefEnd = secondRefStart + secondSubsection.length;

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset + firstRefStart,
                endOffset: offset + firstRefEnd,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: secondSubsection,
                target: targetNode2?.nodeId,
                startOffset: offset + secondRefStart,
                endOffset: offset + secondRefEnd,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      },

      // Multiple internal sections list
      // Example: Sections 11.38 and 61.36
      // Example: Section 11.67 or 32.18
      // Example: Section 22.06, 24.05, or 102.05
      // Example: Sections 22.06, 24.05, and 102.05
      // Example: Sections 11.46 through 11.48
      {
        name: 'multiple_internal_sections_list',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.LIST_OF(
            this.referencePatterns.SECTION_PREFIX,
            this.referencePatterns.SECTION_NUMBER
          ),
        ),
        buildLink: async (matchedText, firstRef, section1, restOfRefs, lastSection, offset) => {
          const matches = [];

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/section/${section1}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);

          matches.push({
            text: firstRef,
            target: targetNode1?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode1 ? { status: "unresolved" } : {})
          });

          const restOfSections = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.SECTION_NUMBER)]: [];
          if (!restOfSections || restOfSections.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          let currentPosition = offset + matchedText.indexOf(restOfRefs);
          for (const match of restOfSections) {
            const sectionNumber = match[0];
            const sectionPos = currentPosition + match.index;
            const endPath = `/section/${sectionNumber}`;
            const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

            matches.push({
              text: sectionNumber,
              target: targetNode?.nodeId,
              startOffset: offset + sectionPos,
              endOffset: offset + sectionPos + sectionNumber.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });
            currentPosition = sectionPos + sectionNumber.length;
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Internal section with subsections
      // Example: Section 6.01(b)
      // Example: Section 6.01 (b)(1)
      // Example: Section 6.01(b)(1)(A)(i)
      {
        name: 'internal_section_with_subsections',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX, this.referencePatterns.SECTION_NUMBER,
          this.referencePatterns.SUBSECTION_WITH_SUBDIVISIONS
        ),
        buildLink: async (matchedText, section, subsections, offset) => {
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/section/${section}${subsections.replace(/\s+/g, '')}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal section reference
      // Example: Section 134.001
      // Example: Section 20A.1102
      // Example: Section 251.19
      // Example: Section 251.1234567
      {
        name: 'internal_section',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SECTION_PREFIX, this.referencePatterns.SECTION_NUMBER
        ),
        buildLink: async (matchedText, section, offset) => {
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/section/${section}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External title code reference
      // Example: Title 3, Government Code
      // Example: Title 3 of the Government Code
      {
        name: 'external_title_code',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.TITLE_PREFIX, this.referencePatterns.TITLE_NUMBER,
          this.referencePatterns.OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, title, codeName, offset) => {
          let targetNode;

          const code = ReferencePatterns.CODE_MAP[codeName.trim()];
          if (code) {
            const startPath = `/collection/${this.node.collection}/code/${code}`;
            const endPath = `/title/${title}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External subtitle title code reference
      // Example: Subtitle A, Title 3, Government Code
      {
        name: 'external_subtitle_title_code',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBTITLE_PREFIX, this.referencePatterns.SUBTITLE_LETTER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.TITLE_PREFIX, this.referencePatterns.TITLE_NUMBER,
          this.referencePatterns.OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, subtitle, title, codeName, offset) => {
          let targetNode;

          const code = codeName ? ReferencePatterns.CODE_MAP[codeName.trim()] : null;
          if (code) {
            const startPath = `/collection/${this.node.collection}/code/${code}`;
            const endPath = `/title/${title}/subtitle/${subtitle}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal subtitle title reference
      // Example: Subtitle A, Title 3
      {
        name: 'internal_subtitle_title',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.SUBTITLE_PREFIX, this.referencePatterns.SUBTITLE_LETTER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.TITLE_PREFIX, this.referencePatterns.TITLE_NUMBER
        ),
        buildLink: async (matchedText, subtitle, title, offset) => {
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/title/${title}/subtitle/${subtitle}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // External title subtitle code reference
      // Example: Title 3, Subtitle A, Government Code
      {
        name: 'external_title_subtitle_code',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.TITLE_PREFIX, this.referencePatterns.TITLE_NUMBER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.SUBTITLE_PREFIX, this.referencePatterns.SUBTITLE_LETTER,
          this.referencePatterns.OF_THE_THIS,
          this.referencePatterns.CODE_NAME
        ),
        buildLink: async (matchedText, title, subtitle, codeName, offset) => {
          let targetNode;

          const code = codeName ? this.referencePatterns.getCodeFromName(codeName) : null;
          if (code) {
            const startPath = `/collection/${this.node.collection}/code/${code}`;
            const endPath = `/title/${title}/subtitle/${subtitle}`;
            targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal title subtitle reference
      // Example: Title 3, Subtitle A
      {
        name: 'internal_title_subtitle',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.TITLE_PREFIX, this.referencePatterns.TITLE_NUMBER,
          this.referencePatterns.COMMA_AND_SPACE,
          this.referencePatterns.SUBTITLE_PREFIX, this.referencePatterns.SUBTITLE_LETTER
        ),
        buildLink: async (matchedText, title, subtitle, offset) => {
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/title/${title}/subtitle/${subtitle}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal title reference
      // Example: Title 3
      {
        name: 'internal_title',
        pattern: this.referencePatterns.combine(
          this.referencePatterns.TITLE_PREFIX, this.referencePatterns.TITLE_NUMBER
        ),
        buildLink: async (matchedText, title, offset) => {
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/title/${title}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [{
              text: matchedText,
              target: targetNode?.nodeId,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            }]
          };
        }
      },

      // Internal chapter list
      // Example: Chapters 25, 26, 28, 30, 32, 48, 50, 69, 71, and 74
      // Example: Chapters 28, 30, and 32
      // Example: Chapter 28, 30, or 32
      // Example: Chapters 28 and 30
      {
        name: 'internal_chapter_list',
        pattern: this.referencePatterns.LIST_OF(
          this.referencePatterns.CHAPTER_PREFIX, this.referencePatterns.CHAPTER_NUMBER
        ),
        buildLink: async (matchedText, firstRef, chapter1, restOfRefs, lastChapter, offset) => {
          const matches = [];

          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath = `/chapter/${chapter1}`;
          const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);

          matches.push({
            text: firstRef,
            target: targetNode?.nodeId,
            startOffset: offset,
            endOffset: offset + firstRef.length,
            ...(!targetNode ? { status: "unresolved" } : {})
          });

          const chapters = restOfRefs ? [...restOfRefs.matchAll(this.referencePatterns.CHAPTER_NUMBER)] : [];
          if (!chapters || chapters.length === 0) {
            return {
              text: matchedText,
              startOffset: offset,
              endOffset: offset + matchedText.length,
              matches
            };
          }

          // Handle remaining chapters
          let currentPosition = offset + matchedText.indexOf(restOfRefs);
          for (const match of chapters) {
            const chapter = match[0];
            const endPath = `/chapter/${chapter}`;
            const targetNode = await StatuteService.findNodeByStartEndPath(startPath, endPath);
            const chapterPos = currentPosition + match.index;
            matches.push({
              text: chapter,
              target: targetNode?.nodeId,
              startOffset: offset + chapterPos,
              endOffset: offset + chapterPos + chapter.length,
              ...(!targetNode ? { status: "unresolved" } : {})
            });

            currentPosition = chapterPos + chapter.length;
          }

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches
          };
        }
      },

      // Multiple internal sections
      // Example: Sections 411.001 and 411.002
      {
        name: 'multiple_internal_sections',
        pattern: this.referencePatterns.combine(
            [this.referencePatterns.SECTION_PREFIX, this.referencePatterns.SECTION_NUMBER],
            this.referencePatterns.AND_OR_THROUGH,
            this.referencePatterns.SECTION_NUMBER
        ),
        buildLink: async (matchedText, firstRef, section1, section2, offset) => {
          console.log('multiple_internal_sections', {
            matchedText,
            firstRef,
            section1,
            section2,
            offset
          });

          let currentOffset = offset;

          // Process first section
          const startPath = `/collection/${this.node.collection}/code/${this.node.code}`;
          const endPath1 = `/section/${section1}`;
          const endPath2 = `/section/${section2}`;
          const targetNode1 = await StatuteService.findNodeByStartEndPath(startPath, endPath1);
          const targetNode2 = await StatuteService.findNodeByStartEndPath(startPath, endPath2);

          // Use the position of matches in the original text for offset calculations
          const firstRefStart = 0;
          const firstRefEnd = firstRef.length;
          const secondRefStart = matchedText.indexOf(section2, currentOffset);
          const secondRefEnd = secondRefStart + section2.length;

          return {
            text: matchedText,
            startOffset: offset,
            endOffset: offset + matchedText.length,
            matches: [
              {
                text: firstRef,
                target: targetNode1?.nodeId,
                startOffset: offset + firstRefStart,
                endOffset: offset + firstRefEnd,
                ...(!targetNode1 ? { status: "unresolved" } : {})
              },
              {
                text: section2,
                target: targetNode2?.nodeId,
                startOffset: offset + secondRefStart,
                endOffset: offset + secondRefEnd,
                ...(!targetNode2 ? { status: "unresolved" } : {})
              }
            ]
          };
        }
      }
    ];
  }
}
