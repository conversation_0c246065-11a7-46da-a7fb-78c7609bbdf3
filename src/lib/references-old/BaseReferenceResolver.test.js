import { BaseReferenceResolver } from './BaseReferenceResolver';
import { ReferencePatterns } from './ReferencePatterns';
import { MockNode } from '@/lib/testing/mockNodes';

describe('BaseReferenceResolver', () => {
    let resolver;
    let mockNode;
    let rootNode;

    beforeEach(() => {
        mockNode = new MockNode('/collection/tx/code/gv/section/1/subsection/a', 'subsection', 'gv', 'tx');
        rootNode = mockNode.getRootNode();
        resolver = new BaseReferenceResolver(mockNode);
        resolver.rootNode = rootNode;
    });

    describe('initialization', () => {
        test('should initialize with a node', () => {
            expect(resolver.node).toBeDefined();
            expect(resolver.node).toBe(mockNode);
        });

        test('should initialize with a root node', () => {
            expect(resolver.rootNode).toBeDefined();
            expect(resolver.rootNode).toBe(rootNode);
        });

        test('should initialize with reference patterns', () => {
            expect(resolver.referencePatterns).toBeDefined();
            expect(resolver.referencePatterns).toBeInstanceOf(ReferencePatterns);
        });

        test('should initialize with empty patterns array', () => {
            expect(resolver.patterns).toBeDefined();
            expect(Array.isArray(resolver.patterns)).toBe(true);
            expect(resolver.patterns).toHaveLength(0);
        });
    });

    describe('addPattern', () => {
        test('should add a new pattern', () => {
            const pattern = /test/g;
            const buildLink = () => [];

            resolver.addPattern(pattern, buildLink);

            expect(resolver.patterns).toHaveLength(1);
            expect(resolver.patterns[0]).toEqual({ pattern, buildLink });
        });

        test('should add multiple patterns', () => {
            const pattern1 = /test1/g;
            const pattern2 = /test2/g;
            const buildLink = () => [];

            resolver.addPattern(pattern1, buildLink);
            resolver.addPattern(pattern2, buildLink);

            expect(resolver.patterns).toHaveLength(2);
            expect(resolver.patterns[0]).toEqual({ pattern: pattern1, buildLink });
            expect(resolver.patterns[1]).toEqual({ pattern: pattern2, buildLink });
        });
    });

    describe('findReferences', () => {
        test('should return empty array for text with no matches', async () => {
            const result = await resolver.findReferences('test text');
            expect(result).toEqual([]);
        });

        test('should return empty array for null text', async () => {
            const result = await resolver.findReferences(null);
            expect(result).toEqual([]);
        });

        test('should find references using added pattern', async () => {
            const pattern = /test/g;
            const buildLink = async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    target: 'test-target',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            });

            resolver.addPattern(pattern, buildLink);
            const result = await resolver.findReferences('this is a test text');

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                text: 'test',
                startOffset: 10,
                endOffset: 14,
                matches: [{
                    text: 'test',
                    target: 'test-target',
                    startOffset: 10,
                    endOffset: 14
                }]
            });
        });

        test('should handle multiple matches', async () => {
            const pattern = /test/g;
            const buildLink = async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    target: 'test-target',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            });

            resolver.addPattern(pattern, buildLink);
            const result = await resolver.findReferences('test another test');

            expect(result).toHaveLength(2);
            expect(result[0]).toMatchObject({
                text: 'test',
                startOffset: 0,
                endOffset: 4,
                matches: [{
                    text: 'test',
                    startOffset: 0,
                    endOffset: 4
                }]
            });
            expect(result[1]).toMatchObject({
                text: 'test',
                startOffset: 13,
                endOffset: 17,
                matches: [{
                    text: 'test',
                    startOffset: 13,
                    endOffset: 17
                }]
            });
        });

        test('should handle errors in buildLink gracefully', async () => {
            jest.spyOn(console, 'error').mockImplementation(() => {});

            const pattern = /test/g;
            const buildLink = async () => { throw new Error('Test error'); };

            resolver.addPattern(pattern, buildLink);
            const result = await resolver.findReferences('test text');

            expect(result).toEqual([]);

            jest.spyOn(console, 'error').mockRestore();
        });
    });

    describe('processText', () => {
        test('should return empty array for null text', async () => {
            const result = await resolver.processText(null);
            expect(result).toEqual([]);
        });

        test('should return empty array for undefined text', async () => {
            const result = await resolver.processText(undefined);
            expect(result).toEqual([]);
        });

        test('should return empty array for empty text', async () => {
            const result = await resolver.processText('');
            expect(result).toEqual([]);
        });

        test('should return empty array for non-content nodes', async () => {
            const nonContentNode = new MockNode('/collection/tx/code/gv', 'code', 'gv', 'tx');
            const nonContentResolver = new BaseReferenceResolver(nonContentNode);

            const pattern = /test/g;
            const buildLink = async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    target: 'test-target',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            });

            nonContentResolver.addPattern(pattern, buildLink);
            const result = await nonContentResolver.processText('this is a test');

            expect(result).toEqual([]);
        });

        test('should process text and return references', async () => {
            const pattern = /test/g;
            const buildLink = async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    target: 'test-target',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            });

            resolver.addPattern(pattern, buildLink);
            const result = await resolver.processText('this is a test');

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                text: 'test',
                startOffset: 10,
                endOffset: 14,
                matches: [{
                    text: 'test',
                    target: 'test-target',
                    startOffset: 10,
                    endOffset: 14
                }]
            });
        });

        test('should handle overlapping references by keeping longer ones', async () => {
            const pattern1 = /test/g;
            const pattern2 = /test text/g;

            resolver.addPattern(pattern1, async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    target: 'test-target',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            }));

            resolver.addPattern(pattern2, async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    target: 'test-text-target',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            }));

            const result = await resolver.processText('this is a test text');

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                text: 'test text',
                startOffset: 10,
                endOffset: 19,
                matches: [{
                    text: 'test text',
                    target: 'test-text-target',
                    startOffset: 10,
                    endOffset: 19
                }]
            });
        });

        test('should mark unresolved references', async () => {
            const pattern = /test/g;
            const buildLink = async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [{
                    text: matchedText,
                    status: 'unresolved',
                    startOffset: offset,
                    endOffset: offset + matchedText.length
                }]
            });

            resolver.addPattern(pattern, buildLink);
            const result = await resolver.processText('this is a test');

            expect(result).toHaveLength(1);
            expect(result[0]).toMatchObject({
                text: 'test',
                startOffset: 10,
                endOffset: 14,
                matches: [{
                    text: 'test',
                    status: 'unresolved',
                    startOffset: 10,
                    endOffset: 14
                }]
            });
        });

        test('should prioritize references with more resolved matches', async () => {
            resolver.addPattern(/test/g, async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [
                    {
                        text: matchedText,
                        target: 'test-target-1',
                        startOffset: offset,
                        endOffset: offset + matchedText.length
                    },
                    {
                        text: matchedText,
                        target: 'test-target-2',
                        startOffset: offset,
                        endOffset: offset + matchedText.length
                    }
                ]
            }));

            resolver.addPattern(/test/g, async (matchedText, offset) => ({
                text: matchedText,
                startOffset: offset,
                endOffset: offset + matchedText.length,
                matches: [
                    {
                        text: matchedText,
                        target: 'test-target-3',
                        startOffset: offset,
                        endOffset: offset + matchedText.length
                    },
                    {
                        text: matchedText,
                        target: undefined,
                        status: 'unresolved',
                        startOffset: offset,
                        endOffset: offset + matchedText.length
                    }
                ]
            }));

            const result = await resolver.processText('this is a test');

            expect(result).toHaveLength(1);
            expect(result[0].matches).toHaveLength(2);
            expect(result[0].matches[0].target).toBe('test-target-1');
            expect(result[0].matches[1].target).toBe('test-target-2');
            expect(result[0].matches.every(m => !m.status && m.target)).toBe(true);
        });
    });
});
