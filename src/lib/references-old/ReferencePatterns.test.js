import { ReferencePatterns } from './ReferencePatterns';

describe('ReferencePatterns', () => {
  let patterns;

  beforeEach(() => {
    patterns = new ReferencePatterns();
  });

  describe('CODE_NAME', () => {
    const validCodeReferences = [
      [', Government Code', 'Government Code'],
      [', Tax Code', 'Tax Code'],
      [' of the Government Code', 'Government Code'],
      [' of the Tax Code', 'Tax Code'],
      [', Civil Practice and Remedies Code', 'Civil Practice and Remedies Code'],
      [' of the Civil Practice and Remedies Code', 'Civil Practice and Remedies Code'],
      [', Code of Criminal Procedure', 'Code of Criminal Procedure'],
      [' of the Code of Criminal Procedure', 'Code of Criminal Procedure'],
      [', Texas Constitution', 'Texas Constitution'],
    ];

    const invalidCodeReferences = [
      'Tax',
      'Code',
      ', Invalid Code',
    ];

    test.each(validCodeReferences)('should match valid code reference: %s', (codeRef, expectedCode) => {
      const match = codeRef.match(patterns.CODE_NAME);
      expect(match).not.toBeNull();
      expect(match[0].trim()).toBe(expectedCode);
    });

    test.each(invalidCodeReferences)('should not match invalid code reference: %s', (codeRef) => {
      const match = codeRef.match(patterns.CODE_NAME);
      expect(match).toBeNull();
    });

    test('should match all codes from CODE_MAP', () => {
      Object.keys(ReferencePatterns.CODE_MAP).forEach(codeName => {
        const withComma = `, ${codeName}`;
        const withOfThe = ` of the ${codeName}`;

        expect(withComma.match(patterns.CODE_NAME)).not.toBeNull();
        expect(withOfThe.match(patterns.CODE_NAME)).not.toBeNull();
      });
    });
  });

  describe('TITLE_NUMBER', () => {
    const validTitles = [
      ['Title 1', '1'],
      ['Title 42', '42'],
      ['Title 999', '999'],
      ['Title 1A', '1A'],
      ['Title 42-A', '42-A'],
    ];

    const invalidTitles = [
      'Title',
      'Title A',
      'Title 1B2',
      'Title1',
      'TitleA',
      'Title -A',
      'Title A-'
    ];

    test.each(validTitles)('should match valid title: %s', (title, expected) => {
      const match = title.match(patterns.TITLE_NUMBER);
      expect(match).not.toBeNull();
      expect(match[0]).toBe(expected);
    });

    test.each(invalidTitles)('should not match invalid title: %s', (title) => {
      const match = title.match(patterns.TITLE_NUMBER);
      expect(match).toBeNull();
    });
  });

  describe('SUBTITLE_LETTER', () => {
    const validSubtitles = [
      ['Subtitle A', 'A'],
      ['Subtitle B1', 'B1'],
      ['Subtitle Z-1', 'Z-1'],
      ['subtitle A', 'A']
    ];

    const invalidSubtitles = [
      'Subtitle',
      'Subtitle 1',
      'Subtitle AA',
      'Subtitle a',
      'SubtitleA'
    ];

    test.each(validSubtitles)('should match valid subtitle: %s', (subtitle, expected) => {
      const match = subtitle.match(patterns.SUBTITLE_LETTER);
      expect(match).not.toBeNull();
      expect(match[0]).toBe(expected);
    });

    test.each(invalidSubtitles)('should not match invalid subtitle: %s', (subtitle) => {
      const match = subtitle.match(patterns.SUBTITLE_LETTER);
      expect(match).toBeNull();
    });
  });

  describe('CHAPTER_NUMBER', () => {
    const validChapters = [
      ['Chapter 6', '6'],
      ['Chapter 66', '66'],
      ['Chapter 666', '666'],
      ['Chapter 66A', '66A'],
      ['Chapter 66-1/2', '66-1/2'],
    ];

    const invalidChapters = [
      'Chapter',
      'Chapter AA',
      'Chapter 6B2',
      'Chapter6',
      'ChapterA'
    ];

    test.each(validChapters)('should match valid chapter: %s', (chapter, expected) => {
      const match = chapter.match(patterns.CHAPTER_NUMBER);
      expect(match).not.toBeNull();
      expect(match[1]).toBe(expected);
    });

    test.each(invalidChapters)('should not match invalid chapter: %s', (chapter) => {
      const match = chapter.match(patterns.CHAPTER_NUMBER);
      expect(match).toBeNull();
    });
  });

  describe('SECTION_NUMBER', () => {
    const validSections = [
      ['Section 1', '1'],
      ['Section 1.1', '1.1'],
      ['Section 11.11', '11.11'],
      ['Section 111.111', '111.111'],
      ['Section 11A.11', '11A.11'],
      ['Section 11-a', '11-a'],
      ['Section 11-a-1', '11-a-1']
    ];

    const invalidSections = [
      'Section .1',
      'Section 1.',
      'Section 1..1',
      'Section 1.1.',
      'Section A1',
      'Section 1A.',
      'Section 1.A'
    ];

    test.each(validSections)('should match valid section: %s', (section, expected) => {
      const match = section.match(patterns.SECTION_NUMBER);
      expect(match).toBeTruthy();
      expect(match[0]).toBe(expected);
    });

    test.each(invalidSections)('should not match invalid section: %s', (section) => {
      const match = section.match(patterns.SECTION_NUMBER);
      expect(match).toBeNull();
    });
  });

  describe('CN_SECTION_NUMBER', () => {
    const validSections = [
      ['Section 1', '1'],
      ['Section 12A', '12A'],
      ['Section 30b', '30b'],
      ['Section 67-a', '67-a'],
      ['Section 49-d-1', '49-d-1'],
      ['Section 50b-6A', '50b-6A']
    ];

    const invalidSections = [
      'Section .1',
      'Section 1.',
      'Section 1.1',
      'Section A1',
      'Section -1',
      'Section 1--a',
      'Section 1a-'
    ];

    test.each(validSections)('should match valid section: %s', (section, expected) => {
      const match = section.match(patterns.CN_SECTION_NUMBER);
      expect(match).toBeTruthy();
      expect(match[0]).toBe(expected);
    });

    test.each(invalidSections)('should not match invalid section: %s', (section) => {
      const match = section.match(patterns.CN_SECTION_NUMBER);
      expect(match).toBeNull();
    });
  });

  describe('SUBSECTION', () => {
    const validSubsections = [
      '(a)',
      '(b)',
      '(z)',
      '(a-1)',
      '(b-2)',
      '(z-99)'
    ];

    const invalidSubsections = [
      '()',
      '(1)',
      '(A)',
      '(a1)',
      '(a-)',
      '(-1)',
      'a',
      '(aa)'
    ];

    test.each(validSubsections)('should match valid subsection: %s', (subsection) => {
      const match = subsection.match(patterns.SUBSECTION);
      expect(match).not.toBeNull();
      expect(match[0]).toBe(subsection);
    });

    test.each(invalidSubsections)('should not match invalid subsection: %s', (subsection) => {
      const match = subsection.match(patterns.SUBSECTION);
      expect(match).toBeNull();
    });
  });

  describe('SUBDIVISION', () => {
    const validSubdivisions = [
      '(1)',
      '(2)',
      '(10)',
      '(999)'
    ];

    const invalidSubdivisions = [
      '()',
      '(a)',
      '(A)',
      '(1a)',
      '(a1)',
      '1',
      '(01)'
    ];

    test.each(validSubdivisions)('should match valid subdivision: %s', (subdivision) => {
      const match = subdivision.match(patterns.SUBDIVISION);
      expect(match).not.toBeNull();
      expect(match[0]).toBe(subdivision);
    });

    test.each(invalidSubdivisions)('should not match invalid subdivision: %s', (subdivision) => {
      const match = subdivision.match(patterns.SUBDIVISION);
      expect(match).toBeNull();
    });
  });

  describe('SUBPARAGRAPH', () => {
    const validSubparagraphs = [
      '(A)',
      '(B)',
      '(Z)'
    ];

    const invalidSubparagraphs = [
      '()',
      '(a)',
      '(1)',
      '(A1)',
      '(1A)',
      'A',
      '(AA)'
    ];

    test.each(validSubparagraphs)('should match valid subparagraph: %s', (subparagraph) => {
      const match = subparagraph.match(patterns.SUBPARAGRAPH);
      expect(match).not.toBeNull();
      expect(match[0]).toBe(subparagraph);
    });

    test.each(invalidSubparagraphs)('should not match invalid subparagraph: %s', (subparagraph) => {
      const match = subparagraph.match(patterns.SUBPARAGRAPH);
      expect(match).toBeNull();
    });
  });

  describe('ARTICLE_NUMBER', () => {
    describe('should match Arabic numerals as article numbers', () => {
      const validArticleNumbers = [
        '1',
        '3',
        '10',
        '16',
        '42',
      ];

      test.each(validArticleNumbers)('should match valid article number: %s', (articleNumber) => {
        const match = articleNumber.match(patterns.ARTICLE_NUMBER);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(articleNumber);
      });
    });

    describe('should match Roman numerals as article numbers', () => {
      const validArticleNumbers = [
        'I',
        'III',
        'X',
        'XVI',
        'XLII'
      ];

      test.each(validArticleNumbers)('should match valid article number: %s', (articleNumber) => {
        const match = articleNumber.match(patterns.ARTICLE_NUMBER);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(articleNumber);
      });
    });

    describe('should work in context with other patterns', () => {
      const patterns = new ReferencePatterns();
      const articlePattern = new RegExp(`Article ${patterns.ARTICLE_NUMBER.source}`);

      const validContexts = [
        'see Article 1 here',
        'see Article 16 here',
        'see Article I here',
        'see Article XVI here'
      ];

      const invalidContexts = [
        'see Article here',
        'see Article A here'
      ];

      test.each(validContexts)('should match valid context: %s', (context) => {
        const match = context.match(articlePattern);
        expect(match).not.toBeNull();
      });

      test.each(invalidContexts)('should not match invalid context: %s', (context) => {
        const match = context.match(articlePattern);
        expect(match).toBeNull();
      });
    });

    it('should capture the article number correctly', () => {
      // Create a pattern that captures ARTICLE_NUMBER
      const articlePattern = new RegExp(`Article (${patterns.ARTICLE_NUMBER.source})`);

      // Test with Arabic numerals
      let match = 'Article 16'.match(articlePattern);
      expect(match).not.toBeNull();
      expect(match[1]).toBe('16');

      // Test with Roman numerals
      match = 'Article XVI'.match(articlePattern);
      expect(match).not.toBeNull();
      expect(match[1]).toBe('XVI');
    });
  });

  describe('ROMAN_NUMERALS', () => {
    const validRomans = [
      'I',
      'II',
      'III',
      'IV',
      'V',
      'VI',
      'VII',
      'VIII',
      'IX',
      'X',
      'XI',
      'XII',
      'XIII',
      'XIV',
      'XV',
      'XVI',
      'XVII',
      'XVIII',
      'XIX',
      'XX',
      'XXI',
      'XXII',
      'XXIII',
      'XXIV',
      'XXV',
      'XXVI',
      'XXVII',
      'XXVIII',
      'XXIX',
      'XXX',
      'XXXI',
      'XXXII',
      'XXXIII',
      'XXXIV',
      'XXXV',
    ];

    const invalidRomans = [
      '',
      'A',
      '1',
      'IIII',
      'VV',
      'IC',
      'MMMM'
    ];

    test.each(validRomans)('should match valid roman: %s', (roman) => {
      const match = roman.match(patterns.ROMAN_NUMERALS);
      expect(match).not.toBeNull();
    });

    test.each(invalidRomans)('should not match invalid roman: %s', (roman) => {
      const match = roman.match(patterns.ROMAN_NUMERALS);
      expect(match).toBeNull();
    });
  });

  describe('Connectors and Separators', () => {
    test('OPTIONAL_COMMA should match various comma patterns', () => {
      const validCommas = [
        ', ',
        ',',
        ' ',
        ''
      ];

      validCommas.forEach(comma => {
        const match = comma.match(patterns.OPTIONAL_COMMA);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(comma);
      });
    });

    test('OF_THE_THIS should match various "of the" patterns', () => {
      const validPatterns = [
        ' of ',
        ' of the ',
        ' of this ',
      ];

      validPatterns.forEach(pattern => {
        const match = pattern.match(patterns.OF_THE_THIS);
        expect(match).not.toBeNull();
      });
    });
  });

  describe('Prefix Patterns', () => {
    const prefixTests = [
      {
        pattern: 'SECTION_PREFIX',
        valid: ['Section ', 'Sections ', 'section ', 'sections '],
        invalid: ['Sect ', 'Sectionn', 'section', 'sections', 'theSection']
      },
      {
        pattern: 'CHAPTER_PREFIX',
        valid: ['Chapter ', 'Chapters ', 'chapter ', 'chapters '],
        invalid: ['Chap ', 'Chapterr', 'chapter', 'chapters', 'theChapter']
      },
      {
        pattern: 'TITLE_PREFIX',
        valid: ['Title ', 'Titles ', 'title ', 'titles '],
        invalid: ['Titl ', 'Titlee', 'title', 'titles', 'theTitle']
      },
      {
        pattern: 'SUBTITLE_PREFIX',
        valid: ['Subtitle ', 'Subtitles ', 'subtitle ', 'subtitles '],
        invalid: ['Sub ', 'Subtitlee', 'subtitle', 'subtitles', 'theSubtitle']
      },
      {
        pattern: 'SUBCHAPTER_PREFIX',
        valid: ['Subchapter ', 'Subchapters ', 'subchapter ', 'subchapters '],
        invalid: ['Sub ', 'Subchapterr', 'subchapter', 'subchapters', 'theSubchapter']
      }
    ];

    prefixTests.forEach(({ pattern, valid, invalid }) => {
      describe(pattern, () => {
        test.each(valid)('should match valid prefix: %s', (prefix) => {
          const match = prefix.match(patterns[pattern]);
          expect(match).not.toBeNull();
          expect(match[0]).toBe(prefix);
        });

        test.each(invalid)('should not match invalid prefix: %s', (prefix) => {
          const match = prefix.match(patterns[pattern]);
          expect(match).toBeNull();
        });
      });
    });
  });

  describe('List Patterns', () => {
    describe('COMMA_SEPARATED_LIST', () => {
      const validLists = [
        ', item2, item3',
        ', 1.1, 1.2, 1.3',
        ', (a), (b), (c)',
        ''  // empty list is valid
      ];

      const invalidLists = [
        ',,',
        ', ,',
        ', item,'
      ];

      test.each(validLists)('should match valid list: %s', (list) => {
        const match = list.match(patterns.COMMA_SEPARATED_LIST);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(list);
      });

      test.each(invalidLists)('should not match invalid list: %s', (list) => {
        const match = list.match(new RegExp(`^${patterns.COMMA_SEPARATED_LIST.source}$`));
        expect(match).toBeNull();
      });
    });

    describe('AND_OR_THROUGH', () => {
      const validConnectors = [
        ' and ',
        ' or ',
        ' through ',
        ', and ',
        ', or ',
        ', through ',
        ' , and ',
        ' , or ',
        ' , through ',
        ',and ',
        ',or ',
        ',through ',
        ' ,and ',
        ' ,or ',
        ' ,through ',
      ];

      const invalidConnectors = [
        'and',
        'or',
        'through',
        ' and',
        ' or',
        ' thorugh',
        'and ',
        'or ',
        'through '
      ];

      test.each(validConnectors)('should match valid connector: %s', (connector) => {
        const match = connector.match(patterns.AND_OR_THROUGH);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(connector);
      });

      test.each(invalidConnectors)('should not match invalid connector: %s', (connector) => {
        const match = connector.match(patterns.AND_OR_THROUGH);
        expect(match).toBeNull();
      });
    });
  });

  describe('Negative Patterns', () => {
    describe('NOT_FOLLOWED_BY_CODE', () => {
      test('should match when not followed by code', () => {
        const text = 'Chapter 1 of the Regulations';
        const pattern = new RegExp(`Chapter 1${patterns.NOT_FOLLOWED_BY_CODE.source}`);
        expect(text.match(pattern)).not.toBeNull();
      });

      test('should not match when followed by code', () => {
        const text = 'Chapter 1, Government Code';
        const pattern = new RegExp(`Chapter 1${patterns.NOT_FOLLOWED_BY_CODE.source}`);
        expect(text.match(pattern)).toBeNull();
      });
    });
  });

  describe('combine patterns', () => {
    test('should combine simple patterns without capture groups', () => {
      const result = patterns.combine(
        patterns.SECTION_PREFIX,
        patterns.COMMA_AND_SPACE
      );

      // Test behavior
      expect('Section , ').toMatch(result);
      expect('Sections , ').toMatch(result);
      expect('section , ').toMatch(result);
      expect('Sec. , ').toMatch(result);

      // Test invalid cases
      expect('SectionX , ').not.toMatch(result);
      expect('Section,').not.toMatch(result);
    });

    test('should preserve existing capture groups', () => {
      const result = patterns.combine(
        patterns.SECTION_NUMBER,
        patterns.COMMA_AND_SPACE
      );

      const matches1 = Array.from('Section 1.1, '.matchAll(result));
      expect(matches1.length).toBe(1);
      expect(matches1[0][1]).toBe('1.1');

      const matches2 = Array.from('Section 42A.123, '.matchAll(result));
      expect(matches2.length).toBe(1);
      expect(matches2[0][1]).toBe('42A.123');

      // Test invalid cases
      expect(Array.from('Section A.1, '.matchAll(result)).length).toBe(0);
      expect(Array.from('Section 1..1, '.matchAll(result)).length).toBe(0);
    });

    test('should wrap array of patterns in capture group', () => {
      const result = patterns.combine(
        [patterns.SECTION_PREFIX, patterns.SECTION_NUMBER],
        patterns.COMMA_AND_SPACE
      );

      // Test behavior with array wrapping
      const matches1 = Array.from('Section 1.1, '.matchAll(result));
      expect(matches1.length).toBe(1);
      expect(matches1[0][1]).toBe('Section 1.1');
      expect(matches1[0][2]).toBe('1.1');

      const matches2 = Array.from('Section 42A.123, '.matchAll(result));
      expect(matches2.length).toBe(1);
      expect(matches2[0][1]).toBe('Section 42A.123');
      expect(matches2[0][2]).toBe('42A.123');

      // Test invalid cases
      expect(Array.from('Section A.1, '.matchAll(result)).length).toBe(0);
      expect(Array.from('Section 1..1, '.matchAll(result)).length).toBe(0);
    });

    test('should handle multiple arrays of patterns', () => {
      const result = patterns.combine(
        [patterns.SECTION_PREFIX, patterns.SECTION_NUMBER],
        patterns.COMMA_AND_SPACE,
        [patterns.ARTICLE_PREFIX, patterns.ROMAN_NUMERALS]
      );

      // Test behavior with multiple arrays
      const matches1 = Array.from('Section 1.1, Article I'.matchAll(result));
      expect(matches1.length).toBe(1);
      expect(matches1[0][1]).toBe('Section 1.1');
      expect(matches1[0][2]).toBe('1.1');
      expect(matches1[0][3]).toBe('Article I');
      expect(matches1[0][4]).toBe('I');

      const matches2 = Array.from('Section 42A.123, Article XIV'.matchAll(result));
      expect(matches2.length).toBe(1);
      expect(matches2[0][1]).toBe('Section 42A.123');
      expect(matches2[0][2]).toBe('42A.123');
      expect(matches2[0][3]).toBe('Article XIV');
      expect(matches2[0][4]).toBe('XIV');

      // Test invalid cases
      expect(Array.from('Section A.1, Article A'.matchAll(result)).length).toBe(0);
      expect(Array.from('Section 1.1, ArticleI'.matchAll(result)).length).toBe(0);
    });

    test('should handle mixed string and RegExp patterns', () => {
      const result = patterns.combine(
        [patterns.SECTION_PREFIX, /(\d+)/],
        ' of ',
        patterns.ARTICLE_PREFIX
      );

      // Test behavior with mixed patterns
      const matches1 = Array.from('Section 42 of Article '.matchAll(result));
      expect(matches1.length).toBe(1);
      expect(matches1[0][1]).toBe('Section 42');
      expect(matches1[0][2]).toBe('42');

      // Test invalid cases
      expect(Array.from('Section A of Article '.matchAll(result)).length).toBe(0);
      expect(Array.from('Section 42ofArticle '.matchAll(result)).length).toBe(0);
    });

    test('should not wrap non-capturing groups in capture groups', () => {
      const result = patterns.combine(
        [patterns.SECTION_PREFIX, patterns.SECTION_NUMBER],
        /(?:\s+of\s+)/,
        patterns.ARTICLE_PREFIX
      );

      // Test behavior with non-capturing groups
      const matches1 = Array.from('Section 1.1 of Article '.matchAll(result));
      expect(matches1.length).toBe(1);
      expect(matches1[0][1]).toBe('Section 1.1');
      expect(matches1[0][2]).toBe('1.1');

      const matches2 = Array.from('Section 42A.123 of Article '.matchAll(result));
      expect(matches2.length).toBe(1);
      expect(matches2[0][1]).toBe('Section 42A.123');
      expect(matches2[0][2]).toBe('42A.123');

      // Test invalid cases
      expect(Array.from('Section 1.1ofArticle '.matchAll(result)).length).toBe(0);
      expect(Array.from('Section A.1 of Article '.matchAll(result)).length).toBe(0);
    });

    test('should combine article prefix with roman numerals', () => {
      const combined = patterns.combine(
        patterns.ARTICLE_PREFIX,
        patterns.ROMAN_NUMERALS
      );

      const validCombos = [
        'Article I',
        'Article II',
        'Article III'
      ];

      validCombos.forEach(combo => {
        const match = combo.match(combined);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(combo);
      });
    });

    test('should combine section prefix with number', () => {
      const combined = patterns.combine(
        patterns.SECTION_PREFIX,
        patterns.SECTION_NUMBER
      );

      const validCombos = [
        'Section 1.1',
        'Section 411.087',
        'section 1A.1'
      ];

      validCombos.forEach(combo => {
        const match = combo.match(combined);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(combo);
      });
    });

    test('should combine chapter with code name', () => {
      const combined = patterns.combine(
        patterns.CHAPTER_PREFIX,
        patterns.CHAPTER_NUMBER,
        patterns.COMMA_OF_THE_THIS,
        patterns.CODE_NAME
      );

      const validCombos = [
        'Chapter 411, Government Code',
        'Chapter 1, Tax Code',
        'Chapter 42A of the Education Code'
      ];

      validCombos.forEach(combo => {
        const match = combo.match(combined);
        expect(match).not.toBeNull();
        expect(match[0]).toBe(combo);
      });
    });

    test('should combine subsection prefix with subsection', () => {
      const combined = patterns.combine(
        patterns.SUBSECTION_PREFIX,
        patterns.SUBSECTION
      );

      const validCombos = [
        'Subsection (a)',
        'Subsection (b)',
        'subsection (c)',
        'Subsection (a-1)',
        'Subsection (b-2)',
        'subsection (z-9)'
      ];

      validCombos.forEach(combo => {
        const matches = Array.from(combo.matchAll(combined));
        expect(matches.length).toBe(1);
        expect(matches[0][0]).toBe(combo);
        // The subsection part should be captured
        expect(matches[0][1]).toBe(combo.match(/\([a-z](?:-\d+)?\)/)[0]);
      });

      const invalidCombos = [
        'Subsection a',
        'Subsection(a)',
        'Subsection (1)',
        'Subsection (A)',
        'Subsection (a1)',
        'Subsection (a-)',
        'Subsection (-1)'
      ];

      invalidCombos.forEach(combo => {
        const matches = Array.from(combo.matchAll(combined));
        expect(matches.length).toBe(0);
      });
    });

    test('should preserve capture groups when combining patterns', () => {
      const combined = patterns.combine(
        patterns.SUBSECTION_PREFIX,
        patterns.SUBSECTION
      );

      const matches = Array.from('Subsection (a)'.matchAll(combined));
      expect(matches.length).toBe(1);
      expect(matches[0][1]).toBe('(a)');  // The subsection part should be captured
    });
  });
});
