import { ModalPositioning } from './ModalPositioning';

// Mock DOM environment
global.window = {
  innerWidth: 1024,
  innerHeight: 768
};

global.document = {
  querySelector: jest.fn()
};

describe('ModalPositioning', () => {
  let mockPanel;
  let mockTargetRect;

  beforeEach(() => {
    mockPanel = {
      getBoundingClientRect: jest.fn(() => ({
        left: 100,
        top: 80,
        right: 900,
        bottom: 600,
        width: 800,
        height: 520
      })),
      scrollTop: 0,
      scrollLeft: 0,
      offsetWidth: 800,
      offsetHeight: 520,
      clientHeight: 520
    };

    mockTargetRect = {
      left: 200,
      top: 200,
      right: 300,
      bottom: 220,
      width: 100,
      height: 20
    };

    // Reset querySelector to return panel by default, no header/breadcrumb unless specified
    document.querySelector = jest.fn((selector) => {
      if (selector === '.statute-content') return mockPanel;
      return null; // No header or breadcrumb by default
    });
  });

  describe('calculatePosition with useFixedPosition=false (legacy behavior)', () => {
    test('should calculate absolute position relative to panel with scroll offset', () => {
      mockPanel.scrollTop = 100;
      mockPanel.scrollLeft = 50;

      const result = ModalPositioning.calculatePosition(mockTargetRect, {
        width: 320,
        height: 63,
        hasCaret: true,
        useFixedPosition: false
      });

      expect(result).toMatchObject({
        left: expect.any(Number),
        top: expect.any(Number),
        caretLeft: expect.any(Number),
        shouldFlip: expect.any(Boolean)
      });
    });
  });

  describe('calculatePosition with useFixedPosition=true (new behavior)', () => {
    test('should calculate fixed position using viewport coordinates', () => {
      // Simulate scrolled content
      mockPanel.scrollTop = 200;
      
      const result = ModalPositioning.calculatePosition(mockTargetRect, {
        width: 320,
        height: 63,
        hasCaret: true,
        useFixedPosition: true
      });

      expect(result).toMatchObject({
        left: expect.any(Number),
        top: expect.any(Number),
        caretLeft: expect.any(Number),
        shouldFlip: expect.any(Boolean)
      });

      // For fixed positioning, coordinates should be viewport-based
      // and not affected by panel scroll
      expect(result.left).toBeGreaterThanOrEqual(0);
      expect(result.top).toBeGreaterThanOrEqual(0);
    });

    test('should prefer positioning above target', () => {
      const result = ModalPositioning.calculatePosition(mockTargetRect, {
        width: 320,
        height: 63,
        hasCaret: true,
        useFixedPosition: true
      });

      // With target at top=200 and height=63, picker should be above
      expect(result.top).toBeLessThan(mockTargetRect.top);
      expect(result.shouldFlip).toBe(false);
    });

    test('should flip below target when insufficient space above', () => {
      // Target near top of viewport - needs to be closer to top to trigger flip
      const targetNearTop = {
        ...mockTargetRect,
        top: 50,
        bottom: 70
      };

      const result = ModalPositioning.calculatePosition(targetNearTop, {
        width: 320,
        height: 63,
        hasCaret: true,
        useFixedPosition: true
      });

      // Should flip to below target: targetTop + targetHeight + margin
      expect(result.top).toBe(targetNearTop.top + targetNearTop.height + 8);
      expect(result.shouldFlip).toBe(true);
    });

    test('should constrain picker within visible panel bounds', () => {
      // Target at far right of panel
      const targetAtEdge = {
        ...mockTargetRect,
        left: 850,
        right: 950
      };

      const result = ModalPositioning.calculatePosition(targetAtEdge, {
        width: 320,
        height: 63,
        hasCaret: true,
        useFixedPosition: true
      });

      // Picker should be constrained within panel bounds
      const panelRight = Math.min(window.innerWidth, mockPanel.getBoundingClientRect().right);
      expect(result.left + 320).toBeLessThanOrEqual(panelRight);
    });

    test('should account for fixed header when calculating position', () => {
      // Mock a fixed header at the top
      const mockHeader = {
        getBoundingClientRect: jest.fn(() => ({
          top: 0,
          height: 60,
          left: 0,
          right: window.innerWidth
        }))
      };

      // Override querySelector to return our mock header
      const originalQuerySelector = document.querySelector;
      document.querySelector = jest.fn((selector) => {
        if (selector === 'header') return mockHeader;
        return originalQuerySelector.call(document, selector);
      });

      // Target near top of viewport (would normally conflict with header)
      const targetNearTop = {
        ...mockTargetRect,
        top: 80,
        bottom: 100
      };

      const result = ModalPositioning.calculatePosition(targetNearTop, {
        width: 400,
        height: 300,
        useFixedPosition: true,
        preferAbove: true
      });

      // Modal should not overlap with header - should be positioned to avoid overlap only if necessary
      // Since target is at top 80px and modal height 300px, it would position above target at 80-300-8 = -228
      // This would overlap header, so it should be repositioned to avoid header
      expect(result.top).toBeGreaterThanOrEqual(60 + 8); // header height + margin

      // Restore original querySelector
      document.querySelector = originalQuerySelector;
    });

    describe('Fixed positioning with carets (HighlightPicker)', () => {
      test('should position above target with caret by default', () => {
        const result = ModalPositioning.calculatePosition(mockTargetRect, {
          width: 320,
          height: 63,
          hasCaret: true,
          useFixedPosition: true
        });

        // Should position above target: targetTop - height - margin
        const expectedTop = mockTargetRect.top - 63 - 8;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(false);
        expect(result.caretLeft).toBeDefined();
      });

      test('should flip below target when insufficient space above', () => {
        const targetNearTop = {
          ...mockTargetRect,
          top: 50,
          bottom: 70
        };

        const result = ModalPositioning.calculatePosition(targetNearTop, {
          width: 320,
          height: 63,
          hasCaret: true,
          useFixedPosition: true
        });

        // Should flip below target: targetTop + targetHeight + margin
        const expectedTop = targetNearTop.top + targetNearTop.height + 8;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(true);
        expect(result.caretLeft).toBeDefined();
      });

      test('should constrain to viewport when flipped', () => {
        const targetNearBottom = {
          ...mockTargetRect,
          top: 50,
          bottom: 70
        };

        // Mock viewport to be smaller to force clamping
        const originalInnerHeight = window.innerHeight;
        window.innerHeight = 150;

        const result = ModalPositioning.calculatePosition(targetNearBottom, {
          width: 320,
          height: 63,
          hasCaret: true,
          useFixedPosition: true
        });

        // Should be clamped to fit within viewport
        expect(result.top + 63).toBeLessThanOrEqual(150 - 8); // height - margin
        expect(result.shouldFlip).toBe(true);

        // Restore original viewport height
        window.innerHeight = originalInnerHeight;
      });
    });

    describe('Fixed positioning without carets (Regular modals)', () => {
      test('should position above target by default when preferAbove=true', () => {
        const result = ModalPositioning.calculatePosition(mockTargetRect, {
          width: 400,
          height: 300,
          preferAbove: true,
          useFixedPosition: true
        });

        // With targetTop=200, height=300, margin=8: 200-300-8 = -108
        // This would be negative, so it should flip below instead
        expect(result.shouldFlip).toBe(true);
        expect(result.top).toBe(mockTargetRect.top + mockTargetRect.height + 8); // 228
        expect(result.caretLeft).toBeUndefined();
      });

      test('should position below target when preferAbove=false', () => {
        const result = ModalPositioning.calculatePosition(mockTargetRect, {
          width: 400,
          height: 300,
          preferAbove: false,
          useFixedPosition: true
        });

        // Should position below target: targetTop + targetHeight + margin
        const expectedTop = mockTargetRect.top + mockTargetRect.height + 8;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(false);
        expect(result.caretLeft).toBeUndefined();
      });

      test('should flip below when preferAbove=true but insufficient space above', () => {
        const targetNearTop = {
          ...mockTargetRect,
          top: 80,
          bottom: 100
        };

        const result = ModalPositioning.calculatePosition(targetNearTop, {
          width: 400,
          height: 300,
          preferAbove: true,
          useFixedPosition: true
        });

        // Should flip below target: targetTop + targetHeight + margin
        const expectedTop = targetNearTop.top + targetNearTop.height + 8;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(true);
      });

      test('should flip above when preferAbove=false but insufficient space below', () => {
        const targetNearBottom = {
          ...mockTargetRect,
          top: 600,
          bottom: 620
        };

        const result = ModalPositioning.calculatePosition(targetNearBottom, {
          width: 400,
          height: 300,
          preferAbove: false,
          useFixedPosition: true
        });

        // Should flip above target: targetTop - height - margin
        const expectedTop = targetNearBottom.top - 300 - 8;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(true);
      });
    });

    describe('Fixed positioning with caretOffset', () => {
      test('should apply caretOffset when flipping below target', () => {
        const targetNearTop = {
          ...mockTargetRect,
          top: 80,
          bottom: 100
        };

        const caretOffset = 10;
        const result = ModalPositioning.calculatePosition(targetNearTop, {
          width: 400,
          height: 300,
          preferAbove: true,
          useFixedPosition: true,
          caretOffset
        });

        // Should flip below with caretOffset: targetTop + targetHeight + margin + caretOffset
        const expectedTop = targetNearTop.top + targetNearTop.height + 8 + caretOffset;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(true);
      });

      test('should apply caretOffset when flipping above target', () => {
        const targetNearBottom = {
          ...mockTargetRect,
          top: 600,
          bottom: 620
        };

        const caretOffset = 10;
        const result = ModalPositioning.calculatePosition(targetNearBottom, {
          width: 400,
          height: 300,
          preferAbove: false,
          useFixedPosition: true,
          caretOffset
        });

        // Should flip above with caretOffset: targetTop - height - margin - caretOffset
        const expectedTop = targetNearBottom.top - 300 - 8 - caretOffset;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(true);
      });

      test('should not apply caretOffset when not flipping', () => {
        // Use a target higher up so modal can actually fit above
        const targetHigher = {
          ...mockTargetRect,
          top: 400,
          bottom: 420
        };

        const caretOffset = 10;
        const result = ModalPositioning.calculatePosition(targetHigher, {
          width: 400,
          height: 300,
          preferAbove: true,
          useFixedPosition: true,
          caretOffset
        });

        // Should position above without caretOffset: targetTop - height - margin
        const expectedTop = targetHigher.top - 300 - 8;
        expect(result.top).toBe(expectedTop);
        expect(result.shouldFlip).toBe(false);
      });
    });

    describe('Fixed positioning with header and breadcrumb', () => {
      test('should account for both header and breadcrumb when detecting fixed area', () => {
        const mockHeader = {
          getBoundingClientRect: jest.fn(() => ({
            top: 0,
            height: 60,
            left: 0,
            right: window.innerWidth
          }))
        };

        const mockBreadcrumb = {
          getBoundingClientRect: jest.fn(() => ({
            top: 60,
            height: 50,
            left: 0,
            right: window.innerWidth
          }))
        };

        // Override querySelector to return our mocks
        const originalQuerySelector = document.querySelector;
        document.querySelector = jest.fn((selector) => {
          if (selector === 'header') return mockHeader;
          if (selector === '#breadcrumb-bar') return mockBreadcrumb;
          return originalQuerySelector.call(document, selector);
        });

        // Target that would conflict with header + breadcrumb (110px total)
        const targetNearTop = {
          ...mockTargetRect,
          top: 120,
          bottom: 140
        };

        const result = ModalPositioning.calculatePosition(targetNearTop, {
          width: 400,
          height: 300,
          useFixedPosition: true,
          preferAbove: true
        });

        // Should flip because 120 - 300 - 8 = -188, which is < 110 (header + breadcrumb)
        expect(result.shouldFlip).toBe(true);
        expect(result.top).toBeGreaterThanOrEqual(110); // total fixed area

        // Restore original querySelector
        document.querySelector = originalQuerySelector;
      });
    });
  });

  test('should handle missing panel gracefully', () => {
    document.querySelector = jest.fn().mockReturnValue(null);

    const result = ModalPositioning.calculatePosition(mockTargetRect, {
      width: 320,
      height: 63,
      hasCaret: true,
      useFixedPosition: true
    });

    expect(result).toEqual({
      left: 0,
      top: 0,
      shouldFlip: false,
      caretLeft: 0
    });
  });
});