export const runtime = "nodejs";

import NextAuth from "next-auth"
import { MongoDBAdapter } from "@auth/mongodb-adapter"
import { mongoClient } from "./lib/mongodb"
import Google from "next-auth/providers/google"
import Resend from "next-auth/providers/resend"

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: MongoDBAdapter(mongoClient),
  providers: [
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
      async profile(profile) {
        return { ...profile }
      },
    }),
    Resend({
      apiKey: process.env.AUTH_RESEND_KEY,
      from: "<EMAIL>",
      name: "Email",
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60 // 30 days
  },
  theme: {
    colorScheme: "light",
    brandColor: "#3498db",
    logo: "/lc-logo-square.png",
    buttonText: "#ffffff",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.roles = user.roles || [];
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.id;
      session.user.roles = token.roles || [];
      return session;
    },
  }
})
