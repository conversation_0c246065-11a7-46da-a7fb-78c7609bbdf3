import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // webpack: (config, { isServer }) => {
  //   if (!isServer) {
  //     config.resolve.fallback = {
  //       fs: false,
  //       net: false,
  //       tls: false,
  //       dns: false,
  //       child_process: false,
  //       'fs/promises': false,
  //       'timers/promises': false,
  //       'util/types': false,
  //     };
  //   }
  //   return config;
  // },
};

export default nextConfig;
