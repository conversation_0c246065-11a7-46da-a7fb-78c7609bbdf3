# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build/Lint/Test Commands
- Development: `npm run dev`
- Build: `npm run build`
- Lint: `npm run lint`
- Run all tests: `npm test`
- Run specific test: `npm test -- -t "test name pattern"`
- Watch tests: `npm run test:watch`
- Run E2E tests: `npm run test:e2e`

## Code Style Guidelines
- Follows Next.js core-web-vitals and TypeScript ESLint configs
- Use ES modules (import/export) syntax, not CommonJS (require)
- Destructure imports when possible (eg. import { foo } from 'bar')
- Use the @/ alias for imports from the src directory
- React components use functional style with hooks
- Tests use Jest with React Testing Library
- TypeScript with strict typing enabled
- Error handling: Use try/catch blocks and log errors appropriately
- Maintain feature-based folder structure (components, hooks, services, models)
- Follow existing patterns for context providers and state management
