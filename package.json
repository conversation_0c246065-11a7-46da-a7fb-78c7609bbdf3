{"name": "statute-browser", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "email": "email dev --dir ./src/emails --port 3333", "test": "NODE_NO_WARNINGS=1 jest", "test:watch": "NODE_NO_WARNINGS=1 jest --watch", "test:e2e": "npx playwright test"}, "dependencies": {"@auth/mongodb-adapter": "^3.7.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-slot": "^1.1.1", "@react-email/components": "^0.0.39", "@react-email/render": "^1.0.4", "@tanstack/react-query": "^5.66.5", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "flags": "^4.0.1", "jsdom": "^26.0.0", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "mongodb": "^6.13.0", "next": "^15.3.3", "next-auth": "^5.0.0-beta.25", "punycode": "^2.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-email": "^4.0.13", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "resend": "^4.1.2", "tailwind-merge": "^2.6.0", "util": "^0.12.5", "uuid": "^11.0.5", "walverine": "^0.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.50.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-window": "^1.8.8", "eslint": "^9", "eslint-config-next": "15.1.3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "mongodb-memory-server": "^10.1.4", "node-notifier": "^10.0.1", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}