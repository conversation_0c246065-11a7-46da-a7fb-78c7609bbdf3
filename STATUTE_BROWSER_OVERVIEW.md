# Statute Browser System Overview

## Architecture Diagram

```
                                 +---------------------+
                                 |                     |
                                 |  Next.js Frontend   |
                                 |                     |
                                 +----------+----------+
                                            |
                                            v
                 +-------------------------------------------------------------------------+
                 |                                                                         |
                 |                        React Component Layer                            |
                 |                                                                         |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |  |                |  |                |  |                     |        |
                 |  | StatuteBrowser |  | NavTree        |  | StatuteContent      |        |
                 |  |                |  |                |  |                     |        |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |                                                                         |
                 +-------------------------------------------------------------------------+
                                            |
                                            v
                 +-------------------------------------------------------------------------+
                 |                                                                         |
                 |                         Feature Modules                                 |
                 |                                                                         |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |  |                |  |                |  |                     |        |
                 |  | Bookmarks      |  | Highlights     |  | Search              |        |
                 |  |                |  |                |  |                     |        |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |                                                                         |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |  |                |  |                |  |                     |        |
                 |  | References     |  | NavTree        |  | Statutes            |        |
                 |  |                |  |                |  |                     |        |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |                                                                         |
                 +-------------------------------------------------------------------------+
                                            |
                                            v
                 +-------------------------------------------------------------------------+
                 |                                                                         |
                 |                         Service Layer                                   |
                 |                                                                         |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |  |                |  |                |  |                     |        |
                 |  | StatuteService |  | BookmarkService|  | HighlightService    |        |
                 |  |                |  |                |  |                     |        |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |                                                                         |
                 |  +----------------+  +----------------+                                 |
                 |  |                |  |                |                                 |
                 |  | SearchService  |  | ReferenceService|                               |
                 |  |                |  |                 |                                |
                 |  +----------------+  +-----------------+                                |
                 |                                                                         |
                 +-------------------------------------------------------------------------+
                                            |
                                            v
                 +-------------------------------------------------------------------------+
                 |                                                                         |
                 |                            API Layer                                    |
                 |                                                                         |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |  |                |  |                |  |                     |        |
                 |  | Statute API    |  | Bookmark API   |  | Highlight API       |        |
                 |  |                |  |                |  |                     |        |
                 |  +----------------+  +----------------+  +---------------------+        |
                 |                                                                         |
                 |  +----------------+  +----------------+                                 |
                 |  |                |  |                |                                 |
                 |  | Search API     |  | Reference API  |                                |
                 |  |                |  |                |                                |
                 |  +----------------+  +----------------+                                |
                 |                                                                         |
                 +-------------------------------------------------------------------------+
                                            |
                                            v
                                 +----------+----------+
                                 |                     |
                                 |     MongoDB         |
                                 |                     |
                                 +----------+----------+
                                            |
                                            v
                                 +----------+----------+
                                 |                     |
                                 | Python ETL Scripts  |
                                 |                     |
                                 +---------------------+

```

## System Components

### Frontend Layer
- **Next.js App**: Main application built with Next.js and React
- **StatuteBrowser**: Main UI component with left navigation panel and content area
- **Context Providers**: TreeStateProvider, HighlightProvider, BookmarkProvider, etc.

### Feature Modules
- **Statutes**: Core feature for displaying statute content
- **Navigation Tree**: Hierarchical navigation of collections, codes, chapters, etc.
- **Bookmarks**: Save and manage statute bookmarks
- **Highlights**: Highlight and annotate statute text
- **References**: Process and link statute references in text
- **Search**: Search through statute text

### Data Services
- **StatuteService**: Fetch statute content, structure, and metadata
- **BookmarkService**: Save, update, and retrieve bookmarks
- **HighlightService**: Process text selections and manage highlights
- **ReferenceService**: Identify and resolve references in text
- **SearchService**: Search functionality for statutes

### Data Storage
- **MongoDB**: Stores all statute data, user data, references, etc.
- **Collections**: statutes, bookmarks, highlights, references

### Data Processing
- **ETL Scripts**: Python scripts for data extraction, transformation, and loading
- **Reference Processor**: Analyzes statute text and identifies references
- **Resolver Factory**: Creates appropriate resolvers for different statute types

## Key Workflows

### Statute Browsing
1. User selects a node in the NavTree
2. TreeStateProvider updates selectedNodeId
3. StatuteContent component fetches content via StatuteService
4. Content is displayed with appropriate view based on node type

### Text Highlighting
1. User selects text in StatuteContent
2. HighlightPicker shows color options
3. On selection, highlight is stored via HighlightService
4. Highlighted text is rendered with appropriate styles

### Reference Processing
1. Python scripts analyze statute text for references to other sections
2. References are stored in MongoDB
3. When viewing statute text, references are rendered as links
4. Clicking a reference navigates to the referenced statute

### Search
1. User enters search terms in SearchInput
2. SearchService queries MongoDB for matching text
3. Results displayed in SearchResultsList
4. Clicking a result navigates to the statute node