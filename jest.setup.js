import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util';

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock next/auth
jest.mock('next-auth/react', () => ({
  useSession: () => ({ data: null, status: 'unauthenticated' }),
}));

// Mock next/server
jest.mock('next/server', () => ({
  NextResponse: {
    json: (body, init) => ({
      status: init?.status || 200,
      headers: new Map(Object.entries(init?.headers || {})),
      json: async () => body
    })
  }
}));

// Mock mongodb
jest.mock('@/lib/mongodb', () => ({
  mongoClient: {
    db: () => ({
      collection: () => ({})
    })
  }
}));

// Mock window.getSelection
window.getSelection = () => ({
  removeAllRanges: () => {},
  addRange: () => {},
  getRangeAt: () => ({}),
  toString: () => '',
});
