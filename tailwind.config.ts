import type { Config } from "tailwindcss";
import tailwindScrollbar from 'tailwind-scrollbar';

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/features/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  safelist: [
    // Base color classes
    'bg-yellow-200',
    'bg-pink-200',
    'bg-blue-200',
    'bg-orange-200',
    'bg-green-200',
    // Hover color classes
    'hover:bg-yellow-300',
    'hover:bg-pink-300',
    'hover:bg-blue-300',
    'hover:bg-orange-300',
    'hover:bg-green-300',
    // Ring classes
    'ring-2',
    'ring-yellow-400',
    'ring-pink-400',
    'ring-blue-400',
    'ring-orange-400',
    'ring-green-400',
    // Group hover classes
    'group-hover:ring-2',
    'group-hover:ring-yellow-400',
    'group-hover:ring-pink-400',
    'group-hover:ring-blue-400',
    'group-hover:ring-orange-400',
    'group-hover:ring-green-400',
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
    },
  },
  plugins: [
    tailwindScrollbar({ nocompatible: true }),
  ],
} satisfies Config;
