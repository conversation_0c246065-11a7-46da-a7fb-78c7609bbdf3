# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.swc
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
enhance_index.log
*.log

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# data
/data
/public/data

# python
__pycache__/
venv/
.pytest_cache/
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz
.claude
<PERSON>U<PERSON>.local.md
